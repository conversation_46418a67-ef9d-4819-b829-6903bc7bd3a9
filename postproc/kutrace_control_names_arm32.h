// Names for syscall, etc. in dclab_tracing 
// dick sites 2019.03.13
// These are from linux-4.19 Arm32 linux/arch/arm/tools/syscall.tbl Others will vary.
//

#ifndef __KUTRACE_CONTROL_NAMES_H__
#define __KUTRACE_CONTROL_NAMES_H__

#include "kutrace_lib.h"

static const char* CpuFamilyModelManuf = "7 3 ARMv7";

static const NumNamePair PidNames[] = {
  {0, "-idle-"},
  {-1, NULL},		// Must be last
};

static const NumNamePair Syscall64Names[] = {
  {511, "-sched-"},	// Fake last syscall. Indicates where __schedule runs
  {-1, NULL},		// Must be last
};

static const NumNamePair Syscall32Names[] = {
  {0, "restart_syscall"},
  {1, "exit"},
  {2, "fork"},
  {3, "read"},
  {4, "write"},
  {5, "open"},
  {6, "close"},
  {8, "creat"},
  {9, "link"},
  {10, "unlink"},
  {11, "execve"},
  {12, "chdir"},
  {14, "mknod"},
  {15, "chmod"},
  {16, "lchown"},
  {19, "lseek"},
  {20, "getpid"},
  {21, "mount"},
  {23, "setuid"},
  {24, "getuid"},
  {26, "ptrace"},
  {29, "pause"},
  {33, "access"},
  {34, "nice"},
  {36, "sync"},
  {37, "kill"},
  {38, "rename"},
  {39, "mkdir"},
  {40, "rmdir"},
  {41, "dup"},
  {42, "pipe"},
  {43, "times"},
  {45, "brk"},
  {46, "setgid"},
  {47, "getgid"},
  {49, "geteuid"},
  {50, "getegid"},
  {51, "acct"},
  {52, "umount2"},
  {54, "ioctl"},
  {55, "fcntl"},
  {57, "setpgid"},
  {60, "umask"},
  {61, "chroot"},
  {62, "ustat"},
  {63, "dup2"},
  {64, "getppid"},
  {65, "getpgrp"},
  {66, "setsid"},
  {67, "sigaction"},
  {70, "setreuid"},
  {71, "setregid"},
  {72, "sigsuspend"},
  {73, "sigpending"},
  {74, "sethostname"},
  {75, "setrlimit"},
  {77, "getrusage"},
  {78, "gettimeofday"},
  {79, "settimeofday"},
  {80, "getgroups"},
  {81, "setgroups"},
  {83, "symlink"},
  {85, "readlink"},
  {86, "uselib"},
  {87, "swapon"},
  {88, "reboot"},
  {91, "munmap"},
  {92, "truncate"},
  {93, "ftruncate"},
  {94, "fchmod"},
  {95, "fchown"},
  {96, "getpriority"},
  {97, "setpriority"},
  {99, "statfs"},
  {100, "fstatfs"},
  {103, "syslog"},
  {104, "setitimer"},
  {105, "getitimer"},
  {106, "stat"},
  {107, "lstat"},
  {108, "fstat"},
  {111, "vhangup"},
  {114, "wait4"},
  {115, "swapoff"},
  {116, "sysinfo"},
  {118, "fsync"},
  {119, "sigreturn"},
  {120, "clone"},
  {121, "setdomainname"},
  {122, "uname"},
  {124, "adjtimex"},
  {125, "mprotect"},
  {126, "sigprocmask"},
  {128, "init_module"},
  {129, "delete_module"},
  {131, "quotactl"},
  {132, "getpgid"},
  {133, "fchdir"},
  {134, "bdflush"},
  {135, "sysfs"},
  {136, "personality"},
  {138, "setfsuid"},
  {139, "setfsgid"},
  {140, "_llseek"},
  {141, "getdents"},
  {142, "_newselect"},
  {143, "flock"},
  {144, "msync"},
  {145, "readv"},
  {146, "writev"},
  {147, "getsid"},
  {148, "fdatasync"},
  {149, "_sysctl"},
  {150, "mlock"},
  {151, "munlock"},
  {152, "mlockall"},
  {153, "munlockall"},
  {154, "sched_setparam"},
  {155, "sched_getparam"},
  {156, "sched_setscheduler"},
  {157, "sched_getscheduler"},
  {158, "sched_yield"},
  {159, "sched_get_priority_max"},
  {160, "sched_get_priority_min"},
  {161, "sched_rr_get_interval"},
  {162, "nanosleep"},
  {163, "mremap"},
  {164, "setresuid"},
  {165, "getresuid"},
  {168, "poll"},
  {169, ""},
  {170, "setresgid"},
  {171, "getresgid"},
  {172, "prctl"},
  {173, "rt_sigreturn"},
  {174, "rt_sigaction"},
  {175, "rt_sigprocmask"},
  {176, "rt_sigpending"},
  {177, "rt_sigtimedwait"},
  {178, "rt_sigqueueinfo"},
  {179, "rt_sigsuspend"},
  {180, "pread64"},
  {181, "pwrite64"},
  {182, "chown"},
  {183, "getcwd"},
  {184, "capget"},
  {185, "capset"},
  {186, "sigaltstack"},
  {187, "sendfile"},
  {190, "vfork"},
  {191, "ugetrlimit"},
  {192, "mmap2"},
  {193, "truncate64"},
  {194, "ftruncate64"},
  {195, "stat64"},
  {196, "lstat64"},
  {197, "fstat64"},
  {198, "lchown32"},
  {199, "getuid32"},
  {200, "getgid32"},
  {201, "geteuid32"},
  {202, "getegid32"},
  {203, "setreuid32"},
  {204, "setregid32"},
  {205, "getgroups32"},
  {206, "setgroups32"},
  {207, "fchown32"},
  {208, "setresuid32"},
  {209, "getresuid32"},
  {210, "setresgid32"},
  {211, "getresgid32"},
  {212, "chown32"},
  {213, "setuid32"},
  {214, "setgid32"},
  {215, "setfsuid32"},
  {216, "setfsgid32"},
  {217, "getdents64"},
  {218, "pivot_root"},
  {219, "mincore"},
  {220, "madvise"},
  {221, "fcntl64"},
  {224, "gettid"},
  {225, "readahead"},
  {226, "setxattr"},
  {227, "lsetxattr"},
  {228, "fsetxattr"},
  {229, "getxattr"},
  {230, "lgetxattr"},
  {231, "fgetxattr"},
  {232, "listxattr"},
  {233, "llistxattr"},
  {234, "flistxattr"},
  {235, "removexattr"},
  {236, "lremovexattr"},
  {237, "fremovexattr"},
  {238, "tkill"},
  {239, "sendfile64"},
  {240, "futex"},
  {241, "sched_setaffinity"},
  {242, "sched_getaffinity"},
  {243, "io_setup"},
  {244, "io_destroy"},
  {245, "io_getevents"},
  {246, "io_submit"},
  {247, "io_cancel"},
  {248, "exit_group"},
  {249, "lookup_dcookie"},
  {250, "epoll_create"},
  {251, "epoll_ctl"},
  {252, "epoll_wait"},
  {253, "remap_file_pages"},
  {256, "set_tid_address"},
  {257, "timer_create"},
  {258, "timer_settime"},
  {259, "timer_gettime"},
  {260, "timer_getoverrun"},
  {261, "timer_delete"},
  {262, "clock_settime"},
  {263, "clock_gettime"},
  {264, "clock_getres"},
  {265, "clock_nanosleep"},
  {266, "statfs64"},
  {267, "fstatfs64"},
  {268, "tgkill"},
  {269, "utimes"},
  {270, "arm_fadvise64_64"},
  {271, "pciconfig_iobase"},
  {272, "pciconfig_read"},
  {273, "pciconfig_write"},
  {274, "mq_open"},
  {275, "mq_unlink"},
  {276, "mq_timedsend"},
  {277, "mq_timedreceive"},
  {278, "mq_notify"},
  {279, "mq_getsetattr"},
  {280, "waitid"},
  {281, "socket"},
  {282, "bind"},
  {283, "connect"},
  {284, "listen"},
  {285, "accept"},
  {286, "getsockname"},
  {287, "getpeername"},
  {288, "socketpair"},
  {289, "send"},
  {290, "sendto"},
  {291, "recv"},
  {292, "recvfrom"},
  {293, "shutdown"},
  {294, "setsockopt"},
  {295, "getsockopt"},
  {296, "sendmsg"},
  {297, "recvmsg"},
  {298, "semop"},
  {299, "semget"},
  {300, "semctl"},
  {301, "msgsnd"},
  {302, "msgrcv"},
  {303, "msgget"},
  {304, "msgctl"},
  {305, "shmat"},
  {306, "shmdt"},
  {307, "shmget"},
  {308, "shmctl"},
  {309, "add_key"},
  {310, "request_key"},
  {311, "keyctl"},
  {312, "semtimedop"},
  {313, ""},
  {314, "ioprio_set"},
  {315, "ioprio_get"},
  {316, "inotify_init"},
  {317, "inotify_add_watch"},
  {318, "inotify_rm_watch"},
  {319, "mbind"},
  {320, "get_mempolicy"},
  {321, "set_mempolicy"},
  {322, "openat"},
  {323, "mkdirat"},
  {324, "mknodat"},
  {325, "fchownat"},
  {326, "futimesat"},
  {327, "fstatat64"},
  {328, "unlinkat"},
  {329, "renameat"},
  {330, "linkat"},
  {331, "symlinkat"},
  {332, "readlinkat"},
  {333, "fchmodat"},
  {334, "faccessat"},
  {335, "pselect6"},
  {336, "ppoll"},
  {337, "unshare"},
  {338, "set_robust_list"},
  {339, "get_robust_list"},
  {340, "splice"},
  {341, "arm_sync_file_range"},
  {342, "tee"},
  {343, "vmsplice"},
  {344, "move_pages"},
  {345, "getcpu"},
  {346, "epoll_pwait"},
  {347, "kexec_load"},
  {348, "utimensat"},
  {349, "signalfd"},
  {350, "timerfd_create"},
  {351, "eventfd"},
  {352, "fallocate"},
  {353, "timerfd_settime"},
  {354, "timerfd_gettime"},
  {355, "signalfd4"},
  {356, "eventfd2"},
  {357, "epoll_create1"},
  {358, "dup3"},
  {359, "pipe2"},
  {360, "inotify_init1"},
  {361, "preadv"},
  {362, "pwritev"},
  {363, "rt_tgsigqueueinfo"},
  {364, "perf_event_open"},
  {365, "recvmmsg"},
  {366, "accept4"},
  {367, "fanotify_init"},
  {368, "fanotify_mark"},
  {369, "prlimit64"},
  {370, "name_to_handle_at"},
  {371, "open_by_handle_at"},
  {372, "clock_adjtime"},
  {373, "syncfs"},
  {374, "sendmmsg"},
  {375, "setns"},
  {376, "process_vm_readv"},
  {377, "process_vm_writev"},
  {378, "kcmp"},
  {379, "finit_module"},
  {380, "sched_setattr"},
  {381, "sched_getattr"},
  {382, "renameat2"},
  {383, "seccomp"},
  {384, "getrandom"},
  {385, "memfd_create"},
  {386, "bpf"},
  {387, "execveat"},
  {388, "userfaultfd"},
  {389, "membarrier"},
  {390, "mlock2"},
  {391, "copy_file_range"},
  {392, "preadv2"},
  {393, "pwritev2"},
  {394, "pkey_mprotect"},
  {395, "pkey_alloc"},
  {396, "pkey_free"},
  {397, "statx"},
  {398, "rseq"},
  {399, "io_pgetevents"},

  {511, "-sched-"},	// Fake last syscall. Indicates where __schedule runs
  {-1, NULL},		// Must be last
};

// Based on arch/x86/include/asm/x86/irq_vectors.h
//    2017: arch/x86/include/asm/irq_vectors.h
//    2019: arch/x86/include/asm/irq_vectors.h
/*
 *  Vectors   0 ...  31 : system traps and exceptions - hardcoded events
 *  Vectors  32 ... 127 : device interrupts
 *  Vector  128         : legacy int80 syscall interface
 *  Vectors 129 ... INVALIDATE_TLB_VECTOR_START-1 except 204 : device interrupts
 *  Vectors INVALIDATE_TLB_VECTOR_START ... 255 : special interrupts
 */

// Based on at /proc/interrupts on RPi4

// IPI0:          0          0          0          0  CPU wakeup interrupts
// IPI1:          0          0          0          0  Timer broadcast interrupts
// IPI2:      75435     302688     400819     234894  Rescheduling interrupts
// IPI3:       3239      42860      27741      19549  Function call interrupts
// IPI4:          0          0          0          0  CPU stop interrupts
// IPI5:     151666     210389     250357     200351  IRQ work interrupts
// IPI6:          0          0          0          0  completion interrupts


static const NumNamePair IrqNames[] = {
  // Raspberry Pi-4B
  { 0, "wakeup"},
  { 1, "timer"},
  { 2, "reschedule"},
  { 3, "call_func"},
  { 4, "cpu_stop"},
  { 5, "irq_work"},
  { 6, "completion"},
  { 7, "backtrace"},

  {17, "local_timer"},		// timer
  {18, "local_timer"},		// timer
  {23, "dma_irq"},
  {31, "fe00b880.mailbox"},
  {34, "uart-pl011"},
  {36, "brcmstb_thermal"},
  {37, "mmc"},			// micro SD card
  {38, "vc4_videos"},
  {43, "v3d"},
  {45, "eth0"},
  {46, "eth0"},
  {52, "VCHIQ_doorbell"},
  {53, "PCIe"},
  {54, "xhci_hcd"},

 // {255, "spurious_apic"},
  {255, "BH"},			// bottom half of an interrupt handler
  {254, "error_apic_ipi"},
  {253, "reschedule_ipi"},
  {252, "call_func_ipi"},
  {251, "call_func1_ipi"},
  {250, "thermal_apic_ipi"},
  {249, "threshold_apic_ipi"},
  {248, "reboot_ipi"},
  {247, "x86_platform_ipi"},
  {246, "irq_work_ipi"},
  {245, "uv_bau_message"},
  {244, "deferred_error"},
  {243, "hypervisor_callback"},
  {242, "posted_intr"},
  {241, "posted_intr_wakeup"},
  {240, "posted_intr_nested"},
  {239, "managed_irq_shutdown"},
  {238, "hyperv_reenlighten"},
  {237, "hyperv_stimer0"},
  {236, "local_timer_vector"},	// event 0x05ec, decimal 1516 4.19 x86

  {13, "fpu_irq"},

  {-1, NULL},		// Must be last
};

// Export this to raw2event.cc, using above value
static const int kTIMER_IRQ_EVENT = 0x05ec;

// Bottom half BH vectors, from include/linux/interrupt.h
static const NumNamePair SoftIrqNames[] = {
  {0, "HI_SOFTIRQ"},
  {1, "TIMER_SOFTIRQ"},
  {2, "NET_TX_SOFTIRQ"},
  {3, "NET_RX_SOFTIRQ"},
  {4, "BLOCK_SOFTIRQ"},
  {5, "IRQ_POLL_SOFTIRQ"},
  {6, "TASKLET_SOFTIRQ"},
  {7, "SCHED_SOFTIRQ"},
  {8, "HRTIMER_SOFTIRQ"},
  {9, "RCU_SOFTIRQ"},

  {-1, NULL},		// Must be last
};

static const NumNamePair TrapNames[] = {
  {0, "Divide-by-zero"},
  {1, "Debug"},
  {2, "Non-maskable_Interrupt"},
  {3, "Breakpoint"},
  {4, "Overflow"},
  {5, "Bound_Range_Exceeded"},
  {6, "Invalid_Opcode"},
  {7, "device_not_available"},
  {8, "Double_Fault"},
  {9, "Coprocessor_Segment_Overrun"},
  {10, "Invalid_TSS"},
  {11, "Segment_Not_Present"},
  {12, "Stack_Segment_Fault"},
  {13, "General_Protection_Fault"},
  {14, "page_fault"},
  {15, "Spurious_Interrupt"},
  {16, "x87_Floating-Point_Exception"},
  {17, "Alignment_Check"},
  {18, "Machine_Check"},
  {19, "SIMD_Floating-Point_Exception"},
  {32, "IRET_Exception"},

  {-1, NULL},		// Must be last
};

// This is just the base set. More could be added later
// see linux-4.19.19/tools/include/uapi/asm-generic/errno-base.h
//  linux-4.19.19/include/linux/errno.h
//  linux-4.19.19/include/uapi/linux/errno.h
//  linux-4.19.19/include/uapi/asm-generic/errno.h

static const NumNamePair ErrnoNames[] = {
  {1, "EPERM"},
  {2, "ENOENT"},
  {3, "ESRCH"},
  {4, "EINTR"},
  {5, "EIO"},
  {6, "ENXIO"},
  {7, "E2BIG"},
  {8, "ENOEXEC"},
  {9, "EBADF"},
  {10, "ECHILD"},
  {11, "EAGAIN"},
  {12, "ENOMEM"},
  {13, "EACCES"},
  {14, "EFAULT"},
  {15, "ENOTBLK"},
  {16, "EBUSY"},
  {17, "EEXIST"},
  {18, "EXDEV"},
  {19, "ENODEV"},
  {20, "ENOTDIR"},
  {21, "EISDIR"},
  {22, "EINVAL"},
  {23, "ENFILE"},
  {24, "EMFILE"},
  {25, "ENOTTY"},
  {26, "ETXTBSY"},
  {27, "EFBIG"},
  {28, "ENOSPC"},
  {29, "ESPIPE"},
  {30, "EROFS"},
  {31, "EMLINK"},
  {32, "EPIPE"},
  {33, "EDOM"},
  {34, "ERANGE"},

  {-1, NULL},		// Must be last

};
#endif	// __KUTRACE_CONTROL_NAMES_H__


