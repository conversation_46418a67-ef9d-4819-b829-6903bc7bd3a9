// Names for syscall, etc. in kutrace for ARM Neoverse N2
// Based on ARM64 Linux system calls and events
//

#ifndef __KUTRACE_CONTROL_NAMES_ARMN2_H__
#define __KUTRACE_CONTROL_NAMES_ARMN2_H__

// ARM N2 specific event names and system call mappings
// These are based on ARM64 Linux kernel system calls and events

// System call numbers for ARM64
#define ARM64_SYS_READ                    0
#define ARM64_SYS_WRITE                   1
#define ARM64_SYS_OPEN                    2
#define ARM64_SYS_CLOSE                   3
#define ARM64_SYS_STAT                    4
#define ARM64_SYS_FSTAT                   5
#define ARM64_SYS_LSTAT                   6
#define ARM64_SYS_POLL                    7
#define ARM64_SYS_LSEEK                   8
#define ARM64_SYS_MMAP                    9
#define ARM64_SYS_MPROTECT               10
#define ARM64_SYS_MUNMAP                 11
#define ARM64_SYS_BRK                    12
#define ARM64_SYS_RT_SIGACTION           13
#define ARM64_SYS_RT_SIGPROCMASK         14
#define ARM64_SYS_RT_SIGRETURN           15
#define ARM64_SYS_IOCTL                  16
#define ARM64_SYS_PREAD64                17
#define ARM64_SYS_PWRITE64               18
#define ARM64_SYS_READV                  19
#define ARM64_SYS_WRITEV                 20
#define ARM64_SYS_ACCESS                 21
#define ARM64_SYS_PIPE                   22
#define ARM64_SYS_SELECT                 23
#define ARM64_SYS_SCHED_YIELD            24
#define ARM64_SYS_MREMAP                 25
#define ARM64_SYS_MSYNC                  26
#define ARM64_SYS_MINCORE                27
#define ARM64_SYS_MADVISE                28
#define ARM64_SYS_SHMGET                 29
#define ARM64_SYS_SHMAT                  30
#define ARM64_SYS_SHMCTL                 31
#define ARM64_SYS_DUP                    32
#define ARM64_SYS_DUP2                   33
#define ARM64_SYS_PAUSE                  34
#define ARM64_SYS_NANOSLEEP             35
#define ARM64_SYS_GETITIMER             36
#define ARM64_SYS_ALARM                  37
#define ARM64_SYS_SETITIMER             38
#define ARM64_SYS_GETPID                 39
#define ARM64_SYS_SENDFILE               40
#define ARM64_SYS_SOCKET                 41
#define ARM64_SYS_CONNECT                42
#define ARM64_SYS_ACCEPT                 43
#define ARM64_SYS_SENDTO                 44
#define ARM64_SYS_RECVFROM               45
#define ARM64_SYS_SENDMSG                46
#define ARM64_SYS_RECVMSG                47
#define ARM64_SYS_SHUTDOWN               48
#define ARM64_SYS_BIND                   49
#define ARM64_SYS_LISTEN                 50
#define ARM64_SYS_GETSOCKNAME            51
#define ARM64_SYS_GETPEERNAME            52
#define ARM64_SYS_SOCKETPAIR             53
#define ARM64_SYS_SETSOCKOPT             54
#define ARM64_SYS_GETSOCKOPT             55
#define ARM64_SYS_CLONE                  56
#define ARM64_SYS_FORK                   57
#define ARM64_SYS_VFORK                  58
#define ARM64_SYS_EXECVE                 59
#define ARM64_SYS_EXIT                   60
#define ARM64_SYS_WAIT4                  61
#define ARM64_SYS_KILL                   62
#define ARM64_SYS_UNAME                  63
#define ARM64_SYS_SEMGET                 64
#define ARM64_SYS_SEMOP                  65
#define ARM64_SYS_SEMCTL                 66
#define ARM64_SYS_SHMDT                  67
#define ARM64_SYS_MSGGET                 68
#define ARM64_SYS_MSGSND                 69
#define ARM64_SYS_MSGRCV                 70
#define ARM64_SYS_MSGCTL                 71
#define ARM64_SYS_FCNTL                  72
#define ARM64_SYS_FLOCK                  73
#define ARM64_SYS_FSYNC                  74
#define ARM64_SYS_FDATASYNC              75
#define ARM64_SYS_TRUNCATE               76
#define ARM64_SYS_FTRUNCATE              77
#define ARM64_SYS_GETDENTS               78
#define ARM64_SYS_GETCWD                 79
#define ARM64_SYS_CHDIR                  80
#define ARM64_SYS_FCHDIR                 81
#define ARM64_SYS_RENAME                 82
#define ARM64_SYS_MKDIR                  83
#define ARM64_SYS_RMDIR                  84
#define ARM64_SYS_CREAT                  85
#define ARM64_SYS_LINK                   86
#define ARM64_SYS_UNLINK                 87
#define ARM64_SYS_SYMLINK                88
#define ARM64_SYS_READLINK               89
#define ARM64_SYS_CHMOD                  90
#define ARM64_SYS_FCHMOD                 91
#define ARM64_SYS_CHOWN                  92
#define ARM64_SYS_FCHOWN                 93
#define ARM64_SYS_LCHOWN                 94
#define ARM64_SYS_UMASK                  95
#define ARM64_SYS_GETTIMEOFDAY           96
#define ARM64_SYS_GETRLIMIT              97
#define ARM64_SYS_GETRUSAGE              98
#define ARM64_SYS_SYSINFO                99
#define ARM64_SYS_TIMES                  100
#define ARM64_SYS_PTRACE                 101
#define ARM64_SYS_GETUID                 102
#define ARM64_SYS_SYSLOG                 103
#define ARM64_SYS_GETGID                 104
#define ARM64_SYS_SETUID                 105
#define ARM64_SYS_SETGID                 106
#define ARM64_SYS_GETEUID                107
#define ARM64_SYS_GETEGID                108
#define ARM64_SYS_SETPGID                109
#define ARM64_SYS_GETPPID                110
#define ARM64_SYS_GETPGRP                111
#define ARM64_SYS_SETSID                 112
#define ARM64_SYS_SETREUID               113
#define ARM64_SYS_SETREGID               114
#define ARM64_SYS_GETGROUPS              115
#define ARM64_SYS_SETGROUPS              116
#define ARM64_SYS_SETRESUID              117
#define ARM64_SYS_GETRESUID              118
#define ARM64_SYS_SETRESGID              119
#define ARM64_SYS_GETRESGID              120
#define ARM64_SYS_GETPGID                121
#define ARM64_SYS_SETFSUID               122
#define ARM64_SYS_SETFSGID               123
#define ARM64_SYS_GETSID                 124
#define ARM64_SYS_CAPGET                 125
#define ARM64_SYS_CAPSET                 126
#define ARM64_SYS_RT_SIGPENDING          127
#define ARM64_SYS_RT_SIGTIMEDWAIT        128
#define ARM64_SYS_RT_SIGQUEUEINFO        129
#define ARM64_SYS_RT_SIGSUSPEND          130
#define ARM64_SYS_SIGALTSTACK            131
#define ARM64_SYS_UTIME                  132
#define ARM64_SYS_MKNOD                  133
#define ARM64_SYS_USELIB                 134
#define ARM64_SYS_PERSONALITY            135
#define ARM64_SYS_USTAT                  136
#define ARM64_SYS_STATFS                 137
#define ARM64_SYS_FSTATFS                138
#define ARM64_SYS_SYSFS                  139
#define ARM64_SYS_GETPRIORITY            140
#define ARM64_SYS_SETPRIORITY            141
#define ARM64_SYS_SCHED_SETPARAM         142
#define ARM64_SYS_SCHED_GETPARAM         143
#define ARM64_SYS_SCHED_SETSCHEDULER     144
#define ARM64_SYS_SCHED_GETSCHEDULER     145
#define ARM64_SYS_SCHED_GET_PRIORITY_MAX 146
#define ARM64_SYS_SCHED_GET_PRIORITY_MIN 147
#define ARM64_SYS_SCHED_RR_GET_INTERVAL  148
#define ARM64_SYS_MLOCK                  149
#define ARM64_SYS_MUNLOCK                150
#define ARM64_SYS_MLOCKALL               151
#define ARM64_SYS_MUNLOCKALL             152
#define ARM64_SYS_VHANGUP                153
#define ARM64_SYS_MODIFY_LDT             154
#define ARM64_SYS_PIVOT_ROOT             155
#define ARM64_SYS__SYSCTL                156
#define ARM64_SYS_PRCTL                  157
#define ARM64_SYS_ARCH_PRCTL             158
#define ARM64_SYS_ADJTIMEX               159
#define ARM64_SYS_SETRLIMIT              160
#define ARM64_SYS_CHROOT                 161
#define ARM64_SYS_SYNC                   162
#define ARM64_SYS_ACCT                   163
#define ARM64_SYS_SETTIMEOFDAY           164
#define ARM64_SYS_MOUNT                  165
#define ARM64_SYS_UMOUNT2                166
#define ARM64_SYS_SWAPON                 167
#define ARM64_SYS_SWAPOFF                168
#define ARM64_SYS_REBOOT                 169
#define ARM64_SYS_SETHOSTNAME            170
#define ARM64_SYS_SETDOMAINNAME          171
#define ARM64_SYS_IOPL                   172
#define ARM64_SYS_IOPERM                 173
#define ARM64_SYS_CREATE_MODULE          174
#define ARM64_SYS_INIT_MODULE            175
#define ARM64_SYS_DELETE_MODULE          176
#define ARM64_SYS_GET_KERNEL_SYMS        177
#define ARM64_SYS_QUERY_MODULE           178
#define ARM64_SYS_QUOTACTL               179
#define ARM64_SYS_NFSSERVCTL             180
#define ARM64_SYS_GETPMSG                181
#define ARM64_SYS_PUTPMSG                182
#define ARM64_SYS_AFS_SYSCALL            183
#define ARM64_SYS_TUXCALL                184
#define ARM64_SYS_SECURITY               185
#define ARM64_SYS_GETTID                 186
#define ARM64_SYS_READAHEAD              187
#define ARM64_SYS_SETXATTR               188
#define ARM64_SYS_LSETXATTR              189
#define ARM64_SYS_FSETXATTR              190
#define ARM64_SYS_GETXATTR               191
#define ARM64_SYS_LGETXATTR              192
#define ARM64_SYS_FGETXATTR              193
#define ARM64_SYS_LISTXATTR              194
#define ARM64_SYS_LLISTXATTR             195
#define ARM64_SYS_FLISTXATTR             196
#define ARM64_SYS_REMOVEXATTR            197
#define ARM64_SYS_LREMOVEXATTR           198
#define ARM64_SYS_FREMOVEXATTR           199
#define ARM64_SYS_TKILL                  200
#define ARM64_SYS_TIME                   201
#define ARM64_SYS_FUTEX                  202
#define ARM64_SYS_SCHED_SETAFFINITY      203
#define ARM64_SYS_SCHED_GETAFFINITY      204
#define ARM64_SYS_SET_THREAD_AREA        205
#define ARM64_SYS_IO_SETUP               206
#define ARM64_SYS_IO_DESTROY             207
#define ARM64_SYS_IO_GETEVENTS           208
#define ARM64_SYS_IO_SUBMIT              209
#define ARM64_SYS_IO_CANCEL              210
#define ARM64_SYS_GET_THREAD_AREA        211
#define ARM64_SYS_LOOKUP_DCOOKIE         212
#define ARM64_SYS_EPOLL_CREATE           213
#define ARM64_SYS_EPOLL_CTL_OLD          214
#define ARM64_SYS_EPOLL_WAIT_OLD         215
#define ARM64_SYS_REMAP_FILE_PAGES       216
#define ARM64_SYS_GETDENTS64             217
#define ARM64_SYS_SET_TID_ADDRESS        218
#define ARM64_SYS_RESTART_SYSCALL        219
#define ARM64_SYS_SEMTIMEDOP             220
#define ARM64_SYS_FADVISE64              221
#define ARM64_SYS_TIMER_CREATE           222
#define ARM64_SYS_TIMER_SETTIME          223
#define ARM64_SYS_TIMER_GETTIME          224
#define ARM64_SYS_TIMER_GETOVERRUN       225
#define ARM64_SYS_TIMER_DELETE           226
#define ARM64_SYS_CLOCK_SETTIME          227
#define ARM64_SYS_CLOCK_GETTIME          228
#define ARM64_SYS_CLOCK_GETRES           229
#define ARM64_SYS_CLOCK_NANOSLEEP        230
#define ARM64_SYS_EXIT_GROUP             231
#define ARM64_SYS_EPOLL_WAIT             232
#define ARM64_SYS_EPOLL_CTL              233
#define ARM64_SYS_TGKILL                 234
#define ARM64_SYS_UTIMES                 235
#define ARM64_SYS_VSERVER                236
#define ARM64_SYS_MBIND                  237
#define ARM64_SYS_SET_MEMPOLICY          238
#define ARM64_SYS_GET_MEMPOLICY          239
#define ARM64_SYS_MQ_OPEN                240
#define ARM64_SYS_MQ_UNLINK              241
#define ARM64_SYS_MQ_TIMEDSEND           242
#define ARM64_SYS_MQ_TIMEDRECEIVE        243
#define ARM64_SYS_MQ_NOTIFY              244
#define ARM64_SYS_MQ_GETSETATTR          245
#define ARM64_SYS_KEXEC_LOAD             246
#define ARM64_SYS_WAITID                 247
#define ARM64_SYS_ADD_KEY                248
#define ARM64_SYS_REQUEST_KEY            249
#define ARM64_SYS_KEYCTL                 250
#define ARM64_SYS_IOPRIO_SET             251
#define ARM64_SYS_IOPRIO_GET             252
#define ARM64_SYS_INOTIFY_INIT           253
#define ARM64_SYS_INOTIFY_ADD_WATCH      254
#define ARM64_SYS_INOTIFY_RM_WATCH       255
#define ARM64_SYS_MIGRATE_PAGES          256
#define ARM64_SYS_OPENAT                 257
#define ARM64_SYS_MKDIRAT                258
#define ARM64_SYS_MKNODAT                259
#define ARM64_SYS_FCHOWNAT               260
#define ARM64_SYS_FUTIMESAT              261
#define ARM64_SYS_NEWFSTATAT             262
#define ARM64_SYS_UNLINKAT               263
#define ARM64_SYS_RENAMEAT               264
#define ARM64_SYS_LINKAT                 265
#define ARM64_SYS_SYMLINKAT              266
#define ARM64_SYS_READLINKAT             267
#define ARM64_SYS_FCHMODAT               268
#define ARM64_SYS_FACCESSAT              269
#define ARM64_SYS_PSELECT6               270
#define ARM64_SYS_PPOLL                  271
#define ARM64_SYS_UNSHARE                272
#define ARM64_SYS_SET_ROBUST_LIST        273
#define ARM64_SYS_GET_ROBUST_LIST        274
#define ARM64_SYS_SPLICE                 275
#define ARM64_SYS_TEE                    276
#define ARM64_SYS_SYNC_FILE_RANGE        277
#define ARM64_SYS_VMSPLICE               278
#define ARM64_SYS_MOVE_PAGES             279
#define ARM64_SYS_UTIMENSAT              280
#define ARM64_SYS_EPOLL_PWAIT            281
#define ARM64_SYS_SIGNALFD               282
#define ARM64_SYS_TIMERFD_CREATE         283
#define ARM64_SYS_EVENTFD                284
#define ARM64_SYS_FALLOCATE              285
#define ARM64_SYS_TIMERFD_SETTIME        286
#define ARM64_SYS_TIMERFD_GETTIME        287
#define ARM64_SYS_ACCEPT4                288
#define ARM64_SYS_SIGNALFD4              289
#define ARM64_SYS_EVENTFD2               290
#define ARM64_SYS_EPOLL_CREATE1          291
#define ARM64_SYS_DUP3                   292
#define ARM64_SYS_PIPE2                  293
#define ARM64_SYS_INOTIFY_INIT1          294
#define ARM64_SYS_PREADV                 295
#define ARM64_SYS_PWRITEV                296
#define ARM64_SYS_RT_TGSIGQUEUEINFO      297
#define ARM64_SYS_PERF_EVENT_OPEN        298
#define ARM64_SYS_RECVMMSG               299
#define ARM64_SYS_FANOTIFY_INIT          300
#define ARM64_SYS_FANOTIFY_MARK          301
#define ARM64_SYS_PRLIMIT64              302
#define ARM64_SYS_NAME_TO_HANDLE_AT      303
#define ARM64_SYS_OPEN_BY_HANDLE_AT      304
#define ARM64_SYS_CLOCK_ADJTIME          305
#define ARM64_SYS_SYNCFS                 306
#define ARM64_SYS_SENDMMSG               307
#define ARM64_SYS_SETNS                  308
#define ARM64_SYS_GETCPU                 309
#define ARM64_SYS_PROCESS_VM_READV       310
#define ARM64_SYS_PROCESS_VM_WRITEV      311
#define ARM64_SYS_KCMP                   312
#define ARM64_SYS_FINIT_MODULE           313
#define ARM64_SYS_SCHED_SETATTR          314
#define ARM64_SYS_SCHED_GETATTR          315
#define ARM64_SYS_RENAMEAT2              316
#define ARM64_SYS_SECCOMP                317
#define ARM64_SYS_GETRANDOM              318
#define ARM64_SYS_MEMFD_CREATE           319
#define ARM64_SYS_KEXEC_FILE_LOAD        320
#define ARM64_SYS_BPF                    321
#define ARM64_SYS_EXECVEAT               322
#define ARM64_SYS_USERFAULTFD            323
#define ARM64_SYS_MEMBARRIER             324
#define ARM64_SYS_MLOCK2                 325
#define ARM64_SYS_COPY_FILE_RANGE        326
#define ARM64_SYS_PREADV2                327
#define ARM64_SYS_PWRITEV2               328
#define ARM64_SYS_PKEY_MPROTECT          329
#define ARM64_SYS_PKEY_ALLOC             330
#define ARM64_SYS_PKEY_FREE              331
#define ARM64_SYS_STATX                  332
#define ARM64_SYS_IO_PGETEVENTS          333
#define ARM64_SYS_RSEQ                   334
#define ARM64_SYS_PIDFD_SEND_SIGNAL      335
#define ARM64_SYS_IO_URING_SETUP         336
#define ARM64_SYS_IO_URING_ENTER         337
#define ARM64_SYS_IO_URING_REGISTER      338
#define ARM64_SYS_OPEN_TREE              339
#define ARM64_SYS_MOVE_MOUNT             340
#define ARM64_SYS_FSOPEN                 341
#define ARM64_SYS_FSCONFIG               342
#define ARM64_SYS_FSMOUNT                343
#define ARM64_SYS_FSPICK                 344
#define ARM64_SYS_PIDFD_OPEN             345
#define ARM64_SYS_CLONE3                 346
#define ARM64_SYS_CLOSE_RANGE            347
#define ARM64_SYS_OPENAT2                348
#define ARM64_SYS_PIDFD_GETFD            349
#define ARM64_SYS_FACCESSAT2             350
#define ARM64_SYS_PROCESS_MADVISE        351
#define ARM64_SYS_EPOLL_PWAIT2           352
#define ARM64_SYS_MOUNT_SETATTR          353
#define ARM64_SYS_QUOTACTL_FD            354
#define ARM64_SYS_LANDLOCK_CREATE_RULESET 355
#define ARM64_SYS_LANDLOCK_ADD_RULE      356
#define ARM64_SYS_LANDLOCK_RESTRICT_SELF 357
#define ARM64_SYS_MEMFD_SECRET           358
#define ARM64_SYS_PROCESS_MRELEASE       359
#define ARM64_SYS_FUTEX_WAITV            360
#define ARM64_SYS_SET_MEMPOLICY_HOME_NODE 361
#define ARM64_SYS_CACHESTAT              362

// ARM N2 specific performance events
#define ARM_N2_EVENT_INST_RETIRED        0x08
#define ARM_N2_EVENT_CYCLES              0x11
#define ARM_N2_EVENT_L3_CACHE_MISS       0x2B
#define ARM_N2_EVENT_L3_CACHE_REFILL     0x2C
#define ARM_N2_EVENT_BRANCH_MISS         0x10
#define ARM_N2_EVENT_DTLB_MISS           0x35
#define ARM_N2_EVENT_ITLB_MISS           0x34

// ARM N2 specific trace events
#define ARM_N2_TRACE_IRQ_ENTRY           0x1000
#define ARM_N2_TRACE_IRQ_EXIT            0x1001
#define ARM_N2_TRACE_SCHED_SWITCH        0x1002
#define ARM_N2_TRACE_SCHED_WAKEUP        0x1003
#define ARM_N2_TRACE_SYSCALL_ENTRY       0x1004
#define ARM_N2_TRACE_SYSCALL_EXIT        0x1005
#define ARM_N2_TRACE_PAGE_FAULT          0x1006
#define ARM_N2_TRACE_CONTEXT_SWITCH      0x1007

#endif // __KUTRACE_CONTROL_NAMES_ARMN2_H__ 