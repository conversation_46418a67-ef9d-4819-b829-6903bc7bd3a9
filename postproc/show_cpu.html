<!DOCTYPE html>
<html>
<meta charset="UTF-8">
<!-- Copyright 2024 Richard <PERSON> -->
<!-- Add slanted overlay -->
<!-- Add LLC misses support -->
<!--   NOTE: Be sure to update script_edit_date -->
<!-- 2023.07.26 Add live mouse event tracking --> 
<!-- 2023.07.31 Shift-righclick Y-axis labels for highlight ignores numbers now --> 
<!-- 2024.05.29 Add mixed ipc/llc support --> 
<!-- 2024.06.04 Have mouseover display always show full data --> 
<!-- 2024.07.03 Add IPI amd monitor/mwait arcs --> 

<head>
 
<style> /* set the CSS */
.noselect {user-select: none;}

.verticalline {
  fill: none;
  stroke: #600090;	/* bluish magenta */
  stroke-width: 1.5px;
}
.verticaldashline {
  fill: none;
  stroke: #600090;	/* bluish magenta */
  stroke-width: 1.5px;
  stroke-dasharray: 2,5;
}
.verticalshortline {
  fill: none;
  stroke: #600090;	/* dark bluish magenta */
  stroke-width: 2px;
}

.rubberline {
  fill: none;
  stroke: #600090;
  stroke-width: 4px;
}

.markertext {
  text-anchor: left;
  font-family: sans-serif; 
  fill: #600090;
}

.rubbertext {
  text-anchor: middle;
  font-family: sans-serif; 
  fill: #600090;
}

.brackettext {
  text-anchor: middle;
  font-family: sans-serif; 
  fill: #000000;
}
 
.graphline {
  fill: none;
  stroke: steelblue;
  stroke-width: 2px;
}

</style>
 
 
<!-- Load the d3.js library -->    	
<!-- selfcontained0 -->
<script src="https://d3js.org/d3.v4.min.js"></script>

<script type="text/javascript">
<!-- selfcontained1 -->

// Screen layout:
//
// Given a browser window size, subdivide it into seven regions
//
// +------------------------------------------------------------------------+
// |            (0) UI controls (HTML)                                      |
// +========================================================================+
// |            (1) Title                                                   |
// +----+--------------------------------------------------------------+----+
// |(2) |                                                              |(4) |
// |Y-  |       (3) Main SVG drawing area                              |IPC |
// |axis|                                                              |    |
// |    |                                                              |    |
// +----+--------------------------------------------------------------+----+
//      |       (5) X-axis                                             |
//      |                                                              |
// +====+==============================================================+====+
// |            (6) UI hint text (HTML)                                     |
// +----+--------------------------------------------------------------+----+
//
//
// A note on mapping for the Y-axis
//
// The Y-axis displays events in multiple rows, in four groups: by CPU number, 
// by process ID (PID)_ number, by remote procedure call (RPC) number, and by 
// busy-resource (RES) number. Each event may be displayed up to four times on 
// four separate but time-aligned rows. Individual groups may be collapsed or
// expanded by clicking on the blue group label at the left side of Region 2.
// Groups are numbered 0..3 via prefixCpu, etc. Resources are typically a
// held lock number, but may also be a disk or network or other shared-resource
// number.
//
// Rows vary in height. Normally, they are full-size, but may be shrunk when
// a group is collapsed or when highlighting (below) is used. In addition, the 
// Y-axis may be panned and zoomed. The net effect is that multiple levels of 
// mapping and terminology are used to determine the Y-pixel coordinate(s) 
// for displaying any event.
//
// Events are numbered 0..N-1 in the data.events array. N can be a million
// or more. Event numbers are assigned once whenever new data is loaded.
//
// Each event's CPU, PID, RPC, and resource field can be mapped to one of up to 
// M rows via the array eventToRow[256K], subscripted by the low 16 bits 
// of the respective field plus 64K times the group number. There is no mapping 
// and no display if the field value is negative. For a trace with 100 CPUs 
// and 100 different PIDs and 100 different RPC IDs and 100 different busy
// resources, M would be 400. Row numbers are assigned once whenever new data 
// is loaded.
//
// Each row in turn is mapped into a height in tracks and assigned a track 
// number via the arrays rowToHeight[M]and rowToTrack[M]. The heights are 
// assigned whenever the group expand/collapse or highlighting changes, and the 
// track numbers are then calculated as the running sum of heights.
//
// Each group can be expanded to show every row within the group or collapsed 
// to show no rows. When a group is expanded, highlighted rows are full height, 
// but non-highlighted rows and be one of three selected heights -- currently
// full 20 or 5 or 1 track. Collapsed rows have height 0. 

// Track heights and numbers are reassigned dynamically whenever groups are 
// expanded or collapsed, highlights are changed, or auto-pruning (below) 
// occurs.
//
// Track numbers are the the input to Y-axis scaling (domain) and the output 
// is a Y-direction pixel number within Region 2 (range). Panning and zooming
// the Y-axis only affects this last mapping, leaving the others unchanged.
//
// Highlighting
// One or more rows can be highlighted, which keeps each event in a highlighted 
// row and in every other row the event occurs drawn in color, with all non-
// highlighted events drawn in gray. If no rows are highlighted, the effect is
// That all events are drawn in color. In addition to the change for color to 
// gray for non-highlighted events, rows consisting entirely of non-highlighted
// events may be shrunk vertically to further de-emphasize them while still
// retaining some amount of surrounding context for the highlighted events.
//
// Auto-pruning
// The events in a row have a containing time range from the earliest event
// timestamp to the latest timestamp-plus-duration. Some rows have a range that
// covers almost the entire trace, but other rows have quite limited ranges 
// because they represent a process that starts, runs, and stops during the 
// trace, or they represent an RPC that arrives, runs, and terminates. If a 
// trace has 100 RPCs but the X-axis is panned and zoomed to only display a 
// subset of the trace that contains only portions of five RPCs, it can be 
// helpful to suppress all the blank rows that represent RPCs that do not 
// overlap the displayed timespan. Auto-pruning removes (assigns height 0) all 
// rows whose time range fails to overlap the displayed range. 
//
// The effect of this feature is that panning and zooming the X-axis can also
// change the Y-axis.
//



//
//============================================================================//
// Main data structures:
//
// The data struct contains all the input data to be drawn. It is either loaded 
// from an external JSON file via d3.json, or from an internal string via 
// JSON.parse(myString).
// The struct contains at least these variables:
// Comment	: An internal comment, not shown to user
// axisLabelX	: text label
// axisLabelY	: text label
// flags	: int 128=hasIPC
//              :      32=hasLLC
// shortMulX	: int scale factor for X-axis; always 1
// shortUnitsX	: text units; always "s" for seconds
// thousandsX	: int multiplier; 1000 or 1024
// title	: diagram title, shown at top of Region 4
// tracebase	: text date and time of trace, yyyy-mm-dd_hh:mm:ss
//		  hh:mm:ss is back-converted to int and updated by X-scrolling
//		  both are shown at lower left of Region 5
// version	: version of the JSON data; should be 3 now
// events	: an array of 10-element arrays, one per timespan
//		  the last item is a dummy array with start time 999.0 so 
//		  that it sorts last, in order to avoid a comma after the last
//		  item, which would be a JSON syntax error.
//		  The design center is 1M events giving near-real-time
//		  pan, zoom, label, etc. Browser may run out of RAM before
//		  2M events though.
//
// event fields:
//  start time	: in seconds relative to hh:mm:00; expected 10ns resolution
//  duration	: in seconds; expected 10ns resolution
//  cpu		: int 0..
//  pid		: thread ID, often called PID in kernel source; 0 = idle
//  rpc		: RPC id that timespan is working on, or zero
//  ev		: event number, hex 000-fff for most events, 1xxxx for 
//		  user-mode execution to tid xxxx
//  arg0	: low 16 bits of first argument to syscall, else 0
//  ret		: low 16 bits of return value form syscall, else 0
//		  error returns -1..-128 show up as 65535..65408
//  ipc		: four-bit granular instructions per cycle, IPC
//  name	: text human-meaningful name for the timespan; syscall name, etc.
//
//============================================================================//

// Scrolling X-axis notes
//  The initial presentation of an input file shows the complete time range 
//  of the trace, up to 999 seconds (16+ minutes) and all 1M+ events.
//
//  Drawing 1M timespans is too slow to support real-time pan and zoom, and 
//  most of the timespans will be too small to see. So the redraw routine 
//  defers drawing spans shorter than one pixel until the accumulated size is 
//  at least a pixel, then draws a simplified representative line. This speeds
//  up redraw by 10x to 100x. As you zoom in, the full spans are revealed.
//
// Scrolling Y-axis notes
//  The Y-axis shows the input file timespans sorted by CPU number, by RPCID, 
//  by PID, and by busy resource. Each of these four groups can be expanded 
//  or suppressed.
// 
//  The initial presentation of an input file shows the complete range of
//  CPU numbers in the trace, but suppresses the other three groups. With
//  full expansion, there are potentially hundreds of thousands of timelines,
//  so the Y-axis also supports pan and zoom. 
//
//  Drawing 100K timelines is too slow to support real-time pan and zoom, and 
//  most of the timelines will be too small to see. So the redraw routine 
//  draws lines with a minimum height of one pixel, overlaying multiple 
//  timelines when more than one land on top of each other. The redraw
//  routine suppresses overlaid timespans unless they extend in time beyond 
//  the previously-drawn line's maximum time. This speeds up redraw by 10x 
//  or more with thousands of lines. As you zoom in, the full lines emerge.
//
// Timeline/text space notes
//  Timeline spacing runs from 2 pixels to 72 pixels. Within this, usually half
//  the vertical space is used to draw the timeline and half to draw text
//  annotations or marks. When the text half is smaller than the minimum
//  annotation text size of ~10px, maybe no text is drawn, or maybe one line.
//  As the text space increases, text size increases by perhaps 1/6 with
//  other 5/6 increasing whitespace until there is room for another line of
//  annotation/mark text.
//
// line spacing vs.overlay multiple timelines
// line/text split
// line drawing full vs abbreviation
// annotation/mark text size vs. multiple lines
// Y-label size vs. subsetting vs. overlay
//
// X-axis label extremes:
//  very big ~1000 seconds
//  very small ~10 nsec

// Y-axis extremes:
//  very big ~256K lines
//  very small ~4 lines
//
// Text-size notes
//  Four text sizes are used, and they dynamically adjust based on browser 
//  window size and user-selected multiplier: Title text, X-axis labels, Y-axis 
//  labels, and annotations/marks. The default text sizes scale linearly 
//  with the browser window height. In addition, a UI control can multiply the
//  text sizes uniformly by 0.75 1.00 1.25 or 1.50 
//  Multiplying by 1.25 or 1.50 shrinks number of annotation lines as needed
//  Multiplying by 0.75 shrinks text space, increases timespan height
//
// Default sizes: region 1 and hint, whatever HTML they are.
//   Title ~8%
//   Y-axis enough for 16-char names, or click-drag axis
//   IPC as today
//   X-axis enough for two lines ~8%
// Default scales:
//   Y-axis enough for just CPUs + 1.5
//   X-axis enough for entire trace timespan
//   (expanding groups does not change Y pan or zoom, but there is an 
//    extra line at bottom so expand last shows 1 line)

// notes: linewidth.k4=15 is readable, even with y-spacing of 8px. 
// could go down to k4=12. smalltick of 10 is readable, as is tile of 32, 
// tick of 14. vertical spacing went negative... Possibly can get by without d3.
// smalltick of 10 can multiply to 6.7, 10, 15, 20. This is on Mac with 4K
// monitor pretending to be 1000px high, not 2000. So 10px is actually 20px
// 8% top for title is good, 10% too big
// winHeight 500 gives tick 18 x 2, so 7-8% bottom should do.
// line/text when 0.75 goes to 75-25 so if 60px as 30-30, goes to 45-15
//
// buttons: black text if available, gray if not; 
//  white w/gray outline if inactive, color w/color outline if active
// text boxes: white background if inactive, color if active
//
// CPU sort by number
// RPCID sort by start time
// resources sort by type/number
// PID sort by number
// Just do toggle @basetime for per-line zero-based, display as 00:00:00
//
// Special search syntax for all-caps CPUI CPUU CPUK RPC PID RES w/us..us
//   and highlight matching lines, not annotate spans
// make names l.c. use all-caps for totals; count is by line, not span
//
// Use same shift-click, shift-unshift convention for highlights as as 
// annotations -- obviousness
//
// search limited not only to onscreen by X, but also onscreen by Y
// including not looking at gray, so can selectively search just one cpu
//
// shift-click any @title text to rotate size
// shift-click any line label toggles highlight/not
//
// In general, click toggles, shift-click cycles (downward)
// CPU group: none,  highlight-only, show
// RPCID group: none,  highlight-only, show
// PID group: none,  highlight-only, show
// Resource group: none,  highlight-only, show
//
// CB: off/on
// IPC: off, user, kernel, both
// Marks: off, abc, d, both
// Annotate: off, all
// User-mode: off, first-one
// Title: text x 1.0 0.75 1.5 1.25
//
//============================================================================//
//
// state:
// X-axis scale/offset
// Y-axis scale/offset
// CB int
// Ipc int, -1 if not available
// Marks int
// Annotate int
// User-mode int
// File text
// Search text
// search not
// search min/max usec float
// textsize multiplier int
// last selected span item#
// last highlighted line line# (bold?)
// CPU group int
// rpcid group int
// pid group int
// res group int
// Y-axis location int
//============================================================================//
//
// Upon loading json:
// move into data, remove any old data2/string to save space
// scan for min/max in each numeric field --
//  ts, dur, cpu, pid, rpc, ev, arg0, ret, ipc
// This allows setting initial scales and linecount
//  but for non-CPU linecount, need count of uniques, not min/max
//
// But also call new_windowsize function to read window size and set up region sizes
// and derive text and timespan line sizes 
// and backmap pixel size vs. time and pixelsize vs. line#
// if overlapping lines, count just once or at least map all the same
//
// Upon expanding a group:
// scan for unique values, sort, and map CPU/PID/RPC/RES to line numbers within group
// don't count non-highlight lines if not displayed
// reserve space for 4 group names when scaling lines numbers. hmm. 
// or backmap to 4x fraction of a line
// keep track of which lines are onscreen, preferably by simple min/max range
//

// Consider UI to keep track of N pan/zoom/option states, switch to any of them.
// perhaps 0-9, or A-Z; save/restore, or push/click to make pushed also top.
// shift-click-unshift to save, shift-click-unclick to erase, click to restore
// shift-click to save (turns bold), click to restore. restore saves current in 0
//
//============================================================================//




//----------------------------------------------------------------------------//
// Global constants                                                           //
//----------------------------------------------------------------------------//

// Version control for this HTML file
var script_edit_date = "(ver2024-07-14)";  // Shown in secondary menu for identification

var book = false;		// Set true to get defaults for creating book diagrams

// Almost-constants used in layout
var gOverheadNs    = 20;	// Approximate KUtrace overhead for one event
var gOverheadIpcNs = 50;	// Approximate KUtrace overhead for one event with IPC recording

// Risc-v Unmatched board overhead is much larger
const kRVOverheadNs    = 400;	// Approximate KUtrace overhead for one event
const kRVOverheadIpcNs = 400;	// Approximate KUtrace overhead for one event with IPC recording
const kRVMinShowOvhd = 0.000040;  // Always show overhead if width lt this (seconds)

// More almost-constants
// Suppress arcs that are a small fraction of the display window
//  if the window width is greater than minwidth milliseconds
var arcfraction = 0.05;    	// Small fraction of window width to suppress arc
var gMinShowArc  = 0.010000;  	// Always show arcs if window width < this (seconds)
var gMinShowOvhd = 0.000008;  	// Always show overhead if width < this (seconds)
var xpixelfraction = 1.0;  	// What fraction of a pixel width to do full draw
const kNetworkMbPerSec = 1000;	// Default: 1 Gb/s


// Little usage hints to display 
const kUsageText = "Scroll wheel to zoom. Click-drag to pan. " + 
  "Shift-click-unclick to annotate. " + 
  "Shift-click-unshift to annotate and keep. " + 
  "Shift-click-drag to measure. Red dot resets.";

const kUsageTextEmpty = "";

// Dummy data to show when there is a missing file name
// 999.0 is end marker; will be popped off before use
const kDummyData = {
  "axisLabelX" : " ",
  "axisLabelY" : " ",
  "shortMulX" : 1,
  "shortUnitsX" : "s",
  "thousandsX" : 1000,
  "title" : "File not found",
  "tracebase" : " ",
  "events" : [[0, 0, 0, 0, 0, 0, 0, 0, 0, "x"], 
              [999.0, 0, 0, 0, 0, 0, 0, 0, 0, "x"]]
};

const ipibase =     -7;
const dotsbase =    -6;
const annotbase =   -5;
const calloutbase = -4;
const arcbase =     -3;
const ovalbase =    -2;
const bracketbase = -1;
const msgrxbase =   0x00204;	// Complete messages
const msgtxbase =   0x00205;
const send_ipi =    0x00207;
const freqbase =    0x00209;
const markbase =    0x0020A;
const pktrxbase =   0x00214;	// Individual packets seen by kernel tcp/udp
const pkttxbase =   0x00215;
const userrxbase =  0x00216;	// Individual packets seen by user code
const usertxbase =  0x00217;
const queued =      0x0021A;	// RPC queued
const mon_store =   0x0021E;	// Monitor_store or ipi wakeup
const lockbase =    0x00282;
const pcbase =      0x00280;	// PC samp 280=u, 281=k
const waitbase =    0x00300;
const faultbase =   0x00400;
const irqbase =     0x00500;
const syscallbase = 0x00800;
const schedbase =   0x009FF;	// Linux x86 dense syscalls
const schedbase2 =  0x00DFF;	// FreeBSD and other sparse syscalls
const rxtxbase =    0x00FFE;
const ovhdbase =    0x00FFF;
const userbase =    0x10000;
const idlebase =    0x10000;
const cexitbase =   0x20000;

const CR = 13;			// Ascii carriage-return 
const searchlistMax = 20;
const searchlistMaxDisplay = 7;

const NSEC10 = 0.000000010;	// 10 nanoseconds



// Pixel-banging in layout
const min_txt_px = 12;		// Minimum font size px for annotation text lines
const rpcfontsize = 12;		// Font size px for RPC message numbers

// LLC misses (just misses, not writebacks)
// Miss count per timespan is encoded into 4 bits as power of two buckets:
// 0:0  1:1-7  2:8..15  3:16..31  ...  14:32K..64K-1  15:64K+
//
// We turn these in to nanoseconds of memory-bus occupancy by estimating 50ns
// for a single miss and derating multiple misses by a factor of 1.1 for each
// bucket, so down by a factor of 2 after 8 buckets and about 4.5 for bucket 15.
// This very roughly approximates overlapped multiple outstanding misses.
// The table here maps bucket number 0..15 to nanoseconds of memory activity.
// These values are extremely rough and only given to two digits.
//const kLlcNsec = [
//  0, 100, 450, 820,             1500, 2700, 5000, 9000, 
//  16000, 30000, 54000, 98000,   180000, 320000, 590000, 1100000
//ss];

// The above seem too large (exceeding actual duration too often).
// Made them ~40% smaller above 16 misses 2024.04.01
const kLlcNsec = [
  0, 100, 400, 700,             900, 1600, 3000, 5400, 
  9600, 18000, 32000, 59000,   110000, 190000, 350000, 660000
];



//----------------------------------------------------------------------------//
// Global constant colors                                                     //
//----------------------------------------------------------------------------//
// For buttons
const kActiveBackColor = "#F0F0FF";	// very light blue
const kActiveBorderColor = "#0000FF";	// blue
const kActiveTextColor = "#000080";	// dark blue

const kInactiveBackColor = "#FFFFFF";	// white
const kInactiveBorderColor = "#C0C0C0";	// light gray
const kInactiveTextColor = "#000000";	// black

const kUnuseableColor = "#E0E0E0";	// very light gray

// For spans
const normalcolor = {
  // HSB(n mod 17, 1.0, 0.8)  top/middle, no white
  color1: [
    "#CC0000", "#CC4700", "#CC8F00", "#BECC00", "#77CC00", 
    "#30CC00", "#00CC18", "#00CC5F", "#00CCA7", "#00A7CC", 
    "#005FCC", "#0018CC", "#3000CC", "#7700CC", "#BE00CC", 
    "#CC008F", "#CC0047" 
  ],

  // HSB(n mod 15, 1, 1)  bottom/outer, no white (made yellow/green darker)
  color2: [
    "#FF0000", "#FF6600", "#FFCC00", "#CCE000", "#66E000", 
    "#00FF00", "#00FF66", "#00FFCC", "#00CCFF", "#0066FF", 
    "#0000FF", "#6600FF", "#CC00FF", "#FF00CC", "#FF0066" 
  ],

  gray1: [
    "#959595", "#aeaeae", "#c8c8c8", "#dddddd", "#d5d5d5", 
    "#cecece", "#c9c9c9", "#cccccc", "#cecece", "#c2c2c2", 
    "#a8a8a8", "#8f8f8f", "#8c8c8c", "#939393", "#9b9b9b", 
    "#9a9a9a", "#989898"
  ],

  gray2: [
    "#9b9b9b", "#bfbfbf", "#e3e3e3", "#f0f0f0", "#e5e5e5", 
    "#dadada", "#dedede", "#e2e2e2", "#d2d2d2", "#adadad", 
    "#898989", "#949494", "#9e9e9e", "#a2a2a2", "#9e9e9e" 
  ],

  lightcolor1: [
    "#e68080", "#e6a380", "#e6c780", "#dfe680", "#bbe680", "#98e680", "#80e68c", "#80e6af", "#80e6d3", "#80d3e6",     
    "#80afe6", "#808ce6", "#9880e6", "#bb80e6", "#df80e6", "#e680c7", "#e680a3"
  ],

  lightcolor2: [
    "#ff8080", "#ffb380", "#ffe680", "#e6ff80", "#b3ff80", "#80ff80", "#80ffb3", "#80ffe6", "#80e6ff", "#80b3ff", 
    "#8080ff", "#b380ff", "#e680ff", "#ff80e6", "#ff80b3"
  ], 

  mod1: 17,
  mod2: 15
};

// Rotate RGB colors to BRG
const colorblindcolor = {
  color1: [
    "#00CC00", "#00CC47", "#00CC8F", "#00BECC", "#0077CC", 
    "#0030CC", "#1800CC", "#5F00CC", "#A700CC", "#CC00A7", 
    "#CC005F", "#CC0018", "#CC3000", "#CC7700", "#CCBE00", 
    "#8FCC00", "#47CC00" 
  ],

  color2: [
    "#00FF00", "#00FF66", "#00FFCC", "#00CCFF", "#0066FF", 
    "#0000FF", "#6600FF", "#CC00FF", "#FF00CC", "#FF0066", 
    "#FF0000", "#FF6600", "#FFCC00", "#CCE000", "#66E000" 
  ],

  gray1: [
    "#959595", "#aeaeae", "#c8c8c8", "#dddddd", "#d5d5d5", 
    "#cecece", "#c9c9c9", "#cccccc", "#cecece", "#c2c2c2", 
    "#a8a8a8", "#8f8f8f", "#8c8c8c", "#939393", "#9b9b9b", 
    "#9a9a9a", "#989898"
  ],

  gray2: [
    "#9b9b9b", "#bfbfbf", "#e3e3e3", "#f0f0f0", "#e5e5e5", 
    "#dadada", "#dedede", "#e2e2e2", "#d2d2d2", "#adadad", 
    "#898989", "#949494", "#9e9e9e", "#a2a2a2", "#9e9e9e" 
  ],

  lightcolor1: [
    "#80e680", "#80e6a3", "#80e6c7", "#80dfe6", "#80bbe6", "#8098e6", "#8c80e6", "#af80e6", "#d380e6", "#e680d3", 
    "#e680af", "#e6808c", "#e69880", "#e6bb80", "#e6df80", "#c7e680", "#a3e680" 
  ],

  lightcolor2: [
    "#80ff80", "#80ffb3", "#80ffe6", "#80e6ff", "#80b3ff", "#8080ff", "#b380ff", "#e680ff", "#ff80e6", "#ff80b3",  
    "#ff8080", "#ffb380", "#ffe680", "#e6ff80", "#b3ff80" 
  ],

  mod1: 17,
  mod2: 15
};


// Subscripted by ev >> 8. i.e. the top hex digit of an event < 4K
// unknown: light yellow, fault: light red, interrupt: light blue, syscall: light green
const kernelcolor = [
  "#FFFFE0", "#FFFFE0", "#FFFFE0", "#FFFFE0", 
  "#FFC0C0", "#C0C0FF", "#FFC0C0", "#C0C0FF",  
  "#E0FFE0", "#E0FFE0", "#E0FFE0", "#E0FFE0", 
  "#E0FFE0", "#E0FFE0", "#E0FFE0", "#E0FFE0", 
  ];

const kernelgray = [
  "#fdfdfd", "#fdfdfd", "#fdfdfd", "#fdfdfd", 
  "#e6e6e6", "#e2e2e2", "#e6e6e6", "#e2e2e2", 
  "#fafafa", "#fafafa", "#fafafa", "#fafafa", 
  "#fafafa", "#fafafa", "#fafafa", "#fafafa" 
  ];

// Edges unknown: yellow, fault: red, interrupt: blue, syscall: green
const darkkernelcolor = [
  "#FFFF70", "#FFFF70", "#FFFF70", "#FFFF70", 
  "#FF6060", "#6060FF", "#FF6060", "#6060FF",  
  "#70FF70", "#70FF70", "#70FF70", "#70FF70", 
  "#70FF70", "#70FF70", "#70FF70", "#70FF70", 
  ];

const darkkernelgray = [
  "#f9f9f9", "#f9f9f9", "#f9f9f9", "#f9f9f9", 
  "#c0c0c0", "#b5b5b5", "#c0c0c0", "#b5b5b5", 
  "#eaeaea", "#eaeaea", "#eaeaea", "#eaeaea", 
  "#eaeaea", "#eaeaea", "#eaeaea", "#eaeaea" 
  ];

// Thresholds for showing slow CPU clocks
// Subscripted by which eighth of freq_min..freq_max we are in.
// Extra items allow overrun when tiny divisor rounds down, so quotient is larger.
const freq_color_range = ["#E00000", "#E0C000", "#E0C000", "#E0C000", 
                          "#E0C000", "#E0C000", "#E0C000", "#00C000", 
                          "#00C000", "#00C000", "#00C000", "#00C000", 
                          "#00C000", "#00C000", "#00C000", "#00C000"];

// Green is not quite clear for full speed; very light green
const freq_opacity_range =      [0.5, 0.4, 0.4, 0.3, 0.3, 0.2, 0.2, 0.05, 
                                 0.05, 0.05, 0.05, 0.05, 0.05, 0.05, 0.05, 0.05];
// Green is not quite clear for full speed; very light green
const freq_bold_opacity_range = [0.6, 0.5, 0.5, 0.4, 0.4, 0.3, 0.3, 0.15, 
                                 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15];

//----------------------------------------------------------------------------//
// Global Morse code constant dot-dash patterns and colors                    //
//----------------------------------------------------------------------------//
// These are for showing reasons for a process or RPC to be waiting
//
// c: Waiting for CPU, gray
// d: Waiting for disk, green
// l: Waiting for lock, red
// m: Waiting for memory, brown
// n: Waiting for network, blue
// p: Waiting for pipe data, dark gray
// t: Waiting for timer, light gray

// International Morse Code
//  A .-    B -...
//  C -.-.  D -..
//  E .     F ..-.
//  G --.   H ....
//  I ..    J .---
//  K -.-   L .-..
//  M --    N -.
//  O ---   P .--.
//  Q --.-  R .-.
//  S ...   T -
//  U .. -  V ...-
//  W .--   X -..-
//  Y -.--  Z --..

const waitdash = [
  [3,3, 9,9],        [9,3, 3,3, 3,3, 3,9],
  [9,3, 3,3, 9,3, 3,9],   [9,3, 3,3, 3,9],
  [3,9],             [3,3, 3,3, 9,3, 3,9],
  [9,3, 9,3, 3,9],   [3,3, 3,3, 3,3, 3,9],
  
  [3,3, 3,9],        [3,3, 9,3, 9,3, 9,9],
  [9,3, 3,3, 9,9],   [3,3, 9,3, 3,3, 3,9],
  [9,3, 9,9],        [9,3, 3,9],
  [9,3, 9,3, 9,9],   [3,3, 9,3, 9,3, 3,9],

  [9,3, 9,3, 3,3, 9,9],   [3,3, 9,3, 3,9],
  [3,3, 3,3, 3,9],   [9,9],
  [3,3, 3,3, 9,9],   [3,3, 3,3, 3,3, 9,9],
  [3,3, 9,3, 9,9],   [9,3, 3,3, 3,3, 9,9],

  [9,3, 3,3, 9,3, 9,9],   [9,3, 9,3, 3,3, 3,9],

  [1,1], [1,1], [1,1], [1,1], [1,1], [1,1]
];

// ab cd ef gh   ij kl mn op   qr st uv wx   yz
const waitcolor = [
  "#808080",   "#808080",	// default = grey
  "#FFC000",   "#55AA55",	// cpu = orange, disk = darkish green
  "#808080",   "#808080",
  "#808080",   "#808080",

  "#808080",   "#808080",
  "#808080",   "#FF4040",	// lock = bright red
  "#AA5500",   "#8080FF",	// memory = dk brown, net = blue
  "#808080",   "#555555",	// pipe = dark gray for now

  "#808080",   "#808080",
  "#808080",   "#E0E0E0",	// time = light grey
  "#808080",   "#808080",
  "#808080",   "#808080",

  "#808080",   "#808080",

  "#808080",   "#808080",
  "#808080",   "#808080",
  "#808080",   "#808080",
];

const fadecolor = "#E8E8E8";	// Very light gray

//----------------------------------------------------------------------------//
// Global defaults that can be modified                                       //
//----------------------------------------------------------------------------//

var basictextsize = 12;		// Calculated at window size change (px)

var debug = false;		// show debugging options 
// moved to state: var fade = 0;			// 0: color, 1: gray KUI, 2: gray KUI/cexit/wait

var timerHandle;		// For fewer redraws of events (faster with big pan/zoom)

var has_ipc = false;
var has_llc = false;
var has_both = false;	

const prefixCpu = 0;
const prefixPid = 1;
const prefixRpc = 2;
const prefixRes = 3;

////const textmultiplier_tbl = [1.0, 0.75, 1.5, 1.25];

const kFromBase40 = "_abcdefghijklmnopqrstuvwxyz0123456789-./";

const ipctextoffset = [0, 0, 4, 8];


// Convert search min/max units to seconds
const units_div = [1000000000.0, 1000000.0, 1000.0];
const units_name = ["nsec:", "&#x00b5;sec:", "msec:"];
const annottype_name = ["Annot:", "annot:"];

// Set the initial margins of the graph
const margin = {top: 20, right: 20, bottom: 50, left: 70, diagramtop: 20, diagrambottom: 50};
 
// Set the margins of the SVG area
const winMargin = {width: 10, height: 40};	// Room for borders and File: box

// Room for top/bottom HTML. These fit snuggly for Chrome, but run over for Safari
// Altered in initializeState if needed
var winMargin2 = {top:40, top2x: 60, bottom:50, bottom2x: 70, left:15, right:15, width1: 1100};	


// Click to toggle 0/max
// Shift-click to cycle 0 max max-1 ... 0
const annotateall_value_max = 1;	// 0: none, 1: all
const annotateuser_value_max = 1;	// 0: none, 1: all
const ipc_value_max = 3;		// 0: none, 1: kernel, 2: user, 3: all
const marks_value_max = 3;		// 0: none, 1: number, 2: text, 3: all
const arcs_value_max = 2;		// 0: none, 1: bold, 2: normal
const waits_value_max = 1;		// 0: none, 1: all
const freq_value_max = 2;		// 0: none, 1:bold, 2: show CPU frequency
const ovhd_value_max = 1;		// 0: none, 1: show overhead time
const lock_value_max = 2;		// 0: none, 1: bold, 2: show lock-held lines 
const cb_value_max = 1;			// 0: normal colors, 1: color-blind colors
const samp_value_max = 2;		// 0: none, 1: show idle PC samples, 2: show PC samples
const searchnot_value_max = 1;		// 0: normal, 1: invert match         
const textmultiplier_value_max = 3;	// 0: normal, 1: 0.75x, 2: 1.5x, 3: 1.25x
const basetime0_value_max = 1;		// 0: normal, 1: relative to row start, /* 2: summary_h, 3: summary_v */
const compress_value_max = 2;		// 0: none, 1: coloronly, 2: all
const minmax_units_max = 2;		// 0: nsec, 1: usec, 2: msec
const annottype_max = 1;		// 0: normal, 1: notched line
const hack_value_max = 1;		// For random trials. Toggled by CB button shift-click

// Secondary menu
const legend_max = 2;			// 0: off, 1: vertical, 2: horizontal
const fade_max = 1;			// 0: off, 1: on


const axesmargin = 5;	// Keep axes and pan zoom rect away from region3 edges (px)

// Annotates: possibly cycle thru long vs. short annotations

// Note: highlighting is shift-click, because it is in the zoom/pan click area
// Behaves like shift-click to annotate

// Region 0 is HTML above the svg. see winMaergin2.top
// Region 1 is the top part of svg for title
// Region 2 is the left part of svg for Y-axis labels
// Region 3 is the middle part and holds all the timelines
// Region 4 is the optional IPC legend
// Region 5 is two-line X-axis
// Region 6 is HTML below the SVG. see winMargin2.bottom

// Figure maximum height = 2400px, maximum width 4000px
// Each region has a percentage of the outersvg width/height, 
// with min and max bounds in pixels
const region1paramH = [8, 30, 120];	// Title gets 8% of height, 30..120px
const region5paramH = [8, 36, 120];	// X-axis and footer gets 8% of height 36..120px
// Middle gets the rest ~= 84% of height
const basictextparamH = [100, 12, 30];	// Axis labels, annots, etc. 12..30px

const region2paramW = [15, 50, 600];	// Y-axis gets 15% of width, 50..600px
const region4paramW = [5, 20, 80];	// IPC legend gets 5% of width
// Timelines get the rest ~= 80% of width
const region2charsparamW = [100, 12, 30];	// Y-axis labels 18..24 chars

const ipcvalues = ["0", "1/8", "1/4", "3/8",  "1/2", "5/8", "3/4", "7/8", 
                   "1.0", "1.25", "1.5", "1.75",  "2.0", "2.5", "3.0", "3.5"];
//const llcvalues = ["0us", "0.05", "0.12", "0.22",  "0.45", "0.82", "1.5", "2.7", 
//                   "5", "9", "16", "30",  "54", "98", "180", "320"];
//const llcvalues = ["0", "1", "2-3", "4-7",  "12", "24", "48", "96", 
//                   "192", "384", "768", "1.5K",  "3K", "6K", "12K", "24K+"];
//const llcvalues = ["0", "64B", "128", "256",  "512", "1KB", "2KB", "4KB", 
//                   "8KB", "16K", "32K", "64K",  "128K", "256K", "512K", "1M+"];
// 2023.06.25 encoding
const llcvalues = ["0", "64", "512", "1KB",       "2KB", "4KB", "8KB", "16K", 
                   "32K", "64K", "128K", "256K",   "512K", "1M", "2M", "4M+"];

const ipc_color = ["#555555", "#0000FF", "#0000FF", "#AA0000"]; 
//         dark gray < 1/2    1/2 <= blue < 2     dark red >= 2

// Non-Highlighted items are displayed in gray, highlighted in color
// Row heights vary by group compress value, based on 1/20 of a full-size row
// (originally, heights of 1 and 5 were also used for squeezed rows)
const grayHeight = [0, 0, 20];	// Subscripted by compress_xxx 
const maxTrackHeight = 20;	// 20 tracks per full-size row

//----------------------------------------------------------------------------//
// Globals about browser window and allocated SVG                             //
//----------------------------------------------------------------------------//

var winTileHeight;
var winTileWidth;
var winScale;

var winWidth = 960;	// Default 16:9, immediately overwritten
var winHeight = 540;
var svgWidth = 16;	// Default, immediately overwritten
var svgHeight = 9;

var region1height, region234height, region5height;
var region2width, region2widthshort, region3width, region3clipwidth, region4width;

var outersvg, svgdotg, innersvg, innersvgdotg;
var svgdotgbare, innersvgbare, innersvgdotgbare;
var region2, region2dotg;
var reddot;

var searchlist = [];


//----------------------------------------------------------------------------//
// Globals for X-axis                                                         //
//----------------------------------------------------------------------------//
//var axisTickPx = "16px";
var AxisLabelPx = "20px";

// Scales for the axes
var x;
var x_zoomed;

// The axis definitions
var xAxis;

// The groups containing the drawn axis and vertical grid overlay
var gXgrid;
var gX;

// The svg zoom, no attach, no draw; define watcher
var myZoom_x;
var panzoomrect_x = null;
//var ignore_zoom = false;


// leftmost and rightmost on-screen x values, for filtering, for speed
var realxleft;
var realxright;
var realxrightmost;	  // includes the margin.right padding
var xdomainwidth;	  // abs(right - left)
var xsecperpix = 0;	  // Only do full drawing of items >= xsecperpix
 
// Changed in calculateBasetime
var xaxisfmt = {
  basetime: 0,
  deltamul: 1, 
  deltamax: 1000, 
  ovflnext: 0, 
  ovflformat: "03.0f",
  deltaformat: "03.0f"
};


//----------------------------------------------------------------------------//
// Globals for Y-axis                                                         //
//----------------------------------------------------------------------------//

// Scales for the axes
var y;
var y_zoomed;

// The axis definitions
var yAxis;

// The groups containing the drawn axis
var gY;

// The svg zoom, no attach, no draw; define watcher
var myZoom_y;
var panzoomrect_y = null;


// topmost and bottommost on-screen y values (tracks), for filtering, for speed
var realytop;
var realybottom;
var ydomainwidth;	  // abs(top - bottom)
var ypixelfraction = 1.0; // What fraction of a pixel height to do full draw
var ypixpertrack = 0;	  // Only do full drawing of items >= ypixpertrack
var ytop_row;
var ybot_row;


// Within-track event and annotation positions and sizes, for each of
// track sizes 1..20. Function of Y-axis zoom
//  perTracksize[i].annotsize 	annotation font size in pix
//  perTracksize[i].annotcount 	annotation lines of text for this size
//                              = trunc(annotheight / annotsize)
//  perTracksize[i].eventheight pix height for drawing events
//  perTracksize[i].annotheight pix height for drawing annotation text
//  perTracksize[i].eventoffset pix offset for event centerline
//  perTracksize[i].annotoffset pix offset for annot[0] text baseline

// Constant mapping after loading data: cpu#/pid#/rpc# ==> row
// Varying mapping based on highlighting: row ==> trackheight in rowToHeight[row]
//   trackheight varies from 0 to 20, an arbitrary scale.
// The Y-axis values are track numbers
// Variable-height rows are mapped to Y-axis values in rowToTrack[row], 
//   giving the top track of a row display
// Y-axis track numbers are mapped to pixel positions by the function y(), with 
//   track# domain [0, maxTrack] and px range [axesmargin, region234height - axesmargin]
//   The actual mapping varies by Y-axis pan and zoom
//
// WITHIN a row's tracks there is further subdivision:
//
// rowToTrack[row]   40+============          ^              ^  ^
//                     | (a) PCsamp           |              |  |  eventoffset
//                     +----------            |              |  |  in px
//                     |                      |  eventheight |  v
//                     | (b) event lines      |  in px       |
//                     |                      v              |
//                     +------------          ^              |  annotoffset
//                     | (c) lock_line        |              v  in px
//                     | (c) annotation text  |  annotheight  
//                     |                      |  in px
// rowToTrack[row+1] 60+============          v
//                     |
//                     :
// All the timeline drawing is positioned using eventheight
// All the annotation text is positioned using annotheight
// Positioning, font sizes, and detail suppression scale according to these:
//   line widths in l1/l2/l3/l4width
//   wait line position and width in draw_wait/call
//   PC sample line position and width in draw_pc_samp/call
//   lock_line line position and width in draw_lock_line/call
//
// eventoffset gives the centerline for the event lines
// annotoffset gives the baseline for the first line of annotation text
//
// This decomposition into pixels is calculated not once per row, but once
// per possible track height, 0..20, in calcPixHeightsPerTrack.
//
 
var perTracksize = [];


//----------------------------------------------------------------------------//
// Globals for data and its metadata                                          //
//----------------------------------------------------------------------------//
var data = null;

//
// Set by getDataMetadata after data is loaded, and then constant
// function of data.events
//
var eventToRow = [];	// Map events to sorted rows. Will be 4 x 64K

// All these will be number of rows in size
var sortNum = [];		// Gives grouped event number for each row
var sortName = [];		// Gives grouped event name for each row
var rowToMinT = [];		// min start_ts
var rowToMaxT = [];		// max start_ts + duration

var dataTsLo, dataTsHi;
var dataBaseDate, dataBaseSecond;

var sortNum_cpu_start = 0;
var sortNum_pid_start = 0;
var sortNum_rpc_start = 0;
var sortNum_res_start = 0;
var sortNumSize = 0;		// Number of rows

var localStorageKey;

//
// Not constant
// Function of data.events, groupcompress, highlight
//
// This varies at runtime. See updateTracks, keyupDebugRows [deprecate this]
var maxTrack = 0;		// Current Number of tracks. Varies with highlights

// These vary at runtime. See updateTracks
var rowToHeight = [];		// Per row, N+1 rows
var rowToTrack = [];		// Per row, N+1 rows


//----------------------------------------------------------------------------//
// Globals for drawing                                                        //
//----------------------------------------------------------------------------//

// These hold per-row state while drawing events
// All are subscripted by row number
var defer_dur_i = [];		// Idle
var defer_dur_u = [];		// User
var defer_dur_k = [];		// Kernel
var defer_dur = [];		// All -- nothing lost
var defer_event_u = [];		// Most recent deferred user event
var defer_event_k = [];		// Most recent deferred kernel event
var highwater_marks = [];	// Keep mark_a/b/c/d from being too repetitious
var highwater_mark_type = [];	//  "
var rowToLabelk = [];		// Rotate event labels separately for each row
var rightmostnum = [];		// Spread apart mark_d numbers
var pending_locks = [];		// End times for each active lock_line 

var matchcount = 0;	// number of matches onscreen
var durcount = 0;	// total duration of matches onscreen
var min_match = 999999.0;	// To allow user to find extreme values easily
var max_match = -1.0;		//  by updating search min..max range and 2nd search

 
//----------------------------------------------------------------------------//
// Global UI state                                                            //
//----------------------------------------------------------------------------//

// Update initializeState() if you add/subtract anything here
var state = {
  // These variable describe the current Region 3 view, axes and options
  // Used for save/restore buttons 1-5
  //  X-axis
//  savedtransformX: d3.zoomIdentity,
//  rangeX: 10,			// Arbitrary. Updated in setInitialRangeDomain
//  xleft_ts: 0, 			// Leftmost time on screen now
//  xright_ts: 0,			// Rightmost time on screen now

  //  Y-axis
//  savedtransformY: d3.zoomIdentity,
//  rangeY: 10,			// Arbitrary. Updated in setInitialRangeDomain
//  ytop_track: 0, 		// Topmost track on screen now
//  ybot_track: 0,		// Bottommost track on screen now

  // For the four groups: Cpu, Pid, Rpc, Res(ource)
//  grouplength: [0, 0, 0, 0],
//  groupcompress: [2, 0, 0, 0], 		// Show CPUs, collapse others
//  grouplabelypos: [0, 0, 0, 0],		// In pixels, for expand/compress place 
//  groupfirsttrack: [0, 0, 0, 0], 	// Label track number for expand/compress place
//  grouphashilite: [0, 0, 0, 0],		// One if any row in group is highlighted

  //  Buttons
//  annottype_value : 0, 		// Normal
//  annotateuser_value: 0, 	// Off
//  annotateall_value: 0, 	// Off
//  marks_value: 3, 		// Initially, show all marks
//  arcs_value: 2, 		// Initially, show all arcs (when zoomed in enough)
//  freq_value: 2, 		// Show slow CPU clock freq gradient
//  waits_value: 1, 		// Initially, show all waits (when zoomed in enough) DEPRECATED
//  ipc_value: 0, 		// Do not show IPC by default
//  samp_value: 0, 		// Do not show PC samples by default
//  ovhd_value: 1, 		// Show overhead time by default
//  lock_value: 2, 		// Show lock-held lines by default
//  cb_value: 0, 			// do not show CB by default
//  searchnot_value: 0,		// false = like grep. true = like grep -v
//  search_text: "",
//  minmax_units_value: 1,	// usec
//  usec_lo_time: 0,		// Actually nsec/usec/msec now
//  usec_hi_time: 0,		//  see state2.minmax_units_value 

// Last single item annotated, even across pan/zoom
//  annotated_one_d: -1,		// subscript of nearest previously-annotated highlited item

//Part of a view. Reconstruct hilite_event_array from here
//  rowToHilite: [],

  // These variables are just for save/restore export of initial view
  // NOT part of a view
  traceid: "",			// Date/time trace ID
  randomid: -1,			// Random number trace ID

  // Uncatigorized as yet
  ////textmultiplier_value: 0,
  ////basetime0_value: 0,

  hilite_evnum : 0,		// Shift-RIGHT-click on an event highlights all of them
  hilite_evnum2 : 0,		// previous highlight, so youcan have two at once onscreen
  just_reset: true,		// Controls initial display of kernel and CPU model

  // List of items that are annotated on screen
  // NOT part of a view
  annotated_d: [],

  // Highlighted events and their rows
  // NOT part of a view. REconstructed from smaller rowToHilite
////  hilite_event_array: [],


  // NOTE: save/restore does not work for sets. Sigh.
  // So instead we move sets into these arrays around save/restore

  dummy_placeholder: 0		// No comma here
};

// Derived from state, Update these when state is restored, via RecalcDerivedState
// --------
var currentcolor = normalcolor; 
var hilite_event_set = new Set();
// --------
// end Derived

// Outside of state; not saved/restored
// Secondary menu
  // These variables describe the overall SVG area, Regions 1-5
var aspecth = 0; 			// Initially, any
var aspectv = 0; 			// Initially, any
var legend_value = 0;
var fade_value = 0;
var force_annot = false;	// Used in drawing legend
var is_riscv = false;		// Show larger overhead if risc-v


//
// This state2 describes a "view" -- all the choices of what information to show in Region 3.
// Views are saved and restored without changing the other state items above.
//
var state2 = {
  // These variable describe the current Region 3 view, axes and options
  // Used for save/restore buttons 1-5
  //  X-axis
  savedtransformX: d3.zoomIdentity,
  rangeX: 10,			// Arbitrary. Updated in setInitialRangeDomain
  xleft_ts: 0, 			// Leftmost time on screen now
  xright_ts: 0,			// Rightmost time on screen now

  //  Y-axis
  savedtransformY: d3.zoomIdentity,
  rangeY: 10,			// Arbitrary. Updated in setInitialRangeDomain
  ytop_track: 0, 		// Topmost track on screen now
  ybot_track: 0,		// Bottommost track on screen now

  // For the four groups: Cpu, Pid, Rpc, Res(ource)
  grouplength: [0, 0, 0, 0],
  groupcompress: [2, 0, 0, 0], 		// Show CPUs, collapse others
  grouplabelypos: [0, 0, 0, 0],		// In pixels, for expand/compress place 
  groupfirsttrack: [0, 0, 0, 0], 	// Label track number for expand/compress place
  grouphashilite: [0, 0, 0, 0],		// One if any row in group is highlighted

  //  Buttons
  annottype_value : 0, 		// Normal
  annotateuser_value: 0, 	// Off
  annotateall_value: 0, 	// Off
  marks_value: 3, 		// Initially, show all marks
  arcs_value: 2, 		// Initially, show all arcs (when zoomed in enough)
  freq_value: 2, 		// Show slow CPU clock freq gradient
  waits_value: 1, 		// Initially, show all waits (when zoomed in enough) DEPRECATED
  ipc_value: 0, 		// Do not show IPC by default
  samp_value: 0, 		// Do not show PC samples by default
  ovhd_value: 1, 		// Show overhead time by default
  lock_value: 2, 		// Show lock-held lines by default
  cb_value: 0, 			// do not show CB by default
  searchnot_value: 0,		// false = like grep. true = like grep -v
  search_text: "",
  minmax_units_value: 1,	// usec
  usec_lo_time: 0,		// Actually nsec/usec/msec now
  usec_hi_time: 0,		//  see state2.minmax_units_value 
  basetime0_value: 0,
  hack_value: 0,		// For random trials

  // Last single item annotated, even across pan/zoom
  annotated_one_d: -1,		// subscript of nearest previously-annotated highlited item

  //Part of a view. Reconstruct hilite_event_array from here
  rowToHilite: [],

  dummy: 0			// No comma here
};


// Saving state N copies current to [N]
// Restoring state N copies current to [0] and loads current from [N]
//  (swaps cleanly if N=0)

// Sample saved view exported to clipboard
// {"savedtransformX":{"k":12.159398851212135,"x":-400.5211251718175,"y":0},"rangeX":584, ... "dummy":0}


// If no savedview, initialize to this
var initview = [ 
  ["&#x21d0;", null],
  ["&#x2780;", null],
  ["&#x2781;", null],
  ["&#x2782;", null],
  ["&#x2783;", null]
];

// Populate HTML from savedview
function populateSavedviewHtml() {
  // Insert them into HTML
  var plusElement = document.getElementById("saveplus");
  for (i = 0; i < data.savedview.length; ++i) {
    var lbl = data.savedview[i][0];
    var json = data.savedview[i][1];
    var newElement = "&nbsp;<span id=\"view" + i + "\" style=\"border-radius:6px\";>" + lbl + "</span>&nbsp;";
    plusElement.insertAdjacentHTML('beforebegin', newElement);
    document.getElementById("view" + i).onclick = doToggleView;
    decorateSave(document.getElementById("view" + i), json !== null);
  }
}

// No savedview in the input JSON file, so create the default one.
function createSavedview() {
//console.log("createSavedview");
  data.savedview = initview;
}



var more_text = false;

//----------------------------------------------------------------------------//
// End Global UI state                                                        //
//----------------------------------------------------------------------------//


//----------------------------------------------------------------------------//
// Other globals                                                              //
//----------------------------------------------------------------------------//

// Set the initial sizes of text, in pixels
// Last ones are debug overrides if non-zero
// 2021.01.16 now default to 3 rows of spans and 3 rows of text
var textsize = {tick: 16, smalltick: 12, label: 20, title: 32, annot: 0, xnice: 10,  
  region2chars:18, region2px:18, rows:0, spn:3, txt:3 };

// Set the initial sizes of event lines
var linewidth = {
  k4 : 30, k3 : 28, k2 : 16, k1 : 6,  
  idle : 2, u2 : 12, u1 : 8
};

// Set the initial dimensions of the graph by calling resize()
var width;
var height;
//var region234height;	// Smaller than height if not many tracks
 
// Keep track of the min and max CPU clock frequency seen, and
// mapping to yellow caution density if clock is slow
var freq_min, freq_max, freq_range, freq_divisor;
var freq_backward_compat = false;

// LOCAL For rubberband
var rubberband;		// Below marked object
var rubberband2;	// Above marked object
var rubberband3;	// Time between two objects
var rubberband4;	// background for rubberband3 text
var x1, y1;
var dx1, dy1;
var vx1, vx2;
var mx1, my1;			// First point for mousemove drag
var originalx, originaly;	// Initial values for delta updates callout drag
var dragging_bubble;		// Callout drag
var dragging_spike;		// Callout drag 
var d1, d2;		// First and second marked data objects


// LOCAL For first-seen user-mode annotation 
var dont_annotate = [];
dont_annotate[0x10000] = true;	// Never label -idle- event

// LOCAL For annotations

// LOCAL for Y labels
var next_ylabel_y0 = 0;


//----------------------------------------------------------------------------//
// Function definitions                                                       //
//----------------------------------------------------------------------------//

// Timestamps are in seconds
function NsToTs(ns) {return ns * 0.000000001;}

// Gets the browser name or returns an empty string if unknown.
// navigator.userAgent
// Chrome
// "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"
// Safari
// "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_2) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0.2 Safari/605.1.15" = $1 
// Firefox
// "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.14; rv:64.0) Gecko/20100101 Firefox/64.0"

function whichBrowser() {
  // Order is important here. Otherwise, Chrome reports as Safari.
  if (navigator.userAgent.indexOf(' Chrome') >= 0) {return 'Chrome';}
  if (navigator.userAgent.indexOf(' Safari') >= 0) {return 'Safari';}
  if (navigator.userAgent.indexOf(' Firefox') >= 0) {return 'Firefox';}

    // Opera 8.0+
    var isOpera = (!!window.opr && !!opr.addons) || 
      !!window.opera || 
      navigator.userAgent.indexOf(' OPR/') >= 0;

    // Firefox 1.0+
    var isFirefox = (typeof InstallTrigger !== 'undefined');

    // Safari 3.0+ "[object HTMLElementConstructor]" 
    var isSafari = /constructor/i.test(window.HTMLElement) || 
      (function (p) { return p.toString() === "[object SafariRemoteNotification]"; })
       (!window['safari'] || safari.pushNotification);

    // Internet Explorer 6-11
    var isIE = /*@cc_on!@*/false || !!document.documentMode;

    // Edge 20+
    var isEdge = !isIE && !!window.StyleMedia;

    // Chrome 1+
    var isChrome = !!window.chrome;

    // Blink engine detection
    var isBlink = (isChrome || isOpera) && !!window.CSS;

    var foo = 
        isOpera ? 'Opera' :
        isFirefox ? 'Firefox' :
        isSafari ? 'Safari' :
        isChrome ? 'Chrome' :
        isIE ? 'IE' :
        isEdge ? 'Edge' :
        isBlink ? 'Blink' :
        "Don't know";
    return foo;
};

// Track search/highlight matches
function InitMatchcount() {
  matchcount = 0;	// number of matches onscreen
  durcount = 0;	// total duration of matches onscreen
  min_match = 999999.0;	// To allow user to find extreme values easily
  max_match = -1.0;		//  by updating search min..max range and 2nd search
}

// Track search/highlight matches
function IncrMatchcount(d) {
      ++matchcount;
      durcount += dur(d);
      min_match = Math.min(min_match, dur(d));
      max_match = Math.max(max_match, dur(d));
}

// Show the numeric results
function ShowMatchcount() {
  var minmax_range = "";
  if (0 < matchcount) {
    minmax_range = " [" + shortnum4(min_match) + ".." +  shortnum4(max_match) + "]";
  }
  document.getElementById("matchcount").innerHTML =
    "Matches: " + matchcount + ", " + shortnum5(durcount) + minmax_range;
}



// Just look at start time of each item in annotated_d
// Linear search
function FindNearestAnnotated(t) {  
  var nearest = -1;
  var nearestdist = 0;
  // Ignore y for a match, ignore duration  
//console.log("annotated_d.length=", state.annotated_d.length);
  for (j = 0; j < state.annotated_d.length; ++j) {
    var i = state.annotated_d[j];
    var d = data.events[i];
    var dist = Math.abs(ts(d) - t);
//console.log("  ", i, ts(d), t, dist); 
    if ((nearest < 0) || (dist < nearestdist)) {
      nearest = i;
      nearestdist = dist;
    }
  }
//console.log("  =", nearest); 
  return nearest;
}


function initializeState() {
console.log("initializeState");
  // If book is true, make these UI changes at startup:
  //   Show secondary UI
  //   Use short-line annotations
  //   Make arcs bold
  //   Suppress frequency overlay
  //   Make y-axis chars smaller
  //   Make axis font larger
  //   Set spn:txt to 3:2
  //   Use dot instead of group down arrow (typesetting issue)
  if (book) {
    document.getElementById("debugtext").hidden = false;
    debug = true;
  }

  state2.annottype_value = book ? 1 : 0;
  state2.annotateuser_value = 0;
  state2.annotateall_value = 0;
  if (state2.ipc_value != -1) {state2.ipc_value = 0;}
  state2.marks_value = 3;
  state2.arcs_value = book ? 1 : 2;
  state2.waits_value = 1;
  if (state2.freq_value != -1) {state2.freq_value = book ? 0 : 2;}
  state2.ovhd_value = 1;
  if (state2.lock_value != -1) {state2.lock_value = 2;}
  state2.cb_value = 0;
  state2.hack_value = 0;
  aspecth = 0;
  aspectv = 0;
  state2.samp_value = 0;
  state2.searchnot_value = 0;
  state2.search_text = "";
  state2.usec_lo_time = 0;
  state2.usec_hi_time = 0;
  ////state.textmultiplier_value = 0;
  state2.basetime0_value = 0;
  state2.xleft_ts = 0;

  state2.xright_ts = 0;
  state2.ytop_track = 0;
  state2.ybot_track = 0;

  state2.groupcompress = [2, 0, 0, 0];	// Show CPUs, collapse others
  state2.grouplabelypos = [0, 0, 0, 0],	// In pixels 
  state2.groupfirsttrack = [0, 0, 0, 0],	// Label track number  
  state2.grouphashilite = [0, 0, 0, 0];

  state2.savedtransformX = d3.zoomIdentity;
  state2.rangeX = 10;				// Arbitrary. Updated in setInitialRangeDomain
  state2.savedtransformY = d3.zoomIdentity;
  state2.rangeY = 10;				// Arbitrary. Updated in setInitialRangeDomain

////  state.hilite_event_array = [];
  hilite_event_set.clear();
  state2.rowToHilite.fill(false);

  state.annotated_d = [];
  state2.annotated_one_d = -1;
  state.hilite_evnum = 0;
  state.hilite_evnum2 = 0;

  state2.minmax_units_value = 1;		// usec
  state.just_reset = true;

  // Secondary menu
  legend_value = 0;
  fade_value = 0;
  textsize.region2chars = book ? 14 : 18;
  textsize.region2px    = book ? 28 : 18;
  textsize.spn          = book ? 3 : 3;
  textsize.txt          = book ? 2 : 3;
  //---------------------------- end init of state

  // Saved states become invalid with new data
  //   savedstate[i] is now data.savedview[i][0]
  //   saved_valid[i] is now (data.savedview[i][1] !== null)

  more_text = false;
  document.getElementById("moretext").style.display = "none";
  document.getElementById("minmax_units").innerHTML = "&#x00b5;sec:";	// default usec

  // Set up extra margin space within window for some browsers
  var thisbrowser = whichBrowser();
console.log("browser:", thisbrowser);
  if (thisbrowser == "Chrome") {
    winMargin2 = {top:40, top2x: 60, bottom:50, bottom2x: 70, 
                  left:15, right:15, width1: 1100};	
  }
  if (thisbrowser == "Safari") {
    winMargin2 = {top:40, top2x: 80, bottom:50, bottom2x: 80, 
                  left:20, right:20, width1:1400};	
  }
  if (thisbrowser == "Firefox") {
    winMargin2 = {top:40, top2x: 80, bottom:50, bottom2x: 80, 
                  left:20, right:20, width1:950};	
  }


  //
  // Design: highlight a row shows it in color and its kernel/user in color on other rows
  //  this covers all events in that row, not just those onscreen.
  //  all other rows are marked to show full height even though they contain some gray
  //  shift-click to add a highlighted row simply adds to colored events and to
  //  full-height rows.
  //  nothing special happens for search, annotate, etc.
  //  no events are deleted in any rows, just their colors change -- maybe
  //  a given row can be on one of several states:
  //  - normal, no rows highlighted. all rows in full-height color
  //  - highlighted row. all events in full-height color
  //    row label is shown in bold
  //  - non-highlighted row with some highlighted events. full height, mixed color/gray
  //    (highlighted events might be offscreen)
  //  - non-highlighted row with no highlighted events. partial height all gray.
  //  This last, only, might be better done with no annotes, no markers, and no ipc,

}


// Accessor functions for 10-element events
// ts           dur       CPU tid  rpc event arg0 ret ipc  name
function ts(d)  {return d[0];}
function tsrel(d)  {return d[0] - xaxisfmt.basetime;}		// Relative to basetime
function tsrel_0(d)  {return d[0] - xaxisfmt.basetime;}		// Relative to rowToMinT
function dur(d) {return d[1];}
function cpu(d) {return d[2];}
function pid(d) {return d[3];}	// Actually thread ID, but called pid in kernel
function tid(d) {return d[3];}
function rpc(d) {return d[4];}
function ev(d)  {return d[5];}
function arg0(d) {return d[6];}
function cpu2(d) {return d[6];}
function msgsize(d) {return d[6];}
function delta_x(d) {return d[6];}
function ret(d) {return d[7];}
function pid2(d) {return d[7];}
function delta_row(d) {return d[7];}
function ipc(d) {return d[8];}
function name(d) {return d[9];}
// placeholder for now
function res(d) {return -1;}

// Fetch IPC, from packed two-bit IPC&LLC if both
// Top 2 bits are IPC: 0 4 8 12. Map into 1 5 9 13
function realipc(d) {return has_both ? (ipc(d) & 0x0c) + 1 : ipc(d);}

// Fetch LLC, from packed two-bit IPC&LLC if both
//// Bottom twobits are LLC: 0 1 2 3. Map into 2 6 10 14
// Bottom two bits are LLC: 0 1 2 3. Map into 0 2 5 8
const kLlcSmall = [0,2,5,8];
function realllc(d) {return has_both ? kLlcSmall[ipc(d) & 0x03] : ipc(d);}

function tsend(d) {return d[0] + d[1];}
function tsrelend(d) {return (d[0] - xaxisfmt.basetime) + d[1];}

function set_ts(d, v) {d[0] = v;}
function set_dur(d, v) {d[1] = v;}
function set_cpu(d, v) {d[2] = v;}
function set_pid(d, v) {d[3] = v;}
function set_rpc(d, v) {d[4] = v;}
function set_ev(d, v)  {d[5] = v;}
function set_arg0(d, v)    {d[6] = v;}
function set_delta_x(d, v) {d[6] = v;}
function set_ret(d, v)       {d[7] = v;}
function set_delta_row(d, v) {d[7] = v;}
function set_ipc(d, v) {d[8] = v;}
function set_name(d, v) {d[9] = v;}

// Boolean functions to decompose events
function is_idle(d)    {return ev(d) == idlebase;}
function is_c_exit(d)  {return ev(d) == cexitbase;}
function is_idle_c_exit(d)  {return (ev(d) == idlebase) || (ev(d) == cexitbase);}

function is_special(d) {return (ev(d) & 0xfffc0) == 0x00200;}		// 200..23F
function is_extra(d)   {return (ev(d) & 0xfffc0) == 0x00280;}		// 280..2BF
function is_rpc(d)     {return (0x0201 <= ev(d)) && (ev(d) <= 0x0205);}
function is_rpcdef(d)  {return (0x0201 <= ev(d)) && (ev(d) <= 0x0203);} // KUTRACE_RPCIDREQ KUTRACE_RPCIDRESP KUTRACE_RPCIDMID
function is_rpcpkt(d)  {return (msgrxbase <= ev(d)) && (ev(d) <= msgtxbase);}	// KUTRACE_RPCIDRXMSG KUTRACE_RPCIDTXMSG
function is_rpcrxpkt(d)  {return (ev(d) == msgrxbase);}			// KUTRACE_RPCIDRXMSG 
function is_rpctxpkt(d)  {return (ev(d) == msgtxbase);}			// KUTRACE_RPCIDTXMSG 
function is_mwait(d)   {return (ev(d) == 0x0208);}			// KUTRACE_MWAIT
function is_pstate(d)  {return (ev(d) == 0x209) || (ev(d) == 0x21C);}	// KUTRACE_PSTATE,PSTATE2 (frequency)
function is_mark(d)    {return (0x020a <= ev(d)) && (ev(d) <= 0x020d);}	// KUTRACE_MARKA..KUTRACE_MARKD
function is_lr_mark(d) {return (ev(d) & ~1) == 0x20E;}  		// KUTRACE_LEFTMARK and KUTRACE_RIGHTMARK
function is_lock(d)    {return (0x0210 <= ev(d)) && (ev(d) <= 0x0212);}	// KUTRACE_LOCKNOACQUIRE..KUTRACE_LOCKWAKEUP
function is_pkt(d)     {return (pktrxbase <= ev(d)) && (ev(d) <= pkttxbase);}     // KUTRACE_RX_PKT or KUTRACE_TX_PKT
function is_pktrx(d)   {return (ev(d) == pktrxbase);}			// KUTRACE_RX_PKT
function is_pkttx(d)   {return (ev(d) == pkttxbase);}			// KUTRACE_TX_PKT
function is_userrxtx(d)  {return (userrxbase <= ev(d)) && (ev(d) <= usertxbase);} // KUTRACE_RX_USER or KUTRACE_TX_USER
function is_pc_samp(d) {return ((ev(d) & ~1) == 0x280);}		// KUTRACE_PC_U and KUTRACE_PC_K
function is_lock_line(d) {return ((ev(d) & ~1) == 0x282);}		// KUTRACE_LOCK_HELD KUTRACE_LOCK_TRY
function is_lock_dots(d) {return (ev(d) == 0x283);}			// KUTRACE_LOCK_TRY 
function is_wait(d)    {return (ev(d) & 0xfffe0) == waitbase;}  	// Waiting, 32 codes
function is_queued(d)  {return (ev(d) == queued);}  			// RPC Waiting in a queue
function is_enq_deq(d) {return ((ev(d) & ~1) == queued);}
function is_kernel(d)  {return (ev(d) & 0xff000) == 0x00000 && 
  (!is_special(d)) && (!is_extra(d)) && (!is_wait(d)) && (!is_queued(d));}
function is_fault(d)   {return (ev(d) & 0xfff00) == faultbase;}  
function is_irq(d)     {return (ev(d) & 0xfff00) == irqbase;}  
// Match both syscall64 ansd syscall32
function is_syscall(d) {return (ev(d) & 0xffa00) == syscallbase;}  
function is_user(d)    {return (ev(d) & 0xf0000) == userbase && !is_idle(d);}
function is_useroridle(d)    {return (ev(d) & 0xf0000) == userbase;}
function is_kerneloruser(d)  {return is_kernel(d) || is_user(d);}
function is_kernelusermark(d)  {return is_kernel(d) || is_user(d) || is_mark(d);}
function is_executable(d)  {return (0x400 <= ev(d));}
function is_kui(d) {return is_kernel(d) || is_user(d) || is_idle(d) || is_c_exit(d) || is_wait(d) || is_queued(d);}

function is_bottom_half(d)  {return ev(d) == 0x5FF;}		// Overloads irqret 255
// These can masquerade as kernel
function is_sched_syscall(d) {return (ev(d) == schedbase) || (ev(d) == schedbase2);} 	// Overloads syscall64 511,1535 
function is_rxtx(d) {return ev(d) == rxtxbase;}  	// overloads sysret32 510
function is_ovhd(d) {return ev(d) == ovhdbase;}    	// overloads sysret32 511


// Overlays are manually added markup
// Except arcs are becoming first-class citizens 2019.03.11
function is_bracket(d) {return ev(d) == -1;}
function is_oval(d) {return ev(d) == -2;}
function is_arc(d) {return ev(d) == -3;}
function is_ipi(d) {return ev(d) == -7;}
function is_callout(d) {return ev(d) == -4;}
function is_annot(d) {return ev(d) == annotbase;}
function is_dots(d) {return ev(d) == dotsbase;}
function is_overlay(d) {return (ev(d) < 0) && !is_arc(d);}
function is_any_overlay(d) {return (ev(d) < 0);}

function is_mark_a(d)    {return (ev(d) == 0x020A);}
function is_mark_b(d)    {return (ev(d) == 0x020B);}
function is_mark_c(d)    {return (ev(d) == 0x020C);}
function is_mark_d(d)    {return (ev(d) == 0x020D);}

 
function is_idle_ev(ev)    {return ev == 0x10000;}
function is_c_exit_ev(ev)  {return ev == 0x20000;}
function is_special_ev(ev) {return (ev & 0xfffc0) == 0x00200;}
function is_extra_ev(ev)   {return (ev & 0xfffc0) == 0x00280;}		// 280..2BF
function is_mark_ev(ev)    {return (0x020A <= ev) && (ev <= 0x020D);}
function is_pstate_ev(ev)  {return (ev == 0x209) || (ev == 0x21C);}
function is_pc_samp_ev(ev) {return ((ev & ~1) == 0x280);}
function is_lock_line_ev(ev) {return (ev == 0x282);}
function is_wait_ev(ev)    {return (ev & 0xfffe0) == 0x00300;}			// 32 codes
function is_queued_ev(ev)  {return (ev == queued);}
function is_kernel_ev(ev)  {return (ev & 0xff000) == 0x00000 && 
  (!is_special_ev(ev)) && (!is_extra(ev)) && (!is_wait_ev(ev)) && (!is_queued_ev(ev));}
function is_fault_ev(ev)   {return (ev & 0xfff00) == 0x00400;}  
function is_irq_ev(ev)     {return (ev & 0xfff00) == 0x00500;}  
// Match both syscall64 ansd syscall32
function is_syscall_ev(ev) {return (ev & 0xffa00) == 0x00800;}  
function is_user_ev(ev)    {return (ev & 0xf0000) == 0x10000 && !is_idle_ev(ev);}
function is_useroridle_ev(ev)   {return (ev & 0xf0000) == 0x10000;}
function is_kerneloruser_ev(ev) {return is_kernel_ev(ev) || is_user_ev(ev);}
function is_kernelusermark_ev(ev) {return is_kernel_ev(ev) || is_user_ev(ev) || is_mark_ev(ev);}

function is_bottom_half_ev(ev)  {return ev == 0x5FF;}

function is_bracket_ev(ev) {return ev == -1;}
function is_oval_ev(ev) {return ev == -2;}
function is_arc_ev(ev) {return ev == -3;}
function is_ipi_ev(ev) {return ev == -7;}
function is_callout_ev(ev) {return ev == -4;}
function is_overlay_ev(ev) {return (ev < 0) && !is_arc_ev(ev);}
function is_any_overlay_ev(ev) {return (ev < 0);}

// True if mark text starts with slash
function is_mark_close(d) {
  var marktext = Base40ToChar(arg0(d));
  return (marktext.substr(0,1) == "/");
}

// Allow marks of different types at same pixel, but not duplicates of same type
// Returns a bit mask value
function get_mark_type(d) {
  var ty = 0;
  if (is_mark_a(d)) {ty = 1;}
  if (is_mark_b(d)) {ty = 2;}
  if (is_mark_c(d)) {ty = 4;}
  if (is_mark_d(d)) {ty = 8;}
  // Closing marks get types 16/32/64
  if (!is_mark_d(d) && (is_mark_close(d))) {ty <<= 4;}
  return ty;
}

// Function to parse the date / time
var parseTime = d3.timeParse("%d-%b-%y");
 
 
 
// Watcher for mouse wheel, mouse pan within svgdotg
// When activated, changes translate/scale of view (which changes drawing), 
// resets scales x and y, reattaches to xAxis and yAxis, redraws groups gX and gXgrid
 
// Forward map pre-zoomed pixels to pan/zoom client pixels
function panzoomX(x, transform) {
  return ((x * transform.k + transform.x));
}
function panzoomY(y, transform) {
  return ((y * transform.k + transform.y));
}
 
// Backmap mouse client pixels to pre-zoomed pixels
function panzoomInvertX(x, transform) {
  return ((x - transform.x) / transform.k);
}
function panzoomInvertY(y, transform) {
  return ((y - transform.y) / transform.k);
}
 
// x in seconds to h:m:ss.fraction
// pass in secfmt = "02d" or "06.3f" or "09.6f" sec/msec/usec
function hhmmss(x, secfmt) {
  var xt = Math.trunc(x);			// Integer seconds
  var hh = Math.trunc(xt / 3600) % 60;
  var mm = Math.trunc(xt / 60) % 60;
  // var ss =  Math.trunc(xt) % 60; 
  var xfsec = x - (hh * 3600) - (mm * 60);
  return d3.format("02d")(hh) + ":" + 
    d3.format("02d")(mm) + ":" + 
    d3.format(secfmt)(xfsec);
}
 
// Fancier thousands scaling, 3 digits
function shortnum(x, thousands) {
  var x0 = x;
  var k = 0;
  if (x == 0) {return d3.format(".0f")(x);}
  while (Math.abs(x0) >= thousands && k < 4) {x0 = x0 / thousands; k = k + 1;}
  while (Math.abs(x0) < 1 && k > -4) {x0 = x0 * thousands; k = k - 1;}
  // Now we have 1 <= x0 < thousands (which is expected to be 1000 or 1024),
  // if between roughly 10**-12 and 10**12
  var fmt = ".0f";
  if (x0 < 99.99) {fmt = ".1f";}
  if (x0 < 9.999) {fmt = ".2f";}
  if (x0 < 0.9999) {fmt = ".3f";}
  // Note space if no units scaling letter
  return d3.format(fmt)(x0) + "pnum KMGT"[k+4];
}

// Self-contained thousands scaling, 5 digits of time
function shortnum5(x) {
  var x0 = x;
  var k = 0;
  if (x <= 0) {return d3.format(".0f")(x);}
  while (Math.abs(x0) >= (2000) && k < 4) {x0 = x0 / 1000; k = k + 1;}
  while (Math.abs(x0) < 2 && k > -4) {x0 = x0 * 1000; k = k - 1;}
  // Now we have 2 <= x0 < 2000 
  var fmt = ".1f";
  if (x0 < 999.9) {fmt = ".2f";}
  if (x0 < 99.99) {fmt = ".3f";}
  if (x0 < 9.999) {fmt = ".4f";}
  // Note space if no units scaling letter
  return d3.format(fmt)(x0) + "pnum KMGT"[k+4] + "s";
}

// Self-contained thousands scaling, 4 digits of time
function shortnum4(x) {
  var x0 = x;
  var k = 0;
  if (x <= 0) {return d3.format(".0f")(x);}
  while (Math.abs(x0) >= (2000) && k < 4) {x0 = x0 / 1000; k = k + 1;}
  while (Math.abs(x0) < 2 && k > -4) {x0 = x0 * 1000; k = k - 1;}
  // Now we have 2 <= x0 < 2000 
  var fmt = ".0f";
  if (x0 < 999.9) {fmt = ".1f";}
  // Note space if no units scaling letter
  return d3.format(fmt)(x0) + "pnum KMGT"[k+4] + "s";
}

// Globals for doing alternate X-axis tick labels only
var ticknumber = 1;
var tickalternating = false;

// Input is a ts in seconds for labelling an X-axis tic mark
// Uses global basetime and xaxisfmt 
// Formats the delta from basetime
function shortxtime(x) {
//console.log("shortxtime", x, ticknumber);
  ++ticknumber;
  if (tickalternating && (ticknumber & 1)) {return("");}

  if (xaxisfmt.deltamul == 0) {
    // Turn seconds into hh:mm
    var delta = Rnd0(x - xaxisfmt.basetime);
    var mm = Math.trunc(delta / 60);
    var ss = Math.trunc(delta) % 60;
    return (showovfl ? d3.format(xaxisfmt.ovflformat)(xaxisfmt.ovflnext) + ":" : "") + 
      d3.format("02d")(mm) + ":" + d3.format("02d")(ss);
  }
  var delta = Rnd1((x - xaxisfmt.basetime) * xaxisfmt.deltamul);
  var showovfl = (xaxisfmt.deltamax == delta);
  if (xaxisfmt.deltamax <= delta) {delta -= xaxisfmt.deltamax;}
  return (showovfl ? d3.format(xaxisfmt.ovflformat)(xaxisfmt.ovflnext) + "_" : "") + 
    d3.format(xaxisfmt.deltaformat)(delta);
}
 

// Reset rotating label positions per row
function resetRotatingLabelk() {
  for (var i = 0; i < sortNumSize; ++i) {rowToLabelk[i] = 0;}
}

function resetlines() {
  d3.selectAll(".verticalline").remove() ;
  d3.selectAll(".verticaldashline").remove() ;
  d3.selectAll(".verticalshortline").remove() ;
  d3.selectAll(".markertext").remove() ;
  d3.selectAll(".rubberline").remove() ;
  d3.selectAll(".rubberbackground").remove() ;
  d3.selectAll(".rubbertext").remove() ;

  dont_annotate = [];
  dont_annotate[0x10000] = true;	// Never label -idle- event
  // clear matches
  document.getElementById("matchcount").innerHTML = "";
  redrawUI();
}


function resetevents() {
  d3.selectAll(".graphlinex").remove();
  d3.selectAll(".ipcmark").remove();
  d3.selectAll(".markmark").remove();
  d3.selectAll(".extra").remove();
}

function resetipc() {
  d3.selectAll(".ipcmark").remove();
}

function resetmark() {
  d3.selectAll(".markmark").remove();
}

// Look at the displayed x-axis extent, backmapping to data values
// via x_zoomed scaler
function resetonscreen_x() {
//console.log("resetonscreen_x");
  // Initialize the left and right time values
  var xleft = x_zoomed.invert(axesmargin); 
  var xright = x_zoomed.invert(region3width - axesmargin); 
  var xrightmost = x_zoomed.invert(region3clipwidth);
  reset_globals_x(xleft, xright, xrightmost);
}

// Look at the displayed y-axis extent, backmapping to data values
// via y_zoomed scaler
function resetonscreen_y() {
//console.log("resetonscreen_y");
  // Initialize the top and bottom track values
  var ytop = Rnd0(y_zoomed.invert(0)); 
  var ybottom = Rnd0(y_zoomed.invert(region234height)); 
  reset_globals_y(ytop, ybottom);
}

// Use right (not rightmost) to auto-prune at X-axis edge, no slop
function rangeonscreen_x(lo, hi) {
  if (0 < state2.basetime0_value) {return true;}	// Rel0 always onscreen
  return (lo <= realxright && realxleft <= hi);
}

// Includes right gray margin 
function onscreen_x(d) {	// Allow slop
  // TEMP: For old RPCs, consider onscreen even if they start and end 9 msec earlier
  // Enough for a megabyte message at 1Gb/s
  if (is_rpcpkt(d) && (dur(d) <= 0.00000001)) {
    return ((ts(d) <= realxrightmost) && ((realxleft - 0.009) <= tsend(d)));
  }
  return ((ts(d) <= realxrightmost) && (realxleft <= tsend(d)));
}
function startsonscreen_x(d) {	// Allow slop
  // PC samples use end, not start
  if (is_pc_samp(d)) {return ((tsend(d) <= realxrightmost) && (realxleft <= tsend(d)));}
  return ((ts(d) <= realxrightmost) && (realxleft <= ts(d)));
}

// Excludes right gray margin
function strictly_onscreen_x(d) {	// No slop
  // PC samples use end, not start
  if (is_pc_samp(d)) {return ((tsend(d) <= realxright) && (realxleft <= tsend(d)));}
  return ((ts(d) <= realxright) && (realxleft <= tsend(d)));
}

function strictly_startsonscreen_x(d) {	// No slop
  // PC samples use end, not start
  if (is_pc_samp(d)) {return ((tsend(d) <= realxright) && (realxleft <= tsend(d)));}
  return ((ts(d) <= realxright) && (realxleft <= ts(d)));
}

function onscreen_ytrack(track, height) {
  return ((track <= realybottom) && (realytop <= (track + height)));
}

// Returns true based on CPU number. cpu < 0 is always false
function onscreen_y(d) {
  if (cpu(d) < 0) {return false;}
  var row = eventToRow[prefixCpu * 65536 + low16of(cpu(d))];
  var track = rowToTrack[row];
  var t_height = rowToHeight[row];
  return onscreen_ytrack(track + t_height / 4, 0);
}

// Returns true based on CPU or PID or RPC. 
// NO. Overlays, cpu < 0, are always true (wait_xxx, etc.)
function onscreen_all_y(d) {
  // Always do CPUs
  var row = eventToRow[prefixCpu * 65536 + low16of(cpu(d))];
  var track = rowToTrack[row];
  var t_height = rowToHeight[row];
  if ((0 < t_height) && onscreen_ytrack(track + t_height / 4, 0)) {return true;}

  if (0 < pid(d)) {
    row = eventToRow[prefixPid * 65536 + low16of(pid(d))];
    track = rowToTrack[row];
    t_height = rowToHeight[row];
    if ((0 < t_height) && onscreen_ytrack(track + t_height / 4, 0)) {return true;}
  }

  if (0 < rpc(d)) {
    row = eventToRow[prefixRpc * 65536 + low16of(rpc(d))];
    track = rowToTrack[row];
    t_height = rowToHeight[row];
    if ((0 < t_height) && onscreen_ytrack(track + t_height / 4, 0)) {return true;}
  }

  return false;
}


// Truncate to 0.5 unit
function Trunc5(x) {return Math.trunc(x * 2) / 2;}

// Round to zero decimal places
function Rnd0(x) {return Math.round(x);}

// Round to 0.2 unit
function Rnd2(x) {return Math.round(x * 5) / 5;}

// Round to 0.5 unit
function Rnd5(x) {return Math.round(x * 2) / 2;}

// Round to one decimal place
function Rnd1(x) {return Math.round(x * 10) / 10;}

// Round to two decimal places
function Rnd2d(x) {return Math.round(x * 100) / 100;}

// Round to multiple of 10
function Rnd10(x) {return Math.round(x / 10) * 10;}


// Round timestamp seconds to nearest microsecond
function RndUsec(x) {return Math.round(x * 1000000) / 1000000;}

// Input is a relative time, output is an absolute pixel position
function AbsPxX(tsrel) {return panzoomX(x(tsrel), state2.savedtransformX);}

// Input is a cpu number, output is an absolute pixel position
function AbsPxY(cpu) {return y(cpu);}

// Input is a relative time, output is an absolute pixel position, rounded to 1/10 pixel
function AbsPxRX(tsrel) {return Rnd1(AbsPxX(tsrel));}

// Input is a cpu number, output is an absolute pixel position, rounded to 1/10 pixel
function AbsPxRY(cpu) {return Rnd1(AbsPxY(cpu));}


// Unpack six characters from 32 bits. 
function Base40ToChar(base40) {
  var str = "";
  // Want uint32 but have int32: Simple code fails if arg is negative...
  var tempbase40 = (base40 >> 1) & 0x7fffffff;	// Positive, low bit missing
  var tempn40 = tempbase40 % 20;
  var c = kFromBase40.substr(tempn40 * 2 + (base40 & 1), 1);	// Puts back in low bit
  str += c;
  base40 = Math.floor(tempbase40 / 20);

  // First character went in last, comes out first
  while (base40 > 0) {
    var n40 = base40 % 40;
    var c = kFromBase40.substr(n40, 1);
    str += c;
    base40 = Math.floor(base40 / 40);
  }
  return str;
}


// Draw an IPC or LLC triangle
// Used for timespans, but also for drawing legends
// If ipcd is >=0, draw as IPC, else draw as LLC of ipcd+16
function draw_ipcmark(dotg, centerx, centery, size, ipcd, legend) {
  var ipcllcd = ipcd;
  if (ipcd < 0) {ipcllcd += 16;} 
  var xx = -size;
  var yy = size / 5;
  var offset = size * 3 / 4;
  var strokewidth = Rnd1(size / 20);
  var rad = Math.PI - ((ipcllcd * Math.PI) / 16);	// Speedometer angle
  var sinangle = Math.sin(rad);
  var cosangle = Math.cos(rad);
  var path;
  // Offset and Rotate the center
  centerx2 = centerx + (offset * cosangle);
  centery2 = centery + (offset * -sinangle);
  if ((ipcllcd & 1) == 0) {
    // draw plain triangle
    path = "M" + (centerx2) + "," + (centery2) + 
               " L" + (centerx2 + (xx * cosangle) + (yy * -sinangle)) + "," +
               (centery2 - ((xx * sinangle) + (yy * cosangle))) +
               " L" + (centerx2 + (xx * cosangle) + (-yy * -sinangle)) + "," + 
               (centery2 - ((xx * sinangle) + (-yy * cosangle))) +
               " L" + (centerx2) +"," + (centery2);
  } else {
    // draw with notch
    path = "M" + (centerx2) + "," + (centery2) + 
               " L" + (centerx2 + (xx * cosangle) + (yy * -sinangle)) +"," +
               (centery2 - ((xx * sinangle) + (yy * cosangle))) +
               " L" + (centerx) +"," + (centery) +
               " L" + (centerx2 + (xx * cosangle) + (-yy * -sinangle)) +"," +
               (centery2 - ((xx * sinangle) + (-yy * cosangle))) +
               " L" + (centerx2) +"," + (centery2);
  }
  dotg.append("path")
    .attr("class", legend ? "ipcmarkl" :"ipcmark")
    .attr("d",  path)
    .attr("fill", (ipcd < 0) ? "#FFC080" : "#FFFFFF")	// white (orangle llc)
    .attr("stroke-width", strokewidth)	
    .attr("stroke", ipc_color[ipcllcd >> 2]);	// gray/blue/red
}

// Rough duration of memory activity  (seconds)
function memdur(ipcd) {
   return kLlcNsec[ipcd] / 1000000000.0;
}

// Draw a line centered at centerx roughly indicating memory-bus busy
// Note that 5th arg is event d, not just its 4-bit IPC/LLC value
function draw_llcmarks(dotg, centerx, centery, size, d, legend) {
  var ipcd = realllc(d);

  // Draw line underneath the timespan
  // Rough second duration of memory activity, but <= the timespan duration - 60ns
  var duration = memdur(ipcd);
  var toobig = false;
  if (dur(d) < duration) {
    duration = dur(d) - 0.000000060;
    toobig = true;
  }

  // Put in small speedometer triangle. ipcd-16 marks as LLC colors, not IPC
  draw_ipcmark(dotg, centerx, centery, size * 0.75, ipcd-16, legend);

  // seconds duration to pixels
  var llcpx = Rnd5(duration / xsecperpix);
  if (llcpx < 2) {return;}
  
  var offset = size * 0.833;	// Draw line at 5/6 of row size below centerline 
  var strokewidth = Rnd1(size / 20);
  path = "M" + (centerx - llcpx/2) + "," + (centery + offset) + " h" + (llcpx);
  dotg.append("path")
    .attr("class", legend ? "ipcmarkl" :"ipcmark")
    .attr("d",  path)
    .attr("stroke-width", strokewidth)	
    .attr("stroke", toobig ? "#FF0080" : "#FF8000");	// orange (red if too big)
      
}


function draw_emptylegend(lx, ly, lheight, lwidth) {
  d3.selectAll(".ipclegend").remove();
  d3.selectAll(".ipcmarkl").remove();
  svgdotg.append("rect")
    .attr("class", "ipclegend")
    .attr("x", lx)
    .attr("y", ly)
    .attr("height", lheight)
    .attr("width", lwidth)
    .attr("style", "fill:rgb(224,224,224); opacity:0.25");
}


function draw_ipcllclegend(lx, ly, lheight, lwidth) {
  d3.selectAll(".ipclegend").remove();
  svgdotg.append("rect")
    .attr("class", "ipclegend")
    .attr("x", lx)
    .attr("y", ly)
    .attr("height", lheight)
    .attr("width", lwidth)
    .attr("style", "fill:rgb(224,224,224); opacity:0.875");

  //var centerx = lx + 12;
  //var centery = ly + 10;
  var centerx = lx + 0.25 * lwidth;
  var centery = ly + 0.025 * lheight;
  ////var txtmul = textmultiplier_tbl[state.textmultiplier_value];
  var txtmul = 1.0;

  // Draw combined IPC and LLC legend
  // When combined, there are just 2 bits for IPC and two for LLC, packed into 4 bits
  if (has_both) {
    svgdotg.append("text")
      .attr("class", "ipclegend")
      .attr("transform", "translate(" + (centerx + 8) + "," + 
                                        (centery + 2) + ")scale(1,1)")
      .attr("font-size", textsize.smalltick + "px")
      .attr("fill", "black")
      .attr("font-family", "sans-serif")
      .style("text-anchor", "left")
      .text("IPC");

    // Only draw four IPC labels: 1, 5, 9, 13
    for (var i = 0; i < 4; ++i) {
      var fulli = i*4 + 1;
      var centerx_i = centerx + ipctextoffset[i >> 2] + 10;
      var centery_i = centery + (i + (i >> 2) + 1) * lheight / 20;
      draw_ipcmark(svgdotg, centerx, centery_i, 20 * txtmul, fulli, true);

      svgdotg.append("text")
        .attr("class", "ipclegend")
        .attr("transform", "translate(" + (centerx_i) + "," + 
                                      (centery_i + 2) + ")scale(1,1)")
        .attr("font-size", textsize.smalltick + "px")
        .attr("fill", "black")
        .attr("font-family", "sans-serif")
        .style("text-anchor", "left")
        .text(ipcvalues[fulli]);
    }

    //// Now draw four LLC labels: 2, 6, 10, 14
    // Now draw four LLC labels: 0, 1, 3, 7
    var lowery =  centery + (4 + (4 >> 2) + 1) * lheight / 20;
    svgdotg.append("text")
      .attr("class", "ipclegend")
      .attr("transform", "translate(" + (centerx + 8) + "," + 
                                        (lowery + 2) + ")scale(1,1)")
      .attr("font-size", textsize.smalltick + "px")
      .attr("fill", "black")
      .attr("font-family", "sans-serif")
      .style("text-anchor", "left")
      .text("Miss");

    for (var i = 0; i < 4; ++i) {
      var fulli = kLlcSmall[i];
      var centerx_i = centerx + ipctextoffset[i >> 2] + 10;
      var centery_i = lowery + (i + (i >> 2) + 1) * lheight / 20;
      draw_ipcmark(svgdotg, centerx, centery_i, 20 * txtmul, fulli-16, true);

      svgdotg.append("text")
        .attr("class", "ipclegend")
        .attr("transform", "translate(" + (centerx_i) + "," + 
                                      (centery_i + 2) + ")scale(1,1)")
        .attr("font-size", textsize.smalltick + "px")
        .attr("fill", "black")
        .attr("font-family", "sans-serif")
        .style("text-anchor", "left")
        .text(llcvalues[fulli]);
    }


  return;
  }

  // Draw either IPC or LLC legend
  var doing_llc = has_llc & !has_ipc;
  svgdotg.append("text")
    .attr("class", "ipclegend")
    .attr("transform", "translate(" + (centerx + 8) + "," + 
                                      (centery + 2) + ")scale(1,1)")
    .attr("font-size", textsize.smalltick + "px")
    .attr("fill", "black")
    .attr("font-family", "sans-serif")
    .style("text-anchor", "left")
    .text(doing_llc ? "Miss" : "IPC");

  var iincr = (txtmul >= 1.5) ? 2 : 1;

  for (var i = 0; i < 16; i += iincr) {
    var centerx_i = centerx + ipctextoffset[i >> 2] + 10;
    var centery_i = centery + (i + (i >> 2) + 1) * lheight / 20;
    draw_ipcmark(svgdotg, centerx, centery_i, 20 * txtmul, doing_llc ? i-16 : i, true);

    svgdotg.append("text")
      .attr("class", "ipclegend")
      .attr("transform", "translate(" + (centerx_i) + "," + 
                                    (centery_i + 2) + ")scale(1,1)")
      .attr("font-size", textsize.smalltick + "px")
      .attr("fill", "black")
      .attr("font-family", "sans-serif")
      .style("text-anchor", "left")
      .text(doing_llc ? llcvalues[i] : ipcvalues[i]);
  }
}




// Add relative drawing ops to make a
function HexBox(txwidth2, txheight2) {
  var box = 
    " l" + (txwidth2) + ",2" + 
    " l0," + (txheight2 * 2) +
    " l" + (-txwidth2) + ",2" + 
    " l" + (-txwidth2) + ",-2" + 
    " l0," + (txheight2 * -2) +
    " l" + (txwidth2) + ",-2"; 
  return box;
}


function draw_mark_abcd(d, x0, y0, rel_y0, marktext, marktextsize, skiptext) {
  var markcolor;
  var marktext;
  var alignleft = false;
  var alignright = false;
  var aligndown = false;
  if (is_mark_a(d)) {
    // mark_a comes in red left-right pairs, right if text starts with slash
    markcolor = "#FF0000";  // red
    if (marktext.substr(0,1) == "/") {alignright = true;}
    else {alignleft = true;}
  }
  if (is_mark_b(d)) {
    // mark_b comes in lowered blue left-right pairs, right if text starts with slash
    markcolor = "#0000FF";  // blue
    aligndown = true;
    if (marktext.substr(0,1) == "/") {alignright = true;}
    else {alignleft = true;}
  }
  if (is_mark_c(d)) {
    // mark_c comes in centered green 
    markcolor = "#008000";  //  dark green
  }
  if (is_mark_d(d)) {
    // mark_d comes in numbered centered black 
    markcolor = "#000000";  // black
  }
        
  var txwidth2;
  var txheight2;
  var markpath;
  if (is_mark_d(d)) {
    // Bare text with narrow triangle
    txwidth2 = Rnd1((marktext.length) * marktextsize * 0.6 / 2);
    markpath = "M" + x0 + "," + y0 + " l-2," + (rel_y0 + 2) +
      " l4,0";
  } else {
    // Boxed text with line
    txwidth2 = Rnd1((1 + marktext.length) * marktextsize * 0.6 / 2);
    txheight2 = Rnd1(marktextsize * 1.2 / 2);
    markpath = "M" + x0 + "," + y0 + " l0," + rel_y0;
    if (aligndown) {
      y0 += Rnd1(marktextsize); 
      markpath += " l0," + Rnd1(marktextsize);
    }
    if (alignleft) {x0 += txwidth2; markpath += " l0,2 m" + (txwidth2) + ",-2";}
    if (alignright) {x0 -= txwidth2; markpath += " l0,2 m" + (-txwidth2) + ",-2";}
    // Add hexagon box. height2/width2 are half full ones
    markpath += HexBox(txwidth2, txheight2);
  } 

  innersvgdotg.append("path")
    .attr("class", "markmark")
    .attr("d",  markpath)
    .attr("fill", "none")
    .attr("stroke-width", 1)
    .attr("stroke", markcolor);

  // Skip showing some mark_d numbers if they would overlap
  if (!skiptext) {
    innersvgdotg.append("text")
      .attr("class", "markmark noselect")
      .attr("transform", "translate(" + (x0) + "," + (y0 + rel_y0) + ")scale(1,1)")
      .attr("dy", marktextsize)
      .attr("font-size", marktextsize + "px")
      .attr("fill", markcolor)
      .attr("font-family", "sans-serif")
      .style("text-anchor", "middle")
      .text(marktext);
  }
}

//  perTracksize[i].annotheight pix height for drawing annotation text
//  perTracksize[i].eventoffset pix offset for event centerline
//  perTracksize[i].annotoffset pix offset for annot[0] text baseline

function draw_mark(row, d, x0, y0) {
  var t_height = rowToHeight[row];
  var marktextsize = perTracksize[t_height].annotsize;

  // Set mark font size to fixed value
  if (0 < textsize.annot) {marktextsize = textsize.annot;}

  var x0 = x0;
  var y0 = y0; 
  var rel_y0 = perTracksize[t_height].annotoffset - 
    perTracksize[t_height].eventoffset - marktextsize;

  var marktext;
  if (is_mark_a(d))      {marktext = Base40ToChar(arg0(d));}
  else if (is_mark_b(d)) {marktext = Base40ToChar(arg0(d));}
  else if (is_mark_c(d)) {marktext = Base40ToChar(arg0(d));}
  else if (is_mark_d(d)) {marktext = arg0(d).toString();}
  var skiptext = false; 
  if (is_mark_d(d)) {
    var txwidth2 = Rnd1((marktext.length) * marktextsize * 0.6 / 2);
    skiptext = ((x0 - txwidth2) < rightmostnum[row]);
  }
  draw_mark_abcd(d, x0, y0, rel_y0, marktext, marktextsize, skiptext);
  rightmostnum[row] = x0 + txwidth2;
  return;
}

// Annotate all CPU items on screen. Use sparingly at deep zoom only...
// Skips mark, samp, arc, callout, rpcpkt
//       row = getrow(d, prefixCpu);
//       make min_ts per row
function draw_annotateall() {
  // Remove any previous lines
  resetlines();

  // To reduce clutter, don't annotate the same user-mode item repeatedly
  state.annotated_d = [];
  state2.annotated_one_d = -1;
  state.hilite_evnum = 0;
  state.hilite_evnum2 = 0;

  dont_annotate = [];
  dont_annotate[0x10000] = true;	// Never label -idle- event
  resetRotatingLabelk();

  // Draw all lines
  var min_ts = 0;	// But only one per xsecperpix; assumes sorted by ts
  var ignore_min_ts = RowsArePresorted();
  if (data == null) {return;}
  for (var i = 0; i < data.events.length; ++i) {
    var d = data.events[i];
    var gray = !eventHilite(i);
    var skip = gray || dont_annotate[ev(d)] || is_mark(d) || is_pc_samp(d) || 
      is_pstate(d) || is_arc(d) || is_ipi(d) || is_callout(d) || is_rpcpkt(d) || 
      !strictly_startsonscreen_x(d) || !onscreen_all_y(d);
    if (skip) {continue;}
    // PC samples use right end, not left
    var tsrel_d = is_pc_samp(d) ? tsrelend(d) : tsrel(d);
    if (ignore_min_ts || (min_ts <= tsrel_d)) {
      var short = true;
      var rotate = true;
      annotatespan(d, "first", short, -1, rotate);
      if (is_user(d)) {dont_annotate[ev(d)] = true;}
      min_ts = tsrel(d) + xsecperpix;
      state.annotated_d.push(i);
      if(state2.annotated_one_d == -1) {state2.annotated_one_d = i;}  // Keep first
    }
  }
}

function draw_annotateuser() {
  // Remove any previous lines
  resetlines();

  // To reduce clutter, don't annotate the same user-mode item repeatedly
  state.annotated_d = [];
  state2.annotated_one_d = -1;
  dont_annotate = [];
  dont_annotate[0x10000] = true;	// Never label -idle- event
  resetRotatingLabelk();

  // Draw first of each user-mode line
  var min_ts = 0;	// But only one per xsecperpix
  var ignore_min_ts = RowsArePresorted();
  if (data == null) {return;}
  for (var i = 0; i < data.events.length; ++i) {
    var d = data.events[i];
    var gray = !eventHilite(i);
    var skip = gray || !is_user(d) || !strictly_startsonscreen_x(d) || !onscreen_all_y(d);
    skip |= dont_annotate[ev(d)];
    // Suppress names starting with dot, except keep dot-slash names
    skip |= (name(d).substr(0, 1) == ".") & (name(d).substr(1, 1) != "/");
    if (skip) {continue;}
    if (ignore_min_ts || (min_ts <= tsrel(d))) {
      var short = true;
      var rotate = true;
      annotatespan(d, "first", short, -1, rotate);
      dont_annotate[ev(d)] = true;
      min_ts = tsrel(d) + xsecperpix;
      state.annotated_d.push(i);
      if(state2.annotated_one_d == -1) {state2.annotated_one_d = i;}  // Keep first
    }
  }
}

function draw_one_ipcllc(d, x0, y0, e_height) {
  var do_kernel = (state2.ipc_value & 2) != 0;
  var do_user   = (state2.ipc_value & 1) != 0;
  // Since IPC for idle is always 0, omit it to avoid clutter
  if ((do_kernel && is_kernel(d)) || (do_user && is_user(d))) {
    var centerx = x0;
    var centery = y0;
    if (has_ipc) {draw_ipcmark(innersvgdotg, centerx, centery, e_height, realipc(d), false);}
    if (has_llc) {draw_llcmarks(innersvgdotg, centerx, centery, e_height, d, false);}
  }
}

// Cycyle through drawing mark_a/c and/or mark_b/d
function draw_one_mark(row, d, x0, y0) {
  var draw_bd = (state2.marks_value & 1) != 0;
  var draw_ac = (state2.marks_value & 2) != 0;
  var is_ac = (ev(d) == 0x20A) || (ev(d) == 0x20C);
  var is_bd = (ev(d) == 0x20B) || (ev(d) == 0x20D);
  if ((is_ac && draw_ac) || (is_bd && draw_bd)) {
    draw_mark(row, d, x0, y0);
  }
}

function draw_one_mark_old(row, d, x0, y0) {
  var draw_numeric = (state2.marks_value & 1) != 0;
  var draw_text = (state2.marks_value & 2) != 0;
  if ((draw_text && ev(d) != 0x20D) || (draw_numeric && ev(d) == 0x20D)) {
    draw_mark(row, d, x0, y0);
  }
}

// Draw dotted hexagon around and label area that is expanded later
function draw_bracket(d, left_t, right_t) {
    // Draw top of diagram bracket
    var left_x = panzoomX(x(left_t), state2.savedtransformX);
    var right_x = panzoomX(x(right_t), state2.savedtransformX);
    var height_y =  diagramheight - 30;
    var top_y = y(0) - linewidth.k4 * 0.8;
    var bottom_y = top_y + height_y;
    var path1 = "M" + Rnd1(left_x - 6) + "," + Rnd1(top_y + (height_y / 2)) +
                " L" + Rnd1(left_x) + "," + Rnd1(top_y) +
                " H" + Rnd1(right_x) +
                " L" + Rnd1(right_x + 6) + "," + Rnd1(top_y + (height_y / 2));
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path1)
      .attr("fill", "none")
      .attr("stroke-dasharray", [1,5])
      .attr("stroke-width", 2)
      .attr("stroke", "#404040");	// mid-gray

    // Draw bracket text
    var brackettext = innersvgdotg.append("text");
    brackettext 
      .attr("transform", 
             "translate(" + ((left_x + right_x) / 2) + "," + (top_y - 4) + ")")
      .attr("class", "brackettext")
      .attr("font-size", textsize.smalltick + "px")
      .text(name(d));

    // Draw bottom of diagram bracket
    var path2 = "M" + Rnd1(left_x - 6) + "," + Rnd1(bottom_y - (height_y / 2)) +
                " L" + Rnd1(left_x) + "," + Rnd1(bottom_y) +
                " H" + Rnd1(right_x) +
                " L" + Rnd1(right_x + 6) + "," + Rnd1(bottom_y - (height_y / 2));
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path2)
      .attr("fill", "none")
      .attr("stroke-dasharray", [1,5])
      .attr("stroke-width", 2)
      .attr("stroke", "#404040");	// mid-gray

}

// Draw oval around one CPU line and fade everything outside it
function draw_oval(d, left_t, right_t) {
    // Draw top of diagram bracket
    var left_x = panzoomX(x(left_t), state2.savedtransformX);
    var right_x = panzoomX(x(right_t), state2.savedtransformX);
    var mid_y = y(cpu(d)) + linewidth.k4 * 0.4;
    var top_y = mid_y - linewidth.k4 * 1.1;
    var bottom_y = mid_y + linewidth.k4 * 1.1;

// How can we fill with alpha?
// fill-opacity: This attribute takes a decimal number between 0.0 and 1.0, inclusive; 
// where 0.0 is completely transparent.
    // Draw outer shape clockwise; draw inner shape counter-clockwise
    // Z closepath followed by moveto continues
    // Rectangle: 0, diagramheight   0,0   diagramwidth,0   diagramwidth,diagramheight  closepath 
    // Oval corners:  left_x,top_y   left_x,bottom_y   right_x,bottom_y   right_x,top_y   closepath 
    // Q 50 10 100 75

    // Start at bottom left corner; go in a bit
    var path3 = "M" + Rnd1(0 + 2) + "," + Rnd1(diagramheight - 2) +
                " V" + Rnd1(0 + 2) + 
                " H" + Rnd1(diagramwidth - 2) +
                " V" + Rnd1(diagramheight - 2) + 
                " Z" +
                " M" + Rnd1(left_x) + "," + Rnd1(top_y) +
                " Q" + Rnd1(left_x - 6) + "," + Rnd1(mid_y) + "," +
                       Rnd1(left_x) + "," + Rnd1(bottom_y) + 
                " H" + Rnd1(right_x) +
                " Q" + Rnd1(right_x + 6) + "," + Rnd1(mid_y) + "," +
                       Rnd1(right_x) + "," + Rnd1(top_y) + 
                " Z";
   innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path3)
      .attr("stroke", "none")
      .attr("fill", "#FFFFFF")
      .attr("fill-opacity", 0.8);
}

// Draw diagonal orange overlay
function draw_diag_orange (x0, x1) {
  // One track is region234height / ydomainwidth pixels
  // One row is  ypixpertrack * maxTrackHeight pixels 
  var rows = (ybot_row + 1) - ytop_row;
  var pxperrow =  region234height / rows;
  // 1/4 of a row margin on top and bottom of diagonal lines
  var orangemargin = Rnd0((pxperrow * 0.25));
  var xleft = Rnd0(x0);
  var ytop = orangemargin;
  var ybottom = region234height - orangemargin;
  var rect_width = Rnd0(x1 - x0);
  var rect_height = Rnd0(region234height - (2 * orangemargin));
  var tilt_xpx = Rnd0(region234height * 0.07);	// About a 4 degree slant. sin(4) = 0.0698

  var rectpath = "M" + 0 + "," + orangemargin + " h" + rect_width + " v" + rect_height + " h" + (-rect_width) + " Z";

  // Make a clipping rectangle sub-group
  var innersvgdotgsvg = innersvgdotg.append("svg")
      .attr("class", "graphlinex") 
      .attr("x", xleft)
      .attr("y", ytop)
      .attr("width", rect_width)
      .attr("height", rect_height);

  innersvgdotgsvgdotg = innersvgdotgsvg.append("g");

  // Light orange fill, semi-transparent
  //innersvgdotgsvgdotg.append("path")
  //    .attr("class", "graphlinex") 
  //    .attr("d",  rectpath)   
  //    .attr("fill", "#FFEEDD")
  //    .attr("fill-opacity", 0.5); 

  // Light orange border to give some definition, semi-transparent
  innersvgdotgsvgdotg.append("path")
      .attr("class", "graphlinex") 
      .attr("d",  rectpath)   
      .attr("fill", "none")
      .attr("stroke-width", 6)		// Half is clipped off
      .attr("stroke", "#FFEEDD")	// light orange
      .attr("stroke-opacity", 0.5); 

  // Multiple diagonal organge lines
  for (var j = xleft - tilt_xpx; j < x1; j+=11) {
    var path = "M" + (j - xleft) + "," + (ybottom) + " l" + tilt_xpx + "," + (-rect_height);
    innersvgdotgsvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path)
      .attr("fill", "none")
      .attr("stroke", "#FF8000")
      .attr("stroke-width", 2);
  }
}

// Draw diagonal orange lines spaced 11px. 
// For hack showing some area of interest
function draw_dots(d, left_t, right_t) {
  if (state2.hack_value == 0) {return;}

  var x0 = x_zoomed(ts(d));
  var x1 = x_zoomed(tsend(d));
  if (cpu(d) == 0) {
    draw_diag_orange(x0, x1);
  }
}

// Rotate <x,y> w.r.t. <0.0>
function xyrotate(xypair, sincospair) {
  var xyresult = {x:0, y:0};
  xyresult.x = xypair.x * sincospair.cos - xypair.y * sincospair.sin;
  xyresult.y = xypair.x * sincospair.sin + xypair.y * sincospair.cos;
  return xyresult;
}
 
// Set up arc path without drawing it yet
function make_arc_path(x0, y0, x1, y1) {
  var left_x = x0;
  var right_x = x1;

  // Suppress arcs that are a small fraction of the display window
  //  suppress if the window width is greater than 5 milliseconds
  //  but if bold, show all arcs
  //  or if hack is set, show all arcs
  var bold = (state2.arcs_value == 1);
  var short_span = ((right_x - left_x) < (region3width * arcfraction));
  var long_window = (xdomainwidth > 0.005);
  if (short_span && long_window && !force_annot && !bold && (state2.hack_value == 0)) {return "";}

  var left_y = y0;
  var right_y = y1;
  var mid_x = (left_x + right_x) / 2;
  var mid_y = (left_y + right_y) / 2;

  // Control point needs to be rotated wrt left ==> right line
  var arc_run = right_x - left_x;
  var arc_rise = right_y - left_y;
  var arc_hypot = Math.sqrt(arc_run * arc_run + arc_rise * arc_rise);
  var sincospair = {sin:0, cos:1};
  sincospair.sin = arc_rise / arc_hypot;
  sincospair.cos = arc_run / arc_hypot;
  // Control point of arc is at <0,10> px rotated
  var ctl_rot = xyrotate({x: 0, y: -30}, sincospair);

  // Arrow angle needs to be wrt control_point ==> right line
  var ctl_run = right_x - (mid_x + ctl_rot.x);
  var ctl_rise = right_y - (mid_y + ctl_rot.y);
  var ctl_hypot = Math.sqrt(ctl_run * ctl_run + ctl_rise * ctl_rise);
  sincospair.sin = ctl_rise / ctl_hypot;
  sincospair.cos = ctl_run / ctl_hypot;
  var arrow_rot = xyrotate({x: -8, y: -8}, sincospair);

  var arc_path = "M" + Rnd1(left_x) + "," + Rnd1(left_y) +
                 " Q" + Rnd1(mid_x + ctl_rot.x) + "," +
                     Rnd1(mid_y + ctl_rot.y) + "," +
                     Rnd1(right_x) + "," + Rnd1(right_y) + 
                 " l" + Rnd1(arrow_rot.x) + "," + Rnd1(arrow_rot.y);
  return arc_path;
}

// Draw wakeup arc from x0 y0 to x1 y1
function draw_arc_wake(x0, y0, x1, y1) {
  if (state2.arcs_value == 0) {return;}

  var arcpath = make_arc_path(x0, y0, x1, y1);
  if (arcpath == "") {return;}
  
  var bold = (state2.arcs_value == 1);
  innersvgdotg.append("path")
    .attr("class", "graphlinex")
    .attr("d", arcpath)
    .attr("fill", "none")
    .attr("stroke-width", bold ? 3.0 : 2.0)
    .attr("stroke-dasharray", bold ? [4,4] : [3,3])
    .attr("stroke", bold ? "#0055AA" : "#0080FF");	// dark/light Blue-green
}

// Draw IPI arc from x0 y0 to x1 y1
function draw_arc_ipi(x0, y0, x1, y1) {
  // Only show the IPI arcs if bold is set
  if (state2.arcs_value != 1) {return;}

  var arcpath = make_arc_path(x0, y0, x1, y1);
  if (arcpath == "") {return;}

  innersvgdotg.append("path")
    .attr("class", "graphlinex")
    .attr("d", arcpath)
    .attr("fill", "none")
    .attr("stroke-width", 2)
    .attr("stroke-dasharray", [9,3])
    .attr("stroke", "#C0C0C0");		// light gray
}

function draw_arc(d, x0, y0, x1, y1) {
  if      (is_arc(d)) {draw_arc_wake(x0, y0, x1, y1);}
  else if (is_ipi(d)) {draw_arc_ipi(x0, y0, x1, y1);}
}

// Names for things...
// Y-axis labels program.1234 kworker.10 etc.
// X-axis label Time (msec) etc.
// Title Cheap whetstone
// Marks mark_a, ...
// Annotations 234.56 futex(0)=1; 3.15us ipc=3/4
// Overlay slow-clock pc samp cexit rpcmsg overhead ipc wait_*
//   bracket oval arc
// Callout bubble added by hand
//   [ 12.34567800, 0.00000000, -1, -1, 18471, -4, 50, -2, 0, "LookHere"],
// callout point at <ts,row>, label center at <ts + deltax, row + deltarow>, label text name
//   ts = x0, cpu/pid/rpc = row, event=bubble, arg0 = delta_x, ret = delta_row, name = text
//   dur, ipc unused
//   ts           dur       CPU tid  rpc event arg0 ret ipc  name

// Returns path data for a rectangle with rounded corners.
// The center is ⟨x,y⟩.
function roundedRect(centx, centy, width, height, radius) {
  var x = centx - width / 2;
  var y = centy - height / 2;
  return "M" + (x + radius) + "," + y
       + "h" + (width - 2 * radius)
       + "a" + radius + "," + radius + " 0 0 1 " + radius + "," + radius
       + "v" + (height - 2 * radius)
       + "a" + radius + "," + radius + " 0 0 1 " + -radius + "," + radius
       + "h" + (2 * radius - width)
       + "a" + radius + "," + radius + " 0 0 1 " + -radius + "," + -radius
       + "v" + (2 * radius - height)
       + "a" + radius + "," + radius + " 0 0 1 " + radius + "," + -radius
       + "z";
}

// Returns path data for a triangle with base centered at x0,y0 and width 2 * halfwidth,
// with point at x1,y1.
function spike(x0, y0, x1, y1, halfwidth) {
  return "M" + (x0 - halfwidth) + "," + y0
       + "h" + (2 * halfwidth) 
       + "l" + (x1 - (x0 + halfwidth)) + "," + (y1 - y0)
       + "l" + ((x0 - halfwidth) - x1) + "," + (y0 - y1)
       + "z";
}

function getTextWidth(text, font) {
  // re-use canvas object for better performance
  var canvas = getTextWidth.canvas || (getTextWidth.canvas = document.createElement("canvas"));
  var context = canvas.getContext("2d");
  context.font = font;
  var metrics = context.measureText(text);
  return metrics.width;
}

// Center at x0,y0 and point at x1,y1
function draw_bubble(d, x0, y0, x1, y1, height) {
  var txt = name(d);
  var lbltextsize = height + "px";
  var width = getTextWidth(txt, lbltextsize + " sans-serif");
  var pad = height / 6;
  var path1 = roundedRect(x0, y0, width + pad, height + pad, Rnd1(height / 3));
  var path2 = spike(x0, y0, x1, y1, Rnd1(height/4));

  // The spike outline
  innersvgdotgbare.append("path")
    .attr("class", "extra")
    .attr("d", path2)
    .attr("fill", "none")
    .attr("stroke-width", "2")	
    .attr("stroke", "#000000");		// black
  // The bubble outline
  innersvgdotgbare.append("path")
    .attr("class", "extra")
    .attr("d", path1)
    .attr("fill", "none")
    .attr("stroke-width", "2")	
    .attr("stroke", "#000000");		// black

  // Spike fill
  // we want mousedown on this to go to doCalloutSpike
  innersvgdotgbare.append("path")
    .on("mousedown", function foo2() {doCalloutSpike(d, event)})
    .attr("class", "extra cospike")
    .attr("d", path2)
    .attr("pointer-events", "all")
    .attr("fill", "#FFFFFF");		// white

  // Bubble fill
  // we want mousedown on this to go to doCalloutBubble
  innersvgdotgbare.append("path")
    .on("mousedown", function foo1() {doCalloutBubble(d)})
    .on("mouseup", mouseup_callout)
    .attr("class", "extra cobubble")
    .attr("d", path1)
    .attr("pointer-events", "all")
    .attr("fill", "#FFFFFF");		// white

  // Bubble text
  innersvgdotgbare.append("text")
    .attr("pointer-events", "none")
    .attr("class", "extra noselect")
    .attr("x", x0 - width/2)
    .attr("y", y0 + height/2 - pad)
    .attr("width", width)
    .attr("height", height)
    .attr("font-family", "sans-serif")
    .attr("font-size", lbltextsize)
    .style("fill", "#0000AA")		// medium dark blue
    .style("text-align", "center")
    .text(txt);
}

function draw_callout(d) {
//console.log("callout", name(d));
  // Tip of callout
  var x1 = x_zoomed(ts(d));
  var row1 = getfirstrow(d);
  if (row1 < 0) {return;}

  var track1 = rowToTrack[row1];
  var t_height1 = rowToHeight[row1];
  if (t_height1 == 0) {return;}
  var e_height1 = perTracksize[t_height1].eventheight;
  // Offset tip up (smaller y1) if delta_row <= 0, else offset down
  var tip_offset = (delta_row(d) <= 0) ? -(e_height1 / 2) : +(e_height1 / 2);
  var y1 = y_zoomed(track1) + perTracksize[t_height1].eventoffset + tip_offset;
 
  // Center of callout bubble
  var x0 = x1 + delta_x(d);
  var y0 = y1 + delta_row(d) * ypixpertrack * t_height1;

  var height =  Rnd0(perTracksize[t_height1].annotsize);
  draw_bubble(d, x0, y0, x1, y1, height * 1.5);
  return;
}


// TODO: bracket and oval are broken. Need position updates like arc
function overlaydraw(d, gray) {
  var left_t = tsrel(d);
  var right_t = tsrel(d) + dur(d);
  if (is_bracket(d)) {
    draw_bracket(d, left_t, right_t);
  } else if (is_oval(d)) {
    draw_oval(d, left_t, right_t);
  } else if (is_dots(d)) {
    draw_dots(d, left_t, right_t);
  } else if (is_callout(d)) {
    draw_callout(d, left_t, right_t);
  } else if (!gray && (is_arc(d) || is_ipi(d))) {
    // SOON draw_arc(d, left_t, right_t);
    // Arc starts at <tsrel, cpu> and goes to <tsrel + dur, cpu2>, where
    // Arc 2 starts at <tsrel, pid> and goes to <tsrel + dur, pid2>, where
    // cpu2 is stored in the arg0 field, pid2 in the ret field
    var x0 = x_zoomed(ts(d));
    var x1 = x_zoomed(tsend(d));
    var row0 = getrow_cpu(d, prefixCpu);
    var track0 = rowToTrack[row0];
    var t_height0 = rowToHeight[row0];
    var e_height0 = perTracksize[t_height0].eventheight;
    var y0 = y_zoomed(track0) + perTracksize[t_height0].eventoffset - (e_height0 / 2);
    var row1 = getrow_cpu2(d, prefixCpu);
    var track1 = rowToTrack[row1];
    var t_height1 = rowToHeight[row1];
    var e_height1 = perTracksize[t_height1].eventheight;
    var y1 = y_zoomed(track1) + perTracksize[t_height1].eventoffset - (e_height1 / 2);
    var valid_arc = (0 <= row0) && (0 <= row1) && (t_height0 != 0)&& (t_height1 != 0);
    if (valid_arc) {draw_arc(d, x0, y0, x1, y1);}

    // Also draw pid to pid2 arc if PIDs are being shown and both rows are valid
    if (state2.groupcompress[1] == 0) {return;}
    var row2 = getrow_pid(d, prefixPid);
    var row3 = getrow_pid2(d, prefixPid);
    if (row2 < 0) {return;}
    if (row3 < 0) {return;}
    if(rowToHeight[row2] == 0) {return;}
    if(rowToHeight[row3] == 0) {return;}

    var track2 = rowToTrack[row2];
    var t_height2 = rowToHeight[row2];
    var e_height2 = perTracksize[t_height2].eventheight;
    var y2 = y_zoomed(track2) + perTracksize[t_height2].eventoffset - (e_height2 / 2);
    var track3 = rowToTrack[row3];
    var t_height3 = rowToHeight[row3];
    var e_height3 = perTracksize[t_height3].eventheight;
    var y3 = y_zoomed(track3) + perTracksize[t_height3].eventoffset - (e_height3 / 2);
    valid_arc = (0 <= row2) && (0 <= row3) && (t_height2 != 0)&& (t_height3 != 0);
    if (valid_arc) {draw_arc(d, x0, y2, x1, y3);}
  }
}


function arraysum(x) {
  var sum = 0;
  for (var i = 0; i < x.length; ++i) {sum += x[i];}
  return sum;
}

// Draw the Morse code for why we are waiting
// Draw at least one nominal character width of line plus one full character
// Draw line plus maximum three characters
function draw_wait(d, x0, y0, x1, gray, height) {
//console.log("draw_wait", x0, x1, y0, height, d);
   if (state2.waits_value == 0) {return;}
  var wait_px = x1 - x0;
  if (wait_px < 2) {return};		// Avoid short pixel clutter
  if ((3 * height) < 0.5) {return;}	// Avoid too thin to see even a Morse character
  var letternum = ev(d) - waitbase;

  // Draw a line plus 0..3 complete characters
  // Line to be a minimum of 1/2 a character
  var char_width_px = arraysum(waitdash[letternum]);	// One char width
  var charcount = Math.floor((wait_px / char_width_px) - 0.5);
  if (charcount < 0) {charcount = 0;}
  if (3 < charcount) {charcount = 3;}
  var charlen = charcount * char_width_px;
  var linelen = wait_px - charlen;

  // Draw thin line followed by chars of Morse code
  // Put on pixel grid to make display sharper
  var pathw1 = "M" + Rnd5(x0) + "," + Rnd5(y0) + " h" + Rnd5(linelen);
  innersvgdotg.append("path")
    .attr("class", "graphlinex")
    .attr("d",  pathw1)
    .attr("fill", "none")
    .attr("stroke-width", Rnd5(height))
    .attr("stroke", (0 < fade_value) ? fadecolor : (gray ? fadecolor : waitcolor[letternum]));

  // Draw Morse code chars
  if (0 < charcount) {
    var pathw2 = "M" + Rnd5(x0 + linelen) + "," + Rnd5(y0) + " h" + Rnd5(charlen);
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  pathw2)
      .attr("fill", "none")
      .attr("stroke-width", Rnd5(2 * height))
      .attr("stroke-dasharray", waitdash[letternum])
      .attr("stroke-dashoffset", -2)	// A little space on left before the characters start
      .attr("stroke", (0 < fade_value) ? fadecolor : (gray ? fadecolor : waitcolor[letternum]));
  }
}

// Draw line to show waiting in a queue
// 2021.05.22 make line height bigger
function draw_queue(d, x0, y0, x1, gray, height) {
//console.log("draw_queue", x0, x1, y0, height, d);
  if (state2.waits_value == 0) {return;}
  var wait_px = x1 - x0;
  if (wait_px < 2) {return};		// Avoid short pixel clutter
  if (height < 0.5) {return;}		// Avoid too thin to see line
//console.log("draw_queue2");

  // Draw thin line 
  // Put on pixel grid to make display sharper
  var pathw = "M" + Rnd5(x0) + "," + Rnd5(y0) + " h" + Rnd5(wait_px);
  innersvgdotg.append("path")
    .attr("class", "graphlinex")
    .attr("d",  pathw)
    .attr("fill", "none")
    .attr("stroke-width", Rnd5(height * 1.25))
    .attr("stroke-dasharray", [3,5])
    .attr("stroke", (0 < fade_value) ? fadecolor : (l1color_arg_dark(arg0(d), gray)));
}



// Draw possible slow-clock overlay -- orange for performance caution
// freq_value 0: off, 1: bold 2: gradient
// Clock frequency in arg0(d) is in MHz, so 2700 = 2.7 GHz
function draw_pstate(d, x0, y0, x1, gray, height) {
  var samp_px = x1 - x0;

  var freq = arg0(d);
  var opacity = 0.0;
  if (state2.freq_value == 0) {return;}
  // Avoid short pixel clutter for normal overlay
  // Always draw bold overlay even if tiny width
  if ((samp_px < 2) && (state2.freq_value == 2)) {return};

  var freq_fraction = Math.floor((freq - freq_min) / freq_divisor);  // 0..8
  if (state2.freq_value == 1) {
    opacity = freq_bold_opacity_range[freq_fraction];
  }
  if (state2.freq_value == 2) {
    opacity = freq_opacity_range[freq_fraction];
  }
  if (opacity == 0.0) {return;}	// Draw nothing if fully clear  

  // Draw overlay to indicate slow clock
  var pathps = "M" + Rnd5(x0) + "," + Rnd5(y0) + " H" + Rnd5(x1);
  innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  pathps)
      .attr("fill", "none")
      .attr("stroke-width", Rnd5(height))
      .attr("stroke", freq_color_range[freq_fraction])	// red, slightly orange, green
      .attr("stroke-opacity", opacity);
}


// Draw one PC sample
function draw_pc_samp(d, x0, y0, x1, gray, height) {
  if (state2.samp_value <= 0) {return;}	// PC samples off or unavailable
  if ((state2.samp_value == 2) && (pid(d) == 0)) {return;}  // Event is pc_samp, but pid 0 = idle
  if ((state2.samp_value == 1) && (pid(d) != 0)) {return;}  // Event is pc_samp, and pid 0 = idle
  var samp_px = x1 - x0;
  if (samp_px < 2) {return};		// Avoid short pixel clutter
  if ((3 * height) < 0.5) {return;}	// Avoid too thin to see 

  // Just draw the slightly-rising line. PC Sample is at top point.
  // Put on pixel grid to make display sharper
  var pathpc = "M" + Rnd5(x0) + "," + Rnd5(y0 - 1) + " L" + Rnd5(x1) + "," + Rnd5(y0 - 6); 
  // Vertical tic
  var pathpc2 = "M" + Rnd5(x1 - 1) + "," + Rnd5(y0 - 9 - height/2) + 
    " L" + Rnd5(x1) + "," + Rnd5(y0 - 3 + height/2) + 
    " L" + Rnd5(x1 + 1) + "," + Rnd5(y0 - 9 - height/2); 
  innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  pathpc)
      .attr("fill", "none")
      .attr("stroke-width", Rnd5(height))
      .attr("stroke", l1color_arg(arg0(d), gray));
  if ((15 <= samp_px) || force_annot) {
    // Add dashed line in second color
    // If kernel-mode address, change the dash pattern and width
    // We have two events, KUTRACE_PC_U and KUTRACE_PC_K 
    var is_kernel = (ev(d) == 0x281);

    var dashwidth = is_kernel ? Rnd5(height * 2) : Rnd5(height);
    var dasharray = is_kernel ? [8,8] : [5,10];
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  pathpc)
      .attr("fill", "none")
      .attr("stroke-width", dashwidth)
      .attr("stroke-dasharray", dasharray)
      .attr("stroke-dashoffset", -5)	// Pick up midway
      .attr("stroke", l2color_arg(arg0(d), gray));
  }
  // Vertical tic
  innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  pathpc2)
      .attr("fill", "none")
      .attr("stroke-width", 1)
      .attr("stroke", "#000000");
}

// Draw lock-held line
function draw_lock_line(d, x0, y0, x1, gray, height, row) {
  if (state2.lock_value <= 0) {return;}	// Lock-held lines off
  var samp_px = x1 - x0;
  if (samp_px < 8) {return};		// Avoid short pixel clutter and bogus endcaps
  if ((3 * height) < 0.5) {return;}	// Avoid too thin to see the line

  // Unstack multiple expired locks
  var lockstack = pending_locks[row];
  while ((0 < lockstack.length) && (lockstack[lockstack.length - 1] <= x0)) {lockstack.pop();}
  
  // Bold the solid-line acquired locks but not dotted-line trying
  var bold = (state2.lock_value == 1);
  var high = (bold && !is_lock_dots(d)) ? height * 1.5 : height;
  // Stack remaining locks on 1.5 centers
  var yy = y0 - lockstack.length * (high * 1.5);	// Above the line
  lockstack.push(x1);

  // Put on pixel grid to make display sharper
  var path = "M" + Rnd5(x0) + "," + Rnd5(yy) + " L" + Rnd5(x1) + "," + Rnd5(yy);  
  innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path)
      .attr("stroke-dasharray", is_lock_dots(d) ? [3,3] : [1,0])
      .attr("fill", "none")
      .attr("stroke-width", Rnd5(high))
      .attr("stroke", l1color_arg_dark(arg0(d), gray));
  // Two arcs on the ends
  var r = Rnd5(high) * 1;
  var endcaps = 
      "M" + Rnd5(x0 + 6) + " " + Rnd5(yy - r) +  
      " Q" + Rnd5(x0 - 6) + " " + Rnd5(yy) + " " + Rnd5(x0 + 6) + " " + Rnd5(yy + r) +
      " M" + Rnd5(x1 - 6) + " " + Rnd5(yy - r) +  
      " Q" + Rnd5(x1 + 6) + " " + Rnd5(yy) + " " + Rnd5(x1 - 6) + " " + Rnd5(yy + r);          
  innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  endcaps)
      .attr("fill", "none")
      .attr("stroke-width", 2)
      .attr("stroke", l1color_arg_dark(arg0(d), gray));
      //.attr("stroke", "#000000");	// black
}

function draw_rx_tx(d, x0, y0, x1, height, pktpx) {
  var rx = is_rpcrxpkt(d) || is_pktrx(d);
  var widthpx = x1 - x0;
  var msg_x0, msg_x1, msg_y0, x_off, y_off, txt_off;
  var msg_color;
  // If incoming, slant down; if outgoing, slant up
  if (rx) {
    msg_x0 = x0;
    msg_x1 = x1;
    msg_y0 = y0 + 17;		// Rx centerline; lower
    x_off = -3;			// Slant backwards
    y_off = 2;			// Raise packet beginning
    txt_off = 14;		// Text below centerline
    msg_color = "#800000";	// dark red
  } else {
    msg_x0 = x0;
    msg_x1 = x1;
    msg_y0 = y0 + 9;		// Tx centerline; higher
    x_off = 3;			// Slant forwards
    y_off = -2;			// lower packet beginning
    txt_off = -6;		// Text above centerline
    msg_color = "#008080";	// dark cyan
  }

  // If the duration is less than 5 pixels, just draw a tic, centered
  if (widthpx < 5) {
    var path1 = "M" + Rnd1(msg_x0 + (widthpx/2) + x_off) + "," + Rnd1(msg_y0) + 
      " m" + (-x_off) + ",4" + 
      " l" + (x_off * 2) + ",-8";
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path1)
      .attr("fill", "none")
      .attr("stroke-width", 1)
      .attr("stroke", "#000000");
    return;
  }

  // Let RPC annot text track other annots
  var  rpcfontsize =  perTracksize[maxTrackHeight].annotsize;
  // Else draw two ticks, line inbetween, afspikend maybe rpc number
  // If room for 1 digit @10px (1 * 10 * 0.5) show rpcid
  if ((rpcfontsize * 0.5) <= widthpx) {
    innersvgdotg.append("text")
      .attr("class", "graphlinex noselect")
      .attr("transform", "translate(" + Rnd1(msg_x0 + x_off + 4) + "," + Rnd1(msg_y0 + txt_off) + ")scale(1,1)")
      .attr("font-size", rpcfontsize + "px")
      .attr("fill", "#000000")
      .attr("font-family", "sans-serif")
      //.style("text-anchor", "end")
      .text(rpc(d));
  }

  // Line, slight overlap of tics, drawn next
  var path2 = "M" + Rnd1(msg_x0 - 2 + x_off) + "," + Rnd1(msg_y0 - y_off) +
    " L" + Rnd1(msg_x1 + 2 + x_off) + "," + Rnd1(msg_y0 + y_off);

  // If one packet of 1448 bytes of data is 15px or more, draw with dashes
  // We approximate a packet as 1400 bytes wide plus 100 byte gap, scaled to pixels
  if (15 <= widthpx) {
    var dashlinepx = pktpx * 14 / 15;
    var dashgappx =  pktpx *  1 / 15;
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path2)
      .attr("fill", "none")
      .attr("stroke-dasharray", [dashlinepx, dashgappx])
      .attr("stroke-dashoffset", -1)	// A little space on left 
      .attr("stroke-width", height)
      .attr("stroke", msg_color);

  } else {
    // Else draw solid line
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path2)
      .attr("fill", "none")
      .attr("stroke-width", height)
      .attr("stroke", msg_color);
  }

  // Tics
  var path1 = "M" + Rnd1(msg_x0 + x_off) + "," + Rnd1(msg_y0) + 
    " m" + (-x_off) + ",4" + 
    " l" + (x_off * 2) + ",-8" +
    " M" + Rnd1(msg_x1 + x_off) + "," + Rnd1(msg_y0) + 
    " m" + (-x_off) + ",4" + 
    " l" + (x_off * 2) + ",-8";
  innersvgdotg.append("path")
    .attr("class", "graphlinex")
    .attr("d",  path1)
    .attr("fill", "none")
    .attr("stroke-width", 1)
    .attr("stroke", "#000000");
  }

// Draw thin line showing approx network occupation for an entire message
// Old form has 10ns duration and x0 either right(rx) or left(tx) endpoint
// New form has pre-calculated duration and x0 is always left endpoint
function draw_rpc_msg(d, x0, y0, x1, gray, height) {
  var msg_x0 = x_zoomed(ts(d));
  var msg_x1 = x_zoomed(tsend(d));
  var rpcmsgdursec = dur(d);
  if (dur(d) <= 0.00000001) {
    // Old form
    var rpcmsgsize = msgsize(d) + 88;		// Size in bytes of dclab_rpc message
    var rpcmsgdurusec = (rpcmsgsize * 8) / kNetworkMbPerSec;	
    rpcmsgdursec = rpcmsgdurusec / 1000000.0;
    if (is_rpcrxpkt(d)) {
      msg_x0 = x_zoomed(ts(d) - rpcmsgdursec);
      msg_x1 = x_zoomed(ts(d));
    } else {
      msg_x0 = x_zoomed(ts(d));
      msg_x1 = x_zoomed(ts(d) + rpcmsgdursec);
    }
  }
  var pktdursec = ((1500 * 8) / kNetworkMbPerSec) / 1000000.0;
  var pktpx = Rnd5(pktdursec / xsecperpix);

  // If the duration is less than 1/10 pixel, draw nothing
  if (rpcmsgdursec < (xsecperpix * 0.1)) {return;}

  var rx = is_rpcrxpkt(d);
  var msg_y0, x_off, y_off, txt_off;
  var msg_color;
  // If incoming, subtract to put transmission before event; if outgoing, add
  if (rx) {
    msg_y0 = y_zoomed(0);	// Rx centerline
    x_off = -3;			// Slant backwards
    y_off = 2;			// Raise packet beginning
    txt_off = 14;		// Text below centerline
    msg_color = "#800000";	// dark red
  } else {
    msg_y0 = y_zoomed(0);	// Tx centerline
    x_off = 3;			// Slant forwards
    y_off = -2;			// lower packet beginning
    txt_off = -6;		// Text above centerline
    msg_color = "#008080";	// dark cyan
  }
  draw_rx_tx(d, msg_x0, msg_y0, msg_x1, height, pktpx);
}

// Draw thin tic mark showing packet-seen-in-kernel time
function draw_pkt(d, x0, y0, x1, gray, height) {
  var pkt_x0 = x_zoomed(ts(d));
  var pkt_x1 = pkt_x0 + 1;	// 1px wide
  var pkt_y0 = y_zoomed(0);	// Centerline just above CPU 0
  var pktpx = 1;
  draw_rx_tx(d, pkt_x0, pkt_y0, pkt_x1, height, pktpx);
}

// Draw sine wave showing slow CPU exit from sleep
function draw_c_exit(d, x0, y0, x1, gray, height) {
  var sinewidth = x1 - x0;
  var sineheight = 0.5 * height;
  var path3 = "M"  + Rnd1(x0) + "," + Rnd1(y0) + 
              " Q" + Rnd1(x0 + 0.25 * sinewidth) +"," + Rnd1(y0 - sineheight) +
              " "  + Rnd1(x0 + 0.50 * sinewidth) +"," + Rnd1(y0) +
              " Q" + Rnd1(x0 + 0.75 * sinewidth) +"," + Rnd1(y0 + sineheight) +
              " "  + Rnd1(x0 + 1.00 * sinewidth) +"," + Rnd1(y0);
//console.log("cexit", path3);
  innersvgdotg.append("path")
    .attr("class", "graphlinex")
    .attr("d",  path3)
    .attr("fill", "none")
    .attr("stroke-width", Rnd1(0.07 * height))
    .attr("stroke", (0 < fade_value) ? fadecolor : (gray ? fadecolor : "#C00000") );	// darkish red
}

function draw_ovhd(d, x0, y0, x1, e_height) {
  // Draw diagonal white lines to show overhead: ~20ns go, ~50ns goipc (RISC-V is more)
  var width = x1 - x0;
  // Avoid short pixel clutter
  if (width < 8) {return;}

  var h5 = e_height / 5;
  var pathov = "M" + Rnd1(x0 + width) + "," + Rnd1(y0 - e_height / 2) + 
      " l" + (-width) + "," + h5 + " m" + width + ",0" +
      " l" + (-width) + "," + h5 + " m" + width + ",0" +
      " l" + (-width) + "," + h5 + " m" + width + ",0" +
      " l" + (-width) + "," + h5 + " m" + width + ",0" +
      " l" + (-width) + "," + h5 + " m" + width + ",0";
  innersvgdotg.append("path")
    .attr("class", "graphlinex")
    .attr("d",  pathov)
    .attr("fill", "none")
    .attr("stroke-width", 2)
    .attr("stroke", "#FFFFFF")	// white;
}

function draw_kernel_fast(d, x0, y0, x1, gray, e_height) {
  var path = "M" + Rnd1(x0) + "," + Rnd1(y0) + " H" + Rnd1(x1);
  // Just draw the syscall/irq/fault background color
  innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path)
      .attr("fill", "none")
      .attr("stroke-width", l3width(d, e_height))
      .attr("stroke", (0 < fade_value) ? fadecolor : l3darkcolor(d, gray));
}

function draw_kernel(d, x0, y0, x1, gray, e_height) {
//console.log("draw_kernel", x0, y0, x1, gray, e_height);
  if (0 < fade_value) {draw_kernel_fast(d, x0, y0, x1, gray, e_height); return;}
  // Draw the syscall/irq/fault four lines
  var path = "M" + Rnd1(x0) + "," + Rnd1(y0) + " H" + Rnd1(x1);
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path)
      .attr("fill", "none")
      .attr("stroke-width", l4width(d, e_height))
      .attr("stroke", l4color(d, gray));
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path)
      .attr("fill", "none")
      .attr("stroke-width", l3width(d, e_height))
      .attr("stroke", l3color(d, gray));
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path)
      .attr("fill", "none")
      .attr("stroke-width", l2width(d, e_height))
      .attr("stroke", l1color(d, gray));
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path)
      .attr("fill", "none")
      .attr("stroke-width", l1width(d, e_height))
      .attr("stroke", l2color(d, gray));
}

function draw_user_fast(d, x0, y0, x1, gray, e_height) {
  // Just draw the user top color
  var path = "M" + Rnd1(x0) + "," + Rnd1(y0) + " H" + Rnd1(x1);
  innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path)
      .attr("fill", "none")
      .attr("stroke-width", l2width(d, e_height) + l1width(d, e_height))
      .attr("stroke", fade ? fadecolor : l2color(d, gray));
}

function draw_user(d, x0, y0, x1, gray, e_height) {
//console.log("draw_user", x0, y0, x1, gray, e_height);
    if (0 < fade_value) {draw_user_fast(d, x0, y0, x1, gray, e_height); return;}
    // Draw stacked user colors
    var path1 = "M" + Rnd1(x0) + "," + Rnd1(y0 - (l1width(d, e_height) / 2)) + 
      " H" + Rnd1(x1);
    var path2 = "M" + Rnd1(x0) + "," + Rnd1(y0 + (l2width(d, e_height) / 2)) + 
      " H" + Rnd1(x1);
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path1)
      .attr("fill", "none")
      .attr("stroke-width", l2width(d, e_height))
      .attr("stroke", l2color(d, gray));
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path2)
      .attr("fill", "none")
      .attr("stroke-width", l1width(d, e_height))
      .attr("stroke", l1color(d, gray));
}

function draw_idle_fast(d, x0, y0, x1, gray, e_height) {
  var path = "M" + Rnd1(x0) + "," + Rnd1(y0) + " H" + Rnd1(x1);
  // Draw the idle line
  innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path)
      .attr("fill", "none")
      .attr("stroke-width", l1width(d, e_height))
      .attr("stroke", (0 < fade_value) ? fadecolor : l1color(d, gray));
}

function draw_idle(d, x0, y0, x1, gray, e_height) {
  if (0 < fade_value) {draw_idle_fast(d, x0, y0, x1, gray, e_height); return;}
  var path = "M" + Rnd1(x0) + "," + Rnd1(y0) + " H" + Rnd1(x1);
  // Draw the idle line; dashed if low-power
  if (arg0(d) == 1) {
      innersvgdotg.append("path")
        .attr("class", "graphlinex")
        .attr("d",  path)
        .attr("fill", "none")
        .attr("stroke-dasharray", [8,2])
        .attr("stroke-width", l1width(d, e_height))
        .attr("stroke", "#000000");	// Black dashes 
  } else {
      innersvgdotg.append("path")
        .attr("class", "graphlinex")
        .attr("d",  path)
        .attr("fill", "none")
        .attr("stroke-width", l1width(d, e_height * 1.5))
        .attr("stroke", l1color(d, gray));
    }
}


// More to do in refactoring...
// This does all the actual drawing from pixel values within innersvgdotg.
// All the tracks and decisions and combining have been factored out.
function slowDrawOnRow2(d, x0, x1, y0, y1, e_height, textsize, gray, fade) {
}


// Draw one entry on given track. Gray'd entries: skip marks and IPC and arcs
// if fade, draw kernel/user/idle as single light gray; keep all decorations
function slowDrawOnRow(row, d, gray) {
  var track = rowToTrack[row];
  var t_height = rowToHeight[row];
  var x0 = x_zoomed(ts(d));
  var x1 = x_zoomed(tsend(d));
  // TEMPORARY:  Relative to rowToMinT
  if (0 < state2.basetime0_value) {
    x0 = x_zoomed(ts(d) - rowToMinT[row]) + dataBaseSecond;
    x1 = x_zoomed(tsend(d) - rowToMinT[row] + dataBaseSecond);
//console.log("x0", ts(d), rowToMinT[row], ts(d) - rowToMinT[row] + dataBaseSecond, x0);
  }

  var y0 = y_zoomed(track) + perTracksize[t_height].eventoffset;
  var e_height = perTracksize[t_height].eventheight;

  if (e_height < 7) {
    // Too small to see well -- draw as single line
    fastDrawOnRow(row, d, gray);
    return;
  }

// Hack: if hack is on and we are drawing local_timer, make it at least 5 pixels wide
  if ((state2.hack_value != 0) && (ev(d) == 1516)) {
    x1 = Math.max(x1, x0 + 5);
  }

  var path = "M" + Rnd1(x0) + "," + Rnd1(y0) + " H" + Rnd1(x1);

  // Do early
  if (is_queued(d)) {
    // Draw a queued waiting line, offset up from centerline
    draw_queue(d, x0, y0 - e_height / 4, x1, gray, l1width(d, e_height));
  } else 

  if (is_kernel(d)) {
//console.log("kernel", path);
    if (0 < fade_value) {fastDrawOnRow(row, d, gray); return;}
    // Draw the syscall/irq/fault four lines
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path)
      .attr("fill", "none")
      .attr("stroke-width", l4width(d, e_height))
      .attr("stroke", l4color(d, gray));
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path)
      .attr("fill", "none")
      .attr("stroke-width", l3width(d, e_height))
      .attr("stroke", l3color(d, gray));
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path)
      .attr("fill", "none")
      .attr("stroke-width", l2width(d, e_height))
      .attr("stroke", l1color(d, gray));
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path)
      .attr("fill", "none")
      .attr("stroke-width", l1width(d, e_height))
      .attr("stroke", l2color(d, gray));

  } else if (is_user(d)) {
    if (0 < fade_value) {fastDrawOnRow(row, d, gray); return;}

    // Draw stacked user colors
    var path1 = "M" + Rnd1(x0) + "," + Rnd1(y0 - (l1width(d, e_height) / 2)) + 
      " H" + Rnd1(x1);
    var path2 = "M" + Rnd1(x0) + "," + Rnd1(y0 + (l2width(d, e_height) / 2)) + 
      " H" + Rnd1(x1);
//console.log("user", path1, path2);
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path1)
      .attr("fill", "none")
      .attr("stroke-width", l2width(d, e_height))
      .attr("stroke", l2color(d, gray));
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path2)
      .attr("fill", "none")
      .attr("stroke-width", l1width(d, e_height))
      .attr("stroke", l1color(d, gray));

  } else if (is_idle(d)) {
//console.log("idle", path);
    if (0 < fade_value) {fastDrawOnRow(row, d, gray); return;}

    // Draw the idle line; dashed if low-power
    if (arg0(d) == 1) {
      innersvgdotg.append("path")
        .attr("class", "graphlinex")
        .attr("d",  path)
        .attr("fill", "none")
        .attr("stroke-dasharray", [8,1])
        .attr("stroke-width", l1width(d, e_height))
        .attr("stroke", l1color(d, gray));	// Black dashes 
    } else {
      innersvgdotg.append("path")
        .attr("class", "graphlinex")
        .attr("d",  path)
        .attr("fill", "none")
        .attr("stroke-width", l1width(d, e_height * 1.5))
        .attr("stroke", l1color(d, gray));
    }

  } else if (is_pstate(d)) {
    // Draw a possible slow-clock overlay
    draw_pstate(d, x0, y0, x1, gray, e_height);

  } else if (is_pc_samp(d)) {
    // Draw a PC sample line
    draw_pc_samp(d, x0, y0 - e_height * 0.55, x1, gray, l1width(d, e_height) / 2);

  } else if (is_lock_line(d)) {
    // Draw a lock-held line
    draw_lock_line(d, x0, y0 - e_height * 0.55, x1, gray, l1width(d, e_height) / 2, row);

  } else if (is_c_exit(d)) {
     draw_c_exit(d, x0, y0, x1, gray, e_height);

  } else if (is_rpcpkt(d)) {	// Represents an entire multi-packet message
    // Draw thin line(s) showing approx network occupation
    draw_rpc_msg(d, x0, y0, x1, gray, l1width(d, e_height) / 2);

  } else if (is_pkt(d)) {	// Represents just a single packet
    // Draw thin like showing approx network occupation
    draw_pkt(d, x0, y0, x1, gray, l1width(d, e_height) / 2);

  } else if (is_special(d) && !is_mark(d)) {
    // Mostly IPIsend, RPCmarkers; duration is non-zero
    // Offset 1/3 line up, make 5x wider to be more visible
    right_t = tsrel(d) + 5 * dur(d);
    var path4 = "M" + Rnd1(x0) + "," + Rnd1(y0 - e_height / 3) + " H" + Rnd1(x1);
//console.log("special", path4);
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path4)
      .attr("fill", "none")
      .attr("stroke-width", e_height)
      .attr("stroke", l2color(d, gray));

  } else if (is_wait(d)) {
    // Draw the Morse code for why we are waiting, offset up from centerline
    draw_wait(d, x0, y0 - e_height / 4, x1, gray, l1width(d, e_height) / 2);

  } else if (!gray && is_mark(d)) {
    draw_one_mark(row, d, x0, y0);

  } else {
    // Draw nothing
  }

  // If less than 8 usec wide, Layer on top the overhead indication
  if ((xdomainwidth < gMinShowOvhd) && (is_kernel(d) || is_user(d))) {
    // Draw diagonal white lines to show overhead; 20ns if noipc, 50ns if IPC (approx measurements)
    var ovhd = NsToTs((state2.ipc_value < 0) ? gOverheadNs : gOverheadIpcNs);
    var x2 = x_zoomed(ts(d) + ovhd);
    draw_ovhd(d, x0, y0, x2, e_height);
 }

  // Layer on top the IPC triangle
  if (!gray && (0 < state2.ipc_value)) {
    draw_one_ipcllc(d, (x0 + x1) / 2, y0, e_height * 0.67);
  }
}


// Draw one small entry on given track, using just one line and color. Skip marks and IPC
function fastDrawOnRow(row, d2, gray) {
  var track = rowToTrack[row];
  var t_height = rowToHeight[row];
  var x0 = x_zoomed(ts(d2));
  var x1 = x_zoomed(tsend(d2));
  var y0 = y_zoomed(track) + perTracksize[t_height].eventoffset;
  var e_height = perTracksize[t_height].eventheight;

  var path = "M" + Rnd1(x0) + "," + Rnd1(y0) + " H" + Rnd1(x1);

  if (is_kernel(d2)) {
    // Just draw the syscall/irq/fault background color
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path)
      .attr("fill", "none")
      .attr("stroke-width", l3width(d2, e_height))
      .attr("stroke", (0 < fade_value) ? fadecolor : l3darkcolor(d2, gray));
  } else if (is_user(d2)) {
    // Just draw the user top color
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path)
      .attr("fill", "none")
      .attr("stroke-width", l2width(d2, e_height) + l1width(d2, e_height))
      .attr("stroke", (0 < fade_value) ? fadecolor : l2color(d2, gray));
  } else if (is_idle(d2)) {
    // Draw the idle line
    innersvgdotg.append("path")
      .attr("class", "graphlinex")
      .attr("d",  path)
      .attr("fill", "none")
      .attr("stroke-width", l1width(d2, e_height))
      .attr("stroke", (0 < fade_value) ? fadecolor : l1color(d2, gray));
  } else {
    // Draw nothing
  }
}




// if ev is idle, only overwrite 0 
// if ev is user, only overwrite 0, idle 
// if ev is kernel, overwrite
// if < kernel, skip 
function Replace_ev(old_ev, new_ev) {
  if (new_ev < 0x400) {return false;} 
  if (is_c_exit(new_ev)) {return false;}
  return true;
  if (old_ev == 0) {return true;}
  if (is_kernel_ev(new_ev)) {return true;}
  if (is_user_ev(new_ev) && !is_kernel_ev(old_ev)) {return true;}
  return false;
}

// Draw one event on the specified row, if onscreen and at least one pixel wide, 
// else defer and accumulate tiny spans
// Treat marks separately, allowing multiple kinds at same pixel, but
// not duplicates of the same kind
function draw_on_row(row, d, gray) {
  if (row < 0) {return;}
  if (rowToHeight[row] == 0) {return;}
  var is_onscreen = onscreen_ytrack(rowToTrack[row], rowToHeight[row]);
  if (!is_onscreen) {return;}

//console.log("  draw_on_row", row, rowToTrack[row], rowToHeight[row], d);
  var can_defer = true;
  if (is_mark(d)) {
    can_defer = false;
    // New highwater of ts + 3px for marks if beyond previous highwater
    if (highwater_marks[row] <= ts(d)) {
      highwater_marks[row] = ts(d) + 3 * xsecperpix;
      highwater_mark_type[row] = 0;
    }
    // Delete if same type as an existing mark already at this pixel
    var mark_type = get_mark_type(d);
    if ((highwater_mark_type[row] & mark_type) != 0) {return;}

    highwater_mark_type[row] |= mark_type;
    // But no defer if screen width is less than 50 msec
    //var long_window = (xdomainwidth > 0.050);
  }

  // Always draw RPC lines and raw packets
  if (is_rpcpkt(d)) {can_defer = false;}
  if (is_pkt(d)) {can_defer = false;}
  if (is_dots(d)) {
    can_defer = false;
  }

// Hack: if hack is on and we are drawing local_timer, make it at least 5 pixels wide
  if ((state2.hack_value != 0) && (ev(d) == 1516)) {
    can_defer = false;
  }


  // Important performance optimization with 1M events 
  if (can_defer && ((defer_dur[row] + dur(d)) < xsecperpix)) {
    // Accumulate tiny intervals
    defer_dur[row] += dur(d);
    if (is_idle_c_exit(d)) {
      defer_dur_i[row] += dur(d);
    } else if (is_user(d)) {
      defer_dur_u[row] += dur(d);
      defer_event_u[row] = ev(d);
    } else if (is_kernel(d)) {
      defer_dur_k[row] += dur(d);
      defer_event_k[row] = ev(d);
    }
  } else {
    // Flush deferred and then draw any bigger event
    if (defer_dur[row] > 0) {
      // Flush deferred event, just in front of d
      var d2 = [];
      for (var j = 0; j < 10; ++j) {d2[j] = d[j];}
      set_ts(d2, ts(d) - defer_dur[row]);
      set_dur(d2, defer_dur[row]);
      set_ev(d2, defer_event_k[row]);	// Default to most recent kernel event
      // Bias 10x toward kernel/user over idle
      var dur_i = defer_dur_i[row];
      var dur_u = 10 * defer_dur_u[row];
      var dur_k = 10 * defer_dur_k[row];
      if ((dur_i > dur_u) && (dur_i > dur_k)) {
        set_ev(d2, 0x10000);		// Idle took most time
      } else if ((dur_u > dur_i) && (dur_u > dur_k)) {
        set_ev(d2, defer_event_u[row]);	// Most recent user took most time
      }
      fastDrawOnRow(row, d2, gray);
      resetDefer(row);
    }
    // Defer (if small) or draw (if large) new event
    if (can_defer && ((defer_dur[row] + dur(d)) < xsecperpix)) {
      // Accumulate tiny new event after flushing deferred event
      defer_dur[row] += dur(d);
      if (is_idle_c_exit(d)) {
        defer_dur_i[row] += dur(d);
      } else if (is_user(d)) {
        defer_dur_u[row] += dur(d);
        defer_event_u[row] = ev(d);
      } else if (is_kernel(d)) {
        defer_dur_k[row] += dur(d);
        defer_event_k[row] = ev(d);
      }
    } else {
      // Draw larger event after flushing deferred event
      slowDrawOnRow(row, d, gray);
    }
  }
}

// Highlight if in set, treating empty set as highlight-all
function eventHilite(i) {
  var d = data.events[i];
  if ((0 < state.fade) && is_kui(d)) {return false;}
  if (state.hilite_evnum != 0) {
    // One or two right-clicked events
    if ((state.hilite_evnum != 0) && (ev(d) == state.hilite_evnum)) {return true;}
    if ((state.hilite_evnum2 != 0) && (ev(d) == state.hilite_evnum2)) {return true;}
    return false;
  }
  if (hilite_event_set.size == 0) {return true;}
  return hilite_event_set.has(i);
}


// Full height if in set, treating empty set as highlight-all
function rowFullsize(row) {
  if (state2.grouphashilite[getGroup(row)] == false) return true;
  return state2.rowToHilite[row];
}

// Draw event d on up to four different rows
function drawoneevent(d, gray) {
    var cpu_d = cpu(d);
    if (0 <= cpu_d) {
      row = eventToRow[prefixCpu * 65536 +  low16of(cpu_d)];
//console.log("cpu ________", row);
      if (0 <= row) {draw_on_row(row , d, gray);}
    }

    var pid_d = pid(d);
    if (0 < pid_d) {		// Do not draw pid=0, idle
      row = eventToRow[prefixPid * 65536 + low16of(pid_d)];
//console.log("pid", row);
      if (0 <= row) {draw_on_row(row , d, gray);}
    }

    var rpc_d = rpc(d);
    if (0 < rpc_d) {		// Do not draw rpc=0: not inside an RPC
      row = eventToRow[prefixRpc * 65536 + low16of(rpc_d)]; 
//console.log("rpc", row);
      if (0 <= row) {draw_on_row(row , d, gray);}
    }

    var res_d = res(d);
    if (0 <= res_d) {
      row = eventToRow[prefixRes * 65536 + low16of(res_d)];
//console.log("res", row);
      if (0 <= row) {draw_on_row(row , d, gray);}
    }
}

// Re-draw extras
function drawExtras() {
//console.log("drawExtras", data.extra.length);
  for (var i = 0; i < data.extra.length; ++i) {
    var d = data.extra[i];
    overlaydraw(d, false);
  }
}


// Here is the plan:
// If not onscreen, continue
// Define tiny as less than, say, 1/2 pixel wide. 
// Calc once per draw the duration width this means
// If (defer[cpu] + dur) < xsecperpix, defer[cpu] += dur
// else 
//   fastdraw non-zero defer, set defer[cpu]=0, deferevent[cpu]=none
//   if (defer[cpu] + dur) < xsecperpix, defer[cpu] += dur deferevent[cpu] = event
//   else draw dur
//
// defer dur sum
//    x  x   lg  fastdraw defer, zero it, retry: += dur
//    0  x   sm  defer += dur
//    0  x   lg  draw dur
//    x  x   sm  += dur
//
// The above for is_idle/is_cexit/user/is_kernel spans
// marks:      20a..20d	space them at least xsecperpix*2 apart
// is_special: 20x	space them at least xsecperpix*2 apart
// ipc:			space them at least xsecperpix*2 apart
// is_cexit: only draw if >= 10 * xsecperpix
//

function fastdrawevents() {
//console.log("fastdrawevents [", data.events.length, 
//            "], mindur", xsecperpix, 
//            " ====================================");
  resetevents();
  initializeDrawingState();
  if (data == null) {return;}

  // Draw everything on screen, except overlays and extras
  d3.selectAll(".brackettext").remove() ;

  // Draw everything on screen except overlays
  var row = 0;
  var overlay_set = new Set();
  for (var i = 0; i < data.events.length; ++i) {
    var d = data.events[i];
    if (is_lr_mark(d)) {continue;}
    if (!onscreen_x(d)) {continue;}
    if (is_any_overlay(d)) {
      overlay_set.add(i);	// Defer this one
      continue;
    }

    // Accumulate all on-screen events that match hilite_evnum
    // Do not count secondary evnum2 matches
    //   (follow logic in annotatesearch2)
    if ((ev(d) == state.hilite_evnum) && 
        strictly_startsonscreen_x(d) && onscreen_all_y(d)) {
      IncrMatchcount(d);
    }

    var gray = !eventHilite(i);
    drawoneevent(d, gray);
  }

  // Draw any overlay items 
  if (overlay_set.size != 0) {
//console.log("has_overlay", overlay_set.size);
    overlay_set.forEach(function(i) {
      var gray = !eventHilite(i);
      var d = data.events[i];
      if (is_any_overlay(d)) {overlaydraw(d, gray);}
    });
  }

  drawExtras();
}

// BUG: strictly speaking, the left/right top/bottom should map just the
//   +/-5 pixel domain, not full region3width/region234height.
//   But the scaling below is correct, since both range and domain are large by 5px

// Width across the diagram is region3width pixels
// Width across the diagram is xdomainwidth seconds
// One pixel is xdomainwidth / width seconds
function reset_globals_x(xleft, xright, xrightmost) {
//console.log("reset_globals_x", xleft, xright);
  realxleft = xleft;
  realxright = xright;
  state2.xleft_ts = xleft;
  state2.xright_ts = xright;
  // Update x start and width text
  document.getElementById("xstartwidth").innerHTML =  
      RndUsec(state2.xleft_ts) + "+" + Rnd0(RndUsec(state2.xright_ts - state2.xleft_ts) * 1000000);

  realxrightmost = xrightmost;
  xdomainwidth = Math.abs(xright - xleft);
  xsecperpix = (xdomainwidth / region3width) * xpixelfraction;
//console.log("NSEC per pixel", Math.round(xsecperpix * 1000000000.0));
}

// Height across the diagram is region234height pixels
// Height across the diagram is ydomainwidth tracks
// One track is region234height / ydomainwidth pixels
//   Note: this is the inverse of the ratio used for X-axis time above
function reset_globals_y(ytop, ybottom) {
//console.log("reset_globals_y tracks:", ytop, ybottom);
  state2.ytop_track = ytop;
  state2.ybot_track = ybottom;
  ytop_row = trackToRow(ytop);
  ybot_row = trackToRow(ybottom);
  // Update y start and width text
  document.getElementById("ystartwidth").innerHTML =  (state2.ytop_track) + "+" + (state2.ybot_track - state2.ytop_track);

  realytop = ytop;
  realybottom = ybottom;
  ydomainwidth = Math.abs(ytop - ybottom);
  ypixpertrack = (region234height / ydomainwidth) * ypixelfraction;

//console.log("    y px/trk:", region234height, ydomainwidth, "=", ypixpertrack);
  calcPixHeightsPerTrack(ypixpertrack);
}

// Because of auto-pruning of Y-axis rows that cannot possibly overlap the X span
// on screen, updating X will in general require also updating the Y-axis
function do_zoomed_x2(arg_transform, arg_m) { 
//console.log("do_zoomed_x2 ----------", arg_transform, arg_m);
//  if (ignore_zoom) {return;}
  // Pick up  the new transform and apply it
  state2.savedtransformX = arg_transform;
  state2.savedtransformX.y = 0;	// Scrolling X does not change Y
  // Remember nearest annotated event to the mouse click, to display after pan
  var m = arg_m;
	
  // Renumber the track heights per row, to auto-prune rows no longer visible
  // Use x_zoomed when drawing everything after this  
  // Set up x_zoomed, y_zoomed and Recalculate X- and Y- on-screen limits
  updateTracks();
  y.domain([0, maxTrack]);
  resetRangeDomain();
  calcPixHeightsPerTrack(ypixpertrack);

  // Remember nearest annotated event to the mouse click, to display after pan
  var mx = m[0];
  var my = m[1];
  var t = x_zoomed.invert(mx);
  var y_track = y_zoomed.invert(my);
  if (0 <= y_track) {
    state2.annotated_one_d = FindNearestAnnotated(t);
  }

  // Turn off annotateall/user after pan/zoom
  state2.annotateall_value = 0;
  state2.annotateuser_value = 0;

  redraw_events_axes();

//console.log("end do_zoomed_x ----------");
}

// This is called for both pan and zoom
function do_zoomed_x() { 
//console.log("do_zoomed_x ----------", d3.event.transform);
  state.just_reset = false;
  var m = d3.mouse(this);
  do_zoomed_x2(d3.event.transform, m);
}

// Tracks contain event lines and annotation text, split in some proportion
// The pixel height for a trackheight is ypixpertrack * t_height
// The font-size for annotation text has a minimum and grows slowly, allowing
// room for 0..4+ lines depending on zoom. It is 8 + (xx - 8) / 6
// Event offset is forced to be an integer number of pixels, to allow
//  subscripting by Y-position
// NO. give up on offsets being exact integers. use rnd1
function calcPixHeightsPerTrack(pixpertrack) {
//console.log("calcPixHeightsPerTrack", pixpertrack);
  // Calculate sizes and offsets per track heights 1..20
  // Minimum annot font size is forced into range [6..48]
  var minannot = Math.min(48, Math.max(6, 0.8 * textsize.smalltick));
  // Apply debug row count if forced
  var cur_tracks = maxTrack;

  for (var i = 0; i <= maxTrackHeight; ++i) {	// Note the <=, 0..20
    perTracksize[i] = {};
    var trackheight = Rnd1(i * pixpertrack);
    var annotfontsize = minannot;
    var e_height;
    if (minannot < trackheight) {
      // Grow slowly, 5% of extra pixels
      annotfontsize += Rnd0((trackheight - minannot) / 20);
    }

    if ((annotfontsize * 2) <= trackheight) {
      // Normal case, allocate half the track height to events, half to annots
      e_height = Rnd1(trackheight / 2);
      perTracksize[i].eventheight = e_height;
      perTracksize[i].eventoffset = Rnd1(e_height / 2);		// Centered in top half
      perTracksize[i].annotheight = trackheight - e_height;
      perTracksize[i].annotoffset = Rnd1(e_height + annotfontsize);  // One line down
    } else if (annotfontsize <= trackheight) {
      // No room for annotations; hold event line height steady
      e_height = Rnd1(annotfontsize);
      perTracksize[i].eventheight = Rnd1(annotfontsize);
      perTracksize[i].eventoffset = Rnd1(e_height / 2);	// Centered near top
      perTracksize[i].annotheight = 0;
      perTracksize[i].annotoffset = Rnd1(e_height + annotfontsize * 0.67); // Intrudes next row 
    } else {
      // No room for annotations and need to shrink event height
      e_height = Rnd1(trackheight);
      perTracksize[i].eventheight = e_height;
      perTracksize[i].eventoffset = Rnd1(e_height / 2);	// Centered near top
      perTracksize[i].annotheight = 0;
      perTracksize[i].annotoffset = Rnd1(e_height + annotfontsize * 0.67);  
    } 
    perTracksize[i].annotsize = annotfontsize;
    perTracksize[i].annotcount = 
      Math.max(1, Rnd0(perTracksize[i].annotheight / annotfontsize));

    // 2021.06.03 if not much space and textsize.txt=0, have minimum span height
    // 2021.01.16 if not much space, use value less than textsize.txt
    // In particular, if txtsize is less than a readable 10 px, logically reduce textsize.txt
    // and apportion more space to spans: 
    var spn = textsize.spn;	// Proportion of a row for drawing spans
    var txt = textsize.txt;	// Proportion of a row for drawing annotation text lines
    var maxlinesperrow = Math.trunc(trackheight / min_txt_px);
    txt = Math.min(txt, Math.max(0, maxlinesperrow - spn));	// Reduce as needed
//console.log("spn txt", textsize.spn, textsize.txt, trackheight, min_txt_px, "=", spn, txt);
    // Debug: set timeline event height to fraction of track height
    if (0 < spn) {
      // Text size does change. Track height does not change, 
      //  but we need to apportion pixpertrack based on spn + txt
      var txtsize = annotfontsize;
      if (0 < textsize.annot) {txtsize = Math.min(txtsize, textsize.annot);}
      e_height = Rnd1(trackheight * spn / (spn + txt));
      // Mostly for presentations. If txt=0, force minimum span height, set by spn
      if (textsize.txt == 0) {e_height = Math.max(textsize.spn * 4, e_height);}

      perTracksize[i].eventheight = e_height;
      perTracksize[i].eventoffset = Rnd1(e_height / 2);		// Centered near top
      perTracksize[i].annotheight = trackheight - e_height;
      // Fit annot font size but no bigger than basictextparamH max 30px
      var maxpix = basictextparamH[2];
      txtsize = Rnd5(Math.min(maxpix, Rnd1(perTracksize[i].annotheight / Math.max(1, txt))));
      txtsize = Math.max(min_txt_px, txtsize);
      textsize.annot = txtsize;  ////??
      perTracksize[i].annotoffset = Rnd1(e_height + txtsize);  	// One line down 
      perTracksize[i].annotsize = txtsize;
      perTracksize[i].annotcount = Math.max(1, txt);
//if (i == maxTrackHeight) console.log("trackheight[20]", trackheight, perTracksize[i].eventheight, perTracksize[i].eventoffset,
// perTracksize[i].annotheight, perTracksize[i].annotoffset, perTracksize[i].annotsize, perTracksize[i].annotcount);
    }
  }
}

// This is called for both pan and zoom
function do_zoomed_y() { 
//console.log("do_zoomed_y ----------", d3.event.transform.k);
  state.just_reset = false;
  // Pick up  the new transform and apply it
  state2.savedtransformY = d3.event.transform;
  state2.savedtransformY.x = 0;	// Scrolling Y does not change X
//console.log("zoom rows=", textsize.rows, "x/y/k", state2.savedtransformY.x, state2.savedtransformY.y, state2.savedtransformY.k);

  // Set up offsets and line widths per track heights [1..10] 
	
  // Renumber the track heights per row, to auto-prune rows no longer visible
  updateTracks();
  y.domain([0, maxTrack]);
  resetRangeDomain();
  calcPixHeightsPerTrack(ypixpertrack);

  // Nothing below this depends on d3.event
  // ---------------------------------------

  // Turn off annotateall/user after pan/zoom
  state2.annotateall_value = 0;
  state2.annotateuser_value = 0;

  redraw_events_axes();

//console.log("end do_zoomed_y ----------");
}
 
function CheckHilites(label, hilite) {
  var ok = true;
  for (var i = 0; i < hilite.length; ++i) {
    if (1 < hilite[i]) {ok = false;}
    if (hilite[i] < 0) {ok = false;}
  }
  if (!ok) {console.log(label, "FAIL", hilite);}
}

// Expecting two equal arrays
function CheckSame(a, b) {
  if (a.length != b.length) {
    console.log("CheckSame length mismatch:", a.length, b.length);
    console.log(a, b);
    return;
  }
  for (var i = 0; i < a.length; ++i) {
    if (a[i] != b[i]) {
      console.log("CheckSame element mismatch:", i, a[i], b[i]);
    }
  }
}


// Recalculate event highlights from just the highlighted rows
function RecalcEventHilites() {
  hilite_event_set.clear();
  for (var row = 0; row < sortNumSize; ++row) {
    if (state2.rowToHilite[row]) {
      setEventHilites(row, true);
    }
  }
}

// Update group has-highlights bits from rowToHilite
function RecalcGroupHilites() {
  state2.grouphashilite = [0, 0, 0, 0];
  for (var row = 0; row < sortNumSize; ++row) {
    state2.grouphashilite[getGroup(row)] |= state2.rowToHilite[row];
  }
}

// Recalculates globals derived from just-changed main state
function RecalcDerivedState() {
  CheckHilites("Recalc",  state2.rowToHilite);
  calc_regionheightwidth();
  currentcolor = (state2.cb_value == 0) ? normalcolor : colorblindcolor;
  RecalcEventHilites();		// from rowToHilite
  RecalcGroupHilites();		// from rowToHilite
}

// Returns a string from state
// Changes no state.
function stringifyState(state) {
  // Note that JSON does not save sets.
  // Note: it is difficult to get JavaScript to do true deep copy
  return JSON.stringify(state);
}

// Sets the variable state from string
function unstringifyState(savedstr) {
  return JSON.parse(savedstr);
}
 
function InsertZerocount(zerocount, retval) {
  if (zerocount == 0) {return;}
  if (zerocount == 1) {retval.push(0); return;}
  retval.push(zerocount);
}

// Compress rowToHilite array. Elements are all 0 or 1, with a very high density of zeros.
// We replace strings of 2 or more zeros by the count.
// Idempotent if already compressed
function CompressHilite(hilite) {
  var retval = [];
  var zerocount = 0;
  for (var i = 0; i < hilite.length; ++i) {
    if (hilite[i] != 0) {
      InsertZerocount(zerocount, retval);
      zerocount = 0;
      retval.push(+hilite[i]);
    } else {
      ++zerocount;
    }
  }
  InsertZerocount(zerocount, retval);
  return retval;
}

// Replace values >= 2 with that many false elements. Idempotent if already decompressed.
function DecompressHilite(hilite) {
  var retval = [];
  for (var i = 0; i < hilite.length; ++i) {
    if (2 <= hilite[i]) {
      for (var j = 0; j < hilite[i]; ++j) {retval.push(false);}
    } else {
      retval.push(hilite[i] != 0);
    }
  }
  CheckHilites("Decomp",  retval);
  return retval;
}

// Stringify state after compressing the rowToHilite array
function CompressStringify(stateobj) {
  var inval;
  if (stateobj === null) {return stateobj;}
  if (typeof stateobj === 'string') {
console.log("BUG: CompressStringify called with a string");
    inval = unstringifyState(stateobj);
  } else {
    inval = stateobj;
  }
  // NOTE: if the argument is state2,state2.rowToHilite is MODIFIED here !
  var temp = inval.rowToHilite;
  var temp1 = CompressHilite(inval.rowToHilite);
  var temp2 = DecompressHilite(temp);
CheckSame(temp, temp2);

  inval.rowToHilite = temp1;
  var retval = stringifyState(inval);
//console.log("CompressStringify", retval);
  inval.rowToHilite = temp;	// Restore original
  return retval;
}

// Unstringify the state and then decompress the roToHilte array
function DecompressUnstringify(stateobj) {
  var retval;
  if (typeof stateobj === 'string') {
    retval = unstringifyState(stateobj);
  } else {
console.log("BUG: DecompressUnstringify called with a non-string");
    retval = stateobj;
  }
  retval.rowToHilite = DecompressHilite(retval.rowToHilite);
  //console.log("DecompressUnstringify", retval);
  return retval;
}

// Save state, including awkward Sets, without changing anything
// Returns a string
function packupState() {
  return CompressStringify(state2);
}

// After restoring state, put Sets and derived main state variables back
function unpackState(savedstr) {
//console.log("unpackState", savedstr);
  state2 = DecompressUnstringify(savedstr);
  CheckHilites("Unpack", state2.rowToHilite);
  RecalcDerivedState();
}

// Reset zoom to original full trace
// Reset a few data-related pieces of state
// Save to state[0] just before resetting, in case reset is a mistake
function resetzoom() {
//console.log("resetzoom ==================");
  fade_value = 0;
  if (d3.event.shiftKey) {
    // Secondary menu toggle on reddot 
    document.getElementById("debugtext").hidden = debug;
    debug = !debug;
    //presentation = !presentation;
    return;
  }

  data.savedview[0][1] = packupState();
  decorateSave(document.getElementById("view0"), true);

  // Reset parts of state
  state2.groupcompress = [2, 0, 0, 0];	// Show CPUs, collapse others
  hilite_event_set.clear();
  state.just_reset = true;
  initializeState();

  allocOuterSvgEtc();
}

//BUG
//BUG: on restore, sometimes RES ends up too low, below region2
//BUG  it appears that auto prune does not redo updateTracks, so track zoom 
//BUF  can to too big or too small. most easily seen when auto zoom and
//BUG  bottom pops up, rather than changing zoom slightly.
//BUG  then save with auto pruned followed by restore and then un-prune expands 
//BUG  below region2.
//BUG


// Zoom the X-axis to start at left, width width
function ZoomToLeftWidth(xleft, xwidth) {
//console.log("ZoomToLeftWidth", xleft, xwidth);
  // Avoid zdiv if tinier than 10 nsec full screen width
  if (xwidth < NSEC10) {xwidth =NSEC10;}

  // Calculate new scale based on desired width vs. scale=1.0 width
  // x is a function that maps times to pixels:
  //   x.domain([dataTsLo, dataTsHi]);
  //   x.range([axesmargin, region3width - axesmargin])
  //   state2.savedtransformX = d3.zoomIdentity;
  //   x_zoomed = state2.savedtransformX.rescaleX(x);
  // When we zoom in,
  //   the scale k: gets larger, so that tslo + e maps to axesmargin + k * e
  // When we pan by dragging left,
  //   the offset x: in pixels gets more negative
  // x w/identity tslo => axesmargin : (ts - tslo) * xxx + pixlo 
  //   where xxx = (pixhi - pixlo) / (tshi - tslo) 
  // x w/zoom tslo => axesmargin : (ts - tslo) * xxx * k - x + pixlo 
  //   where xxx = (pixhi - pixlo) / (tshi - tslo) 
  // so if new time width is W smalleer than initial, scale k is  (tshi - tslo) / W
  // For scale 1, putting TS at left edge means pix offset of
  //   - (TS - tslo) * xxx 
  // For scale 2, putting TS at left edge means pix offset of
  //   - (TS - tslo) * xxx * 2
  // var new_xk = full_ts_width / new_ts_width;
  // var new_xx = -(ts - tslo) * new_xk mapped to pix : * full_pix_width / full_ts_width;
  //
  // The D3 base mapping maps to 0 not pix0 (e.g. 5):
  // So the calculation is tslo * mul - offset to get to 5. offset = tslo*mul - 5
  //                       tslo * mul * k - offset to get to 5     = tslo*mul*k - 5
  // so difference is 5*(k-1)
  
  var px0 = x.range()[0];
  var px1 = x.range()[1];
  var ts0 = x.domain()[0];
  var ts1 = x.domain()[1];
  var full_pix_width = px1 - px0;
  var full_ts_width = ts1 - ts0;
  var mul = full_pix_width / full_ts_width;
  //console.log("px0/1 ts0/1", px0, px1,  ts0, ts1);
  //console.log("full_pix full_ts", full_pix_width, full_ts_width, mul);
 

  var new_xk = full_ts_width / xwidth;
  var new_xx = -(xleft - ts0) * new_xk * mul;
  new_xx -= px0 * (new_xk - 1);			// 5*(k-1)
  var new_xy = state2.savedtransformX.y;	// Unchanged
//console.log("k: x: y:", new_xk, new_xx, new_xy);
  // These reset the transformations to identity, overwriting 
  // savedtransformX/Y, which cannot easily be assigned because 
  // they are functions, not just simple variables.
  state2.savedtransformX = d3.zoomIdentity.translate(new_xx, new_xy).scale(new_xk);

  // These are needed to have the restored transforms stick
  panzoomrect_x.call(myZoom_x.transform, state2.savedtransformX);
  panzoomrect_x.call(myZoom_x);
  
  delayed_redraw_events_axes();
}

const OneTwoFive = 
  [400, 200, 100, 40, 20, 10, 4, 2,  1,  0.4, 0.2, 0.1, 0.04, 0.02, 0.01, 0.004, 0.002];
  
// Expecting something like 59:33.123-- or 33.9
// With respect to something like base time of 17:59:00
// Seek to center the display on target time, width based
// on +/-5 in last fraction digit and 1/2/5 zoom in/out
function SeekTime(needle) {
  //console.log("SeekTime", needle);
  var colon_pos = needle.search(/:/);
  var period_pos = needle.search(/\./);
  var suffix_pos = needle.search(/\+|\-/);
  if (suffix_pos < 0) {suffix_pos = needle.length;}
  //console.log("  ", colon_pos, period_pos, suffix_pos);
  // Check for valid
  if (period_pos < 0) {return;}

  // We want target center time to be seconds from dataBaseSecond
  var target = 0;
  // Minutes
  if (colon_pos > 0) {
    // We want the user-supplied minute to be >= dataBaseSecond minute
    var base_minute = Math.floor(dataBaseSecond / 60) % 60;
    var needle_minute = +needle.substr(0, colon_pos);
    if (needle_minute < base_minute) {return;}
    target += (needle_minute - base_minute) * 60;
    //console.log("  target:", target);
  }
  // Seconds
  target += +needle.substr(colon_pos + 1, suffix_pos - colon_pos - 1);
  //console.log("  target", target, dataBaseSecond);
  
  // We want the width on each side of the center to be +/- 5 in the last digit
  // adjusted up/down from there
  var fraction_digits = suffix_pos - period_pos - 1;
  var sidewidth = Math.pow(10, -fraction_digits) * 5;
  // Accumulate suffix adjustment
  var plusminus = 0;
  for (var i = suffix_pos; i < needle.length; ++i) {
    if (needle[i] == '+') {++plusminus;}
    if (needle[i] == '-') {--plusminus;}
  }

  plusminus = Math.max(-8, Math.min(plusminus, 8));
  sidewidth *= OneTwoFive[plusminus + 8];
  //console.log("  sidewidth", sidewidth);
  
  // Change the zoom to cover the x-axis range
  //   target - sidewidth * 1.025 to target + sidewidth * 1.025
  // The extra 2.5% on each side makes the endpoint labels visible
  sidewidth *= 1.025;
  ZoomToLeftWidth(target - sidewidth, 2 * sidewidth);
}
 

// Convert the current position and width into the nearest encompassing
// value for SeekTime
function SeekCurrent(needle) {
  //console.log("SeekCurrent", needle);
  // take current width and round up to 1/2/5
  var ts_half_width = (realxright - realxleft) / 2;
  var center = realxleft + ts_half_width;
  
  var atleast = NSEC10;		// There will be 2.5% slop on both ends in SeekTime
  var i;			// Round up here to 3% to make multiple @@'s stable
  for (i = 0; i < 8; ++i) {
    if (ts_half_width <= atleast * 5 * 1.03) {break;}
    atleast *= 10;
  }
  // Now have atleast*5 = 50ns 500  5us 50 500  5ms 50 500 
  var fraction_digits = 8 - i;
  var sidewidth = atleast * 5;
  var suffix = "";
  if (ts_half_width <= atleast * 2 * 1.03) {
    sidewidth = atleast * 2;
    suffix = "+";
  }
  if (ts_half_width <= atleast * 1.03) {
    sidewidth = atleast;
    suffix = "++";
  }
  if (fraction_digits == 0) {
    fraction_digits = 1;
    suffix += "---"
  }

  //console.log ("ts_half_width", center, ts_half_width, "<= sidewidth", sidewidth, suffix);
  var seektarget = center.toFixed(fraction_digits) + suffix;
  //console.log("SeekCurrent=", seektarget);
  state2.search_text = seektarget;
  resetText(document.getElementById("SearchText"), seektarget);
  // Go there
  SeekTime(seektarget);
}

//
// Annotate all items on whose name contains search string and 
// whose duration dur is mindur <= dur <= maxdur. 
// Or if search is a time, seek to that time
//  18:40.123 seeks to minute 18:40.123, with width 5 msec on each side
//  18:40.1234 seeks to minute 18:40.1234, with width 500 usec on each side
//  18:40.1234+ seeks to minute 18:40.1234, with 200 usec on each side
//  40.12-- seeks to second 40.12, with width not 50 msec but 200 msec on each side
//    Minute is optional. Use values above 60 as needed across an hour boundary.
//    Initial width is +/- 1 in the last fraction digit, minimum one digit
//    Multiple + or - suffixes change widths in 1/2/5 pattern, + zooming in, - out
//  @@ seeks close to the current time, with normalized width
//
function annotatesearch2(needle, mindur, maxdur) {
//console.log("annotatesearch2", needle, mindur, maxdur);
  // Remove any previous lines
  resetlines();
  document.getElementById("matchcount").innerHTML = "";

  // xx:xx.xxx+- or xxx.xxx+-
  if (needle.search(/^(\d\d?:)?\d\d?\d?\.[0-9]+(\+|\-)*$/) >= 0) {
    SeekTime(needle); 
    return;
  }
  if (needle.search(/^@@$/) >= 0) { 
    SeekCurrent(needle); 
    return;
  }
  
  // Ignore 1-char searches with zero durations -- too many matches
  if (!IsActiveText(needle) && !IsActiveNumber(mindur) && !IsActiveNumber(maxdur)) {return;} 

  var has_time_bounds = (0 < mindur) || (0 < maxdur);	// User specified min..max
  var units_divisor = units_div[state2.minmax_units_value];
  var mindurval = +mindur / units_divisor;	// Minimum duration converted to seconds
  var maxdurval = +maxdur / units_divisor;	// Maximum duration converted to seconds
  if (maxdurval == 0) {maxdurval = 999.9;}	// Turn empty box into ~17 minutes max

  state.annotated_d = [];
  state2.annotated_one_d = -1;
  state.hilite_evnum = 0;
  state.hilite_evnum2 = 0;

  resetRotatingLabelk();

  // Draw all matching annotation lines
  var min_ts = 0;	// Highwater mark for drawing only one per xsecperpix
  InitMatchcount();
  if (data == null) {return;}
  var needle_hyphen = (needle[0] == '-');
  for (var i = 0; i < data.events.length; ++i) {
    if (!eventHilite(i)) {continue;}	// Only count highlighted events
    var d = data.events[i];
    if(strictly_startsonscreen_x(d) && onscreen_all_y(d)) {	// On screen
      var hit = (fullname(d, false).search(needle) >= 0);	// Name match
      // 2024.04.01 also match on ipc=xxx or llc=xxx if 
      // ipc(d) nonzero and item duration is > 10ns (skips markers, etc.)
      // Note that this match is case-senstive and a regex, so use 
      // ipc=3\. to distinguish from ipc=3/8
      // or llc=64$ to distinguish from llc=64K
      if ((ipc(d) > 0) && (dur(d) > NSEC10) && (needle[3] == '=')) {
        var tmptext = "";
        if (has_ipc) {tmptext += " ipc=" + ipcvalues[realipc(d)];}
        if (has_llc) {tmptext += " llc=" + llcvalues[realllc(d)];}
        hit |= (tmptext.search(needle) >= 0);
      }
      hit &= ((name(d)[0] != '-') || needle_hyphen);		// But not initial hyphen unless in needle
      ////hit |= (rpc(d).toString().search(needle) >= 0); 	// Or rpcid (not in fullname)
      hit &= (mindurval <= dur(d)) && (dur(d) <= maxdurval);	// And within duration range
      // Invert search match if state2.searchnot_value = true
      if (hit != state2.searchnot_value) {
        // Draw if not on top of previous
        // PC samples use right end, not left
        var tsrel_d = is_pc_samp(d) ? tsrelend(d) : tsrel(d);
        if (min_ts <= tsrel_d) {
          var short = true;
          var rotate = true;
          annotatespan(d, "first", short, -1, rotate);
          // Track duplicate x-positions if not presorted
          if (!RowsArePresorted()) {
            min_ts = tsrel(d) + xsecperpix;
          }
          state.annotated_d.push(i);
          if(state2.annotated_one_d == -1) {state2.annotated_one_d = i;}  // Keep first
        }
        // Count even if not drawn
        IncrMatchcount(d);
      }
    }
  }
  // Show the numeric results
  ShowMatchcount();
}
 
// Linear search for nearest event ts(d) <= t in given row
// If not displaying frequency, ignore those entries
// If not displaying PC samples, ignore those entries
// If displaying PC samples and high_click, find only those entries
// hilo_click is +1 for high, -1 for low, and 0 for normal click near row midline
// Never hit on row start markers
function LinearFindNearestEvent(events, t, row, rr, hilo_click) {
//console.log("LinearFindNearestEvent", hilo_click);
  var pc_only = (0 < state2.samp_value) && (hilo_click == 1);
  var lock_only = (0 < state2.lock_value) && (hilo_click == 1);
  var freq_only = (0 < state2.freq_value) && (hilo_click == -1);
  var nearest = -1;
  var nearestdist = 0;
  for (i = 0; i < events.length; ++i) {
    var d = data.events[i];
    if (t < ts(d)) {continue;}
    if (is_pstate(d) != freq_only) {continue;}
    if (is_pc_samp(d) != pc_only) {continue;}
    if (is_lock_line(d) != lock_only) {continue;}
    // If nested locks, pick first one that reaches t
    if (is_lock_line(d) && (tsend(d) < t)) {continue;}
    if (is_lr_mark(d)) {continue;}
    if (rr(i) != row) {continue;}
    var dist = t - ts(d); 
    if ((nearest < 0) || (dist < nearestdist)) {
      nearest = i;
      nearestdist = dist;
    }
  }
  return nearest;
}

// on mousedown -- start line at backmap of pos clientX clientY 
//  button=0 and shiftKey=true , remove any prior visible line
//  create mousemove mouseup listener
// mousemove -- continue line, redraw  clientX clientY
// moutseup -- abandon line if shiftKey=true
//                   keep line visible if shiftKey=false
// mouseout mouseleave -- ignore until mousein
// mouseenter mouseover
// d3.event.stopPropagation()
 
// To snap to nearest point, we have mouse point x,y scaled to data units and data array.
// Taking advantage of data being sorted by x value, we use bisect (binary search) to 
// find a nearby point, then refine based on two nearby points to see closest x**2 + y**2. 
// THIS: Or not bother and just use closest x. Snap x1y1 or x2y2 to that point.
// Consider not snapping if too far away.
 
// For convenience, we want to binary search in raw client pixels.
// So in function(d) we map data into range/domain and then into pan/zoom
 

// TODO here:
//
// 1) map x-pixel to nearest ts(d):
//    map x-pixel to time
//    binary search time in ts(d), ignoring cpu# etc., giving data events subscript i
//    then refine to find closest ts..tsend range or perfect overlap
// 2) map y-pixel to nearest row:
//    map y-pixel to track
//    binary search track in rowToTrack/Height, giving y-row
//    then refine to find closest track..track+height range or perfect overlap,
//      favoring largest or at least non-zero height
//
// annotate does y-pixel nearest then x-pixel nearest, restricting to row(event) == y-row
//   for some field of the event. We can specify the field access function ahead of time
//
// drag works like annotate
//
// highlighting and expand/contract do y-pixel nearest == y-row
//   (no need for four group watchers)
//   highlight then scans onscreen events and adds any with 
//     row(event) == y-row to highlight set
//

// For convenience, we want to binary search in raw client pixels.
// So in function(d) we map data into range/domain and then into pan/zoom
 
// Snap raw input [x,y] pair to nearest x in data, returning [x',y'] of that point
// Note that we only look at the range [1..N-1] so that i-1 is always valid

// Map input x-pixel to time and then to nearest event in given row, using accessor rr
// Returns -1 if none found
// lg N search in events, unless presorted
function doSnap_x(x, row, rr, hilo_click) {
  var t = x_zoomed.invert(x);	// Time corresponding to x horizontal pixels
  var i = 1;
  if (RowsArePresorted()) {
    // Linear search; use if events array is not fully sorted by timestamp
    i = LinearFindNearestEvent(data.events, t, row, rr, hilo_click);
    return i;
  }
    
  // Lg N search; depends on events array being fully sorted by ts(d)
  var bisectPoint = d3.bisector(function(d) {return ts(d);}).left;
  i = bisectPoint(data.events, t, 1, data.events.length - 1);
  // Note: resulting subscript is one above our real match; see i-1 below

//console.log("doSnap_x(", x, ",", row, "hilo", hilo_click, ") = ", i, data.events[i], "time=", t);

  // From this starting point, scan backward to find first entry for
  // desired row. Find pc_samp or lock_line if enabled and hilo_click matches
  // We want the subscript of the entry d such that it is in row and
  //  ts(d) <= t
  // For overlapping timespans, we actually prefer one that includes the clicked 
  //  time t, i.e. d such that ts(d) <= t <= tsend(d)
  var pc_only = (0 < state2.samp_value) && (hilo_click == 1);
  var lock_only = (0 < state2.lock_value) && (hilo_click == 1);
  var freq_only = (0 < state2.freq_value) && (hilo_click == -1);
  var lo_i, hi_i;
  for (lo_i = i - 1; 0 <= lo_i; --lo_i) {
//console.log("        (", x, ",", row, ") = ", lo_i, data.events[lo_i]);
    var d = data.events[lo_i];
    if (is_pstate(d) != freq_only) {continue;}
    if (is_pc_samp(d) != pc_only) {continue;}
    if (is_lock_line(d) != lock_only) {continue;}
    // Added 2021.04.22, might not be right yet. 
    if (t < ts(d)) {break;}		// We backscanned too far
    if (tsend(d) < t) {continue;}	// Doesn't overlap t

    // If nested locks, pick first one that reaches t
    if (is_lock_line(d) && (tsend(d) < t)) {continue;}
    if (rr(lo_i) == row) {break;}
  }
	
  // Exits lo_i = -1 if not found

  hi_i = data.events.length;

//console.log("  expand", i, "lo,hi", lo_i, hi_i);

// TODO: break early if not onscreen
  if ((lo_i < 0) && (data.events.length <= hi_i)) {return -1;}	// Not found	
  if (data.events.length <= hi_i) {return lo_i;}		// No hi	
  if (lo_i < 0) {return hi_i;}					// No lo	

  var lo_point = ts(data.events[lo_i]);
  var hi_point = ts(data.events[hi_i]);
//console.log("  ", t, lo_point, hi_point);
  var nearest = lo_i;
  if (Math.abs(t - lo_point) > Math.abs(hi_point - t)) {nearest = hi_i;}
//console.log("  = i", nearest);
  return nearest;
}

function trackToRow(track) {
  var bisectPoint = d3.bisector(function(rtt){return rtt;}).left;
  var row = bisectPoint(rowToTrack, track, 1, rowToTrack.length - 1);
  return row;
}

// Map input y-pixel to track and then to nearest row
// Lg N search in rows
function doSnap_y(y) {
  var track = y_zoomed.invert(y);
  var row = trackToRow(track);
//console.log("doSnap_y =", y, Rnd1(track), row, rowToTrack[row]);

  // Allow click 25% above to top of a row
  var split_track = rowToTrack[row] - (rowToHeight[row] / 4);
  if (track < split_track) {--row;}

  // Put onscreen
  if (row < ytop_row) {row = ytop_row;}
  if (ybot_row < row) {row = ybot_row;}
//console.log("doSnap_y =", y, Rnd1(track), row, rowToTrack[row]);


//  var lo_point = rowToTrack[row - 1] + (rowToHeight[row - 1] / 3);
//  var hi_point = rowToTrack[row] + (rowToHeight[row] / 3);
//  if (row < 1) {lo_point = 0;}
//console.log("  ", Rnd1(track), "[", row-1, "]", Rnd1(rowToTrack[row - 1]), "[", row, "]", Rnd1(rowToTrack[row]));
//console.log("  ", Rnd1(track), "[", row-1, "]", Rnd1(lo_point), "[", row, "]", Rnd1(hi_point));
//  // Which row is closer?
//  if ((track - lo_point) < (hi_point - track)) {--row;}
//console.log("  = row ", row);
//console.log("doSnap_y = row ", row, track < rowToTrack[row] ? "high" : "normal");

  return row;
}

// These functions return a value from an event
var ff_cpu = function(d) {
  if (0 <= cpu(d)) {
    return cpu(d);
  }
  return -1;
}

var ff_pid = function(d) {
  if (0 < pid(d)) {
    return pid(d);
  }
  return -1;
}

var ff_rpc = function(d) {
  if (0 < rpc(d)) {
    return rpc(d);
  }
  return -1;
}

var ff_res = function(d) {
  if (0 <= res(d)) {
    return res(d);
  }
  return -1;
}

function getAccessor(row) {
  if (row < sortNum_pid_start) return (ff_cpu);
  if (row < sortNum_rpc_start) return (ff_pid);
  if (row < sortNum_res_start) return (ff_rpc);
  return (ff_res);
}



// These functions return a row number from an event
var rr_cpu = function(i) {
  var d = data.events[i];
  if (0 <= cpu(d)) {
    return eventToRow[prefixCpu * 65536 + low16of(cpu(d))];
  }
  return -1;
}

var rr_pid = function(i) {
  var d = data.events[i];
  if (0 < pid(d)) {
    return eventToRow[prefixPid * 65536 + low16of(pid(d))];
  }
  return -1;
}

var rr_rpc = function(i) {
  var d = data.events[i];
  if (0 < rpc(d)) {
    return eventToRow[prefixRpc * 65536 + low16of(rpc(d))];
  }
  return -1;
}

var rr_res = function(i) {
  var d = data.events[i];
  if (0 <= res(d)) {
    return eventToRow[prefixRes * 65536 + low16of(res(d))];
  }
  return -1;
}

// Return an accessor that maps an entry to a row within the right group
function getAccessorRR(row) {
  if (row < sortNum_pid_start) return (rr_cpu);
  if (row < sortNum_rpc_start) return (rr_pid);
  if (row < sortNum_res_start) return (rr_rpc);
  return (rr_res);
}


// Debug: show row from rowToTrack[row] - (rowToHeight[row] / 4) to rowToTrack[row] + (rowToHeight[row] * 3/4)
function DebugMarkClickRow(mx, row) {
  var temp_height_px = rowToHeight[row] * ypixpertrack;
  var temp_y0 = y_zoomed(rowToTrack[row]) + (temp_height_px * -0.25);
  var temp_y1 = y_zoomed(rowToTrack[row]);
  var temp_y2 = y_zoomed(rowToTrack[row]) + (temp_height_px * 0.75);
  var path = "M" + Rnd1(mx-3) + "," + Rnd1(temp_y0) + " V" + Rnd1(temp_y1);
    innersvgdotg.append("path")
      .attr("class", "labeltick")
      .attr("d",  path)
      .attr("stroke-width", "3")	
      .attr("stroke", "#C0C0C0");		// light gray
  var path = "M" + Rnd1(mx-3) + "," + Rnd1(temp_y1) + " V" + Rnd1(temp_y2);
    innersvgdotg.append("path")
      .attr("class", "labeltick")
      .attr("d",  path)
      .attr("stroke-width", "3")	
      .attr("stroke", "#000000");		// black
}

// Returns track number (not pixels) of top of row spans
function RowTop(row) {
  return rowToTrack[row];
}

// Returns track number (not pixels) of bottom of row spans
function RowBot(row) {
  var toptrack = rowToTrack[row];	// In tracks
  var rowheight = rowToHeight[row];	// In tracks
  var eventheight_px = perTracksize[rowheight].eventheight;	// In pixels
  var eventheight_trk = Rnd0(eventheight_px / ypixpertrack);
  return toptrack + eventheight_trk;	// Back to tracks
}



// Snap raw input [mx,my] pair to nearest in data, returning event subscript
function FindNearestEvent(mx, my, use_hilo) {
  var row = doSnap_y(my);
  if (row < 0) {return -1;}
//DebugMarkClickRow(mx, row);

  var click_track = y_zoomed.invert(my);
  var snap_track = rowToTrack[row];
  var hilo_click = 0;
  if (use_hilo) {
    if (click_track <= RowTop(row)) {hilo_click = 1;}	// Just above top of row spans
    if (click_track >= RowBot(row)) {hilo_click = -1;} 	// Just below bottom of row spans
//console.log("FindNearestEvent", row, click_track, RowTop(row), RowBot(row), hilo_click);
  }

  var ff = function(d) {return ts(d);};
  var rr = getAccessorRR(row);
  var nearest = doSnap_x(mx, row, rr, hilo_click);

//console.log("   nearest =", nearest, "of", data.events.length); 
  return nearest;
}

const soft_irq_name = [
  "hi", "timer", "net_tx", "net_rx",   "bloc", "irq_poll", "tasklet", "sched", 
  "hrtime", "rcu", "", "",    "", "", "", ""
];

const errno_name = [
  "eperm", "enoent", "esrch", "eintr",   "eio", "enxio", "e2big", "enoexec", 
  "ebadf", "echild", "eagain", "enomem",   "eacces", "efault", "enotblk", "ebusy",
  "eexist", "exdev", "enodev", "enotdir",   "eisdir", "einval", "enfile", "emfile",
  "enotty", "etxtbsy", "efbig", "enospc",   "espipe", "erofs", "emlink", "epipe",
  "edom", "erange", "edeadlk", "enametoolong",   "enolck", "enosys", "enotempty", "eloop",
  "", "", "", "",   "", "", "", "", 
  "", "", "", "",   "", "", "", "", 
  "", "", "", "",   "", "", "", "", 
];

// Create full event name
// short: name(arg)
// long, simple annot: name
// long, normal annot: name(arg)=ret
function fullname(d, short) {
  var fulltext;
  var arg = arg0(d);
  var add_arg = !(is_rpc(d) | is_lock(d) | is_pkt(d) | is_userrxtx(d) | is_enq_deq(d) | is_mark(d));
  if (is_pstate(d) && (name(d).indexOf('_') >= 0)) {add_arg = false;}
  if (short) {
    // Short form is name truncated to 9 chars, plus any (non-zero) arg
    fulltext = name(d);
    if (name(d).length > 9) {
      fulltext = name(d).substr(0, 7) + "~" + name(d).substr(name(d).length - 1, 1);
    }
    if (is_special(d) || is_irq(d)) {
      if (add_arg && (arg != 0)) {
        // Include numeric argument
        fulltext += "(" + arg + ")";
      }
    } else if (is_syscall(d) && !is_sched_syscall(d)) {
      fulltext += "(" + arg + ")";
    }
  } else {
    // Long form is full name, plus any (non-zero) arg and retval
    // Except if simple annot then just name 
    fulltext = name(d);
    if (state2.annottype_value) {return fulltext;}

    if (is_special(d) || is_irq(d)) {
      if (add_arg && (arg != 0)) {
        fulltext += "(" + arg + ")";
      }
    } else if (is_syscall(d) && !is_sched_syscall(d)) {
      var rt = ret(d);
      // Map 16-bit truncated return value into -128..65408 so we can 
      //   see likely error codes -1..-128
      if (rt > (65536 - 128)) {
        // For "negative", map return value to an errno name
        fulltext += "(" + arg + ")=" + errno_name[(65535 - rt) & 0x3F];
      } else {
        fulltext += "(" + arg + ")=" + rt;
      }
    }
  }
  return fulltext;
}
 
// Rotate the annots within a row
function rotateRow(row, maxannot) {
    ++rowToLabelk[row];
    if (rowToLabelk[row] >= maxannot) {rowToLabelk[row] = 0;}
}

function draw_one_annot(d, vx, vy,  liney, linedy,  dotsy, dotsdy,  fulltext, annotheight, clss) {
  // Put something behind the string
  var xmid = Rnd1(vx + 4);
  var ymid = Rnd1(vy - 3);
  var textwidth = Rnd1((fulltext.length + 1) * annotheight * 0.5);

  innersvgdotg.append("rect")
      .attr("class", "markertext " + clss)    
      .attr("x", (xmid - 2) + "px")       
      .attr("y", (ymid - (annotheight - 2)) + "px")       
      .attr("width", textwidth + "px")
      .attr("height", (annotheight - 2) + "px")
      .attr("style", "fill:rgb(255,255,255); opacity:0.875"); 

  // Show the string
  innersvgdotg.append("text")	// Placeholder objects
      .attr("class", "markertext noselect " + clss)
      .attr("transform", "translate(" + (xmid) + "," + (ymid) + ")scale(1,1)")
      .attr("font-size", annotheight + "px")
      .text(fulltext);

  // Solid line below
  var path1 = "M" + Rnd1(vx) + "," + Rnd1(liney) + " v" + (linedy);
  // Possible dots above
  var path2 = "M" + Rnd1(vx) + "," + Rnd1(dotsy) + " v" + (dotsdy);

  if (state2.annottype_value) {
    // Drop a vertical line down one row at item start
    innersvgdotg.append("path")
        .attr("class", "verticalshortline " + clss)
        .attr("d",  path1);
  } else {
    // Drop a vertical line down one row at item start
    innersvgdotg.append("path")
        .attr("class", "verticalline " + clss)
        .attr("d",  path1);
    // Pop a vertical dashed line up 
    innersvgdotg.append("path")
        .attr("class", "verticaldashline " + clss)
        .attr("d",  path2);
  }
}

// xx.xxus fullname; dur ipc= llc=
// Create complete text for d. Includes ipc/llc value.
function complete_text(d) {
    // Diddle state2.annottype_value so e always see fsyscall arg/ret in fullname
    // TODO: rationalize all the display choices
    var temp = state2.annottype_value;
    state2.annottype_value = 0;
    var fulltext =  shortnum5(tsrel(d)) + " " + fullname(d, false) + "; " + 
      shortnum(dur(d), data.thousandsX) + data.shortUnitsX;
    state2.annottype_value = temp;
    if ((0 <= state2.ipc_value) && (is_executable(d))) {	// available and real execution span
      if (has_ipc) {
        fulltext += " ipc=" + ipcvalues[realipc(d)];
      }
      if (has_llc) {
        fulltext += " llc=" + llcvalues[realllc(d)];
      }
    }
    return fulltext;
}

// Inputs per rowheight:
//  perTracksize[i].annotsize 	annotation font size
//  perTracksize[i].annotcount 	number of annotation lines
//  perTracksize[i].eventheight pix height for drawing events
//  perTracksize[i].annotheight pix height for drawing annotation text
//  perTracksize[i].eventoffset pix offset for event centerline
//  perTracksize[i].annotoffset pix offset for annot[0] text baseline

// Mark a time span with vertical line and text time/name/duration
// my is the mouse-click y-pixel or -1 if no click. If -1, draw at cpu/pid/rpc row			
// Return the x-coordinate in pixels 
function annotatespan(d, clss, short, my, rotate) {
//console.log("annotatespan", d, clss, short, rotate);
  if (state2.annottype_value) {
    // Short one-line annot
    rubberband = innersvgdotg.append("path")	// Placeholder objects
        .attr("class", "verticalshortline " + clss);
  } else {
    // Tall two-line annot
    rubberband = innersvgdotg.append("path")	// Placeholder objects
        .attr("class", "verticalline " + clss);
    rubberband2 = innersvgdotg.append("path")	// Placeholder objects
        .attr("class", "verticaldashline " + clss);
  }

    // Pixel x-position for start of event
    var vx = x_zoomed(ts(d));	// time to pixel
    var vx_ret = vx;
    if (is_pc_samp(d)) {vx = x_zoomed(tsend(d)) - 3;} 	// Near the sample, right end
    if (vx < 3) {vx = 3;}				// If off to the left, put onscreen
    if (region3width < vx) {return vx_ret;}		// If off to the right,skip
    
    // Working toward pixel y-position via row
    var row;
    if (my < 0) {
      // No incoming click; find cpu/pid/rpc row that is onscreen and non-zero height
      row = getfirstrow(d);
      if (row < 0) {return vx_ret;}	// no such row
    } else {
      // Incoming click; find nearest row
      row = doSnap_y(my);
    }
    // Allow wait_xxx etc. to be marked
    if (row < 0) {row = 0;}

    var rowheight = rowToHeight[row];
    var rowtrack = rowToTrack[row];
    if (rowheight == 0) {return vx_ret;}

    var maxannot = perTracksize[rowheight].annotcount;
    if (maxannot <= 0) {return vx_ret;}
 
    var annotheight = perTracksize[rowheight].annotsize;
    if (annotheight <= 0) {return vx_ret;}

    var y0 = y_zoomed(rowToTrack[row]);
    var vy = y0 + perTracksize[rowheight].annotoffset + 
             (maxannot - 1 - rowToLabelk[row]) * annotheight;
    
    // Rotate the annots within a row
    if (rotate) {rotateRow(row, maxannot);}

    // Create text string
    // short:  fullname
    // simple: fullname
    // long:   xx.xxus fullname ipc= llc=
    var timetxt = shortnum5(tsrel(d));
    var fulltext = fullname(d, short);
    if (!short) {
      // Long form includes start time, duration, and IPC/LLC
      // Except if simple annot, just full name
      if (!state2.annottype_value) {
        fulltext = timetxt + " " + fulltext + "; " + 
          shortnum(dur(d), data.thousandsX) + data.shortUnitsX;
        if ((0 <= state2.ipc_value) && (is_executable(d))) {	// available and real execution span
          if (has_ipc) {
            fulltext += " ipc=" + ipcvalues[realipc(d)];
          }
          if (has_llc) {
            fulltext += " llc=" + llcvalues[realllc(d)];
          }
        }
      }
    } 
//console.log("text vx,y '", fulltext, "'", vx, vy);
    ////TODO: draw_one_annot(d, vx, vy, linedy, dotsdy, fulltext, annotheight, clss)

    var xmid = Rnd1(vx + 4);
    var ymid = Rnd1(vy - 3);
    var textwidth = Rnd1((fulltext.length + 1) * annotheight * 0.5);

    // Put something behind the string
    innersvgdotg.append("rect")
      .attr("class", "markertext " + clss)    
      .attr("x", (xmid - 2) + "px")       
      .attr("y", (ymid - (annotheight - 2)) + "px")       
      .attr("width", textwidth + "px")
      .attr("height", (annotheight - 2) + "px")
      .attr("style", "fill:rgb(255,255,255); opacity:0.875"); 

    // Show the string
    innersvgdotg.append("text")	// Placeholder objects
      .attr("class", "markertext noselect " + clss)
      .attr("transform", "translate(" + (xmid) + "," + (ymid) + ")scale(1,1)")
      .attr("font-size", annotheight + "px")
      .text(fulltext);

  if (state2.annottype_value) {
    // Drop a tailed vertical line down one row at item start
    var path = d3.path();
    path.moveTo(vx, y0 + perTracksize[rowheight].eventoffset + 
      0.50 * perTracksize[rowheight].eventheight);
    path.lineTo(vx, ymid - 2);
    path.lineTo(vx + 4, ymid + 3);	// Tail to right
    rubberband.attr("d", path);
  } else {
    // Drop a vertical line down to region234height at item start
    var path = d3.path();
    path.moveTo(vx, y0 + perTracksize[rowheight].eventoffset + 
      0.55 * perTracksize[rowheight].eventheight);
    path.lineTo(vx, region234height);
    rubberband.attr("d", path);

    // Pop a vertical dashed line up to 4 (a little space under title) at item start
    var path2 = d3.path();
    path2.moveTo(vx, 4);
    path2.lineTo(vx, y0 + perTracksize[rowheight].eventoffset - 
      0.55 * perTracksize[rowheight].eventheight);
    rubberband2.attr("d", path2);
  }

    return vx_ret;
}
 
// If mouse is in Region2, go affect Y, else we are in Region3 and affecting X
// But if at far left 1/4 of region2, ignore here (click label rotates compress) 
function inHilitePartOfRegion2(x) {
  if (0 <= x) {return false;}
  if (x < (-0.75 * region2width)) {return false;}
  return true;
}

function mouse_dblclick(e) {
console.log("mouse_dblclick at",e);
  d3.event.stopPropagation();
}

// Mouse clicks on main SVG area come here. Those with 0 <= x affect Region3 and
// those with x < 0 affect Region2
function mousedown_x() {
//console.log("mousedown_x", d3.event.button, d3.event.target);
//console.log("mousedown_x", d3.event.button, d3.event);
    // If not shiftKey, pass it on
    if (!d3.event.shiftKey) {
      return;
    }
    d3.event.stopPropagation();
    var m = d3.mouse(this);
    mx1 = m[0];		// Remember so we can check for tiny movement in mousemove
    my1 = m[1];		// Remember so we can check for tiny movement in mousemove
//console.log("mousedown at", mx1, my1, d3.event);

    // If over the reddot, pass on to that
    if ((-7 <= mx1) && (mx1 <= 7) && 
        (region234height-7 <= my1) && (my1 <= region234height+7)) {
      resetzoom();
      return;
    }

    if (inHilitePartOfRegion2(mx1)) {mousedown_y(m); return;}

    var dx1 = x_zoomed.invert(mx1);	// time
    var dy1 = y_zoomed.invert(my1);	// track
//console.log("mousedown_x at", dx1, dy1, "realxleft", realxleft);

    // Find nearest data object, use hilo
    var i = FindNearestEvent(mx1, my1, true); 
    if (i < 0) {return;}

    d1 = data.events[i];
    // Draw and label vertical line at start of object
    var short = false;
    var rotate = true;
    vx1 = annotatespan(d1, "first", short, my1, rotate);

    // Shift-RIGHT-click highights all same-event-number items, fades others
    if (0 < d3.event.button) {
      state.hilite_evnum2 = state.hilite_evnum;
      state.hilite_evnum = ev(d1);
      resetText(document.getElementById("SearchText"), name(d1));
      // Draw all events onscreen, plus marks, plus IPC
      redrawEvents();
    }

    state.annotated_d.push(i);
    // Always keep around the most recent explicit click
    state2.annotated_one_d = i;
//console.log("mousedown_x annotated_one_d=", state2.annotated_one_d); 
   
    // Set up for possible drag
    dragging_bubble = false;
    dragging_spike = false;
    svgdotg.on("mousemove", mousemove_x);
}
 
// Rubber band shift-click-drag to see timespan Delta
function mousemove_x() {
//console.log("mousemove_x");
  if (dragging_bubble) {bubblemove();}
  if (dragging_spike) {spikemove();}
    d3.event.stopPropagation();
    d3.event.preventDefault();

    var m = d3.mouse(this);
    var mx2 = m[0];
    var my2 = m[1];

    var deltamx = Math.abs(mx1 - mx2);
    var deltamy = Math.abs(my1 - my2);
    if ((deltamx < 5) && (deltamy < 5)) {return;}	// Move at least 5 px
  
    // Remove any previous second line
    d3.selectAll(".second").remove();

    rubberband3 = innersvgdotg.append("path")	// Placeholder objects
        .attr("class", "rubberline second");
//    // backmap mouse client pixels to pre-zoomed pixels
//    var dx2 = x_zoomed.invert(mx2);
//    var dy2 = y_zoomed.invert(my2);
//console.log("mousemove at", dx2, dy2);

    // Find nearest data object, use hilo
    var i = FindNearestEvent(mx2, my2, true); 
    if (i < 0) {return;}

    d2 = data.events[i];
    // Draw and label vertical line at start of object
    var short = false;
    var rotate = false;
    vx2 = annotatespan(d2, "second", short, my2, rotate);

    // Make a path at the bottom and label it
    var path = d3.path();
    path.moveTo(vx1, region234height - 4);
    path.lineTo(vx2, region234height - 4);
    // Update the path placeholder
    rubberband3.attr("d", path);

    var deltax = Math.abs(ts(d1) - ts(d2)) * data.shortMulX;
    var deltatext = shortnum5(deltax);

    // Put something behind the delta so it is visible even if on top of timelines
    // Assume that the average chafracter is 0.5em and add 2 chars for slop
    var xmid = Rnd1((vx1 + vx2) / 2);
    var ymid = Rnd1(region234height - 8);
    var textwidth = Rnd1((deltatext.length + 2) * textsize.smalltick * 0.5);

    innersvgdotg.append("rect")
      .attr("class", "rubberbackground second")    
      .attr("x", (xmid - textwidth / 2) + "px")       
      .attr("y", (ymid - textsize.smalltick * 0.9) + "px")       
      .attr("width", textwidth + "px")
      .attr("height", textsize.smalltick + "px")
      .attr("style", "fill:rgb(255,255,255); opacity:0.875"); 

    // Label the line
    rubbertextx = innersvgdotg.append("text")
        .attr("class", "markertext noselect second");
    rubbertextx
      .attr("transform", 
            "translate(" + (xmid) + "," + 
             (ymid) + ")scale(1,1)")
      .attr("font-size", textsize.smalltick + "px")
      .style("text-anchor", "middle")
      .text(deltatext);   
}
 
// Mouse clicks on main SVG area come here. Those with 0 <= x affect Region3 and
// those with x < 0 affect Region2
function mouseup_x() {
//console.log("mouseup_x");
  // We can get callout dragging mouseup events here. Stop that dragging also.
  mouseup_callout();

    d3.event.stopPropagation();
    var m = d3.mouse(this);
    var mx = m[0];
    var my = m[1];
//console.log("mouseup_x at", mx, my);
    // If mouse is in Region2, go affect Y, else we are in Region3 and affecting X
    if (inHilitePartOfRegion2(mx)) {
      mouseup_y(m); 
      return;
    }

    dragging_bubble = false;
    dragging_spike = false;
    svgdotg.on("mousemove", mousemove_region3);

    // If shift key is still down, remove all rubberlines and clear annots
    if (d3.event.shiftKey) {
      resetlines();
      state.annotated_d = [];
      state2.annotated_one_d = -1;
      if (state.hilite_evnum != 0) {
        state.hilite_evnum = 0;
        state.hilite_evnum2 = 0;
        // Draw all events onscreen, plus marks, plus IPC
        redrawEvents();
      }
    }
}

// Display live name of current mouseover item in lower left corner of region 3
function delayed_mousemove_event(xpx, ypx) {
  // Find nearest data object, use hilo
  var i = FindNearestEvent(xpx, ypx, true); 
//console.log("mousemove", xpx, ypx, i);
  if (i < 0) {return;}
  var d = data.events[i];
  var dtext = complete_text(d);
//console.log(dtext);

  var xmid = 10;
  var ymid = region234height - 10;
  var aheight = 18;
  var textwidth = Rnd1((dtext.length + 1) * aheight * 0.5);
  d3.selectAll(".movetext").remove();

  // Put something behind the string
  innersvgdotg.append("rect")
    .attr("class", "movetext noselect ")    
    .attr("x", (xmid - 2) + "px")       
    .attr("y", (ymid - (aheight - 2)) + "px")       
    .attr("width", textwidth + "px")
    .attr("height", (aheight - 2) + "px")
    .attr("style", "fill:rgb(255,255,255); opacity:0.875"); 

  // Show the string
  innersvgdotg.append("text")	// Placeholder objects
    .attr("class", "movetext noselect ")
    .attr("transform", "translate(" + (xmid) + "," + (ymid) + ")scale(1,1)")
    .attr("font-size", aheight + "px")
    .attr("font-family", "sans-serif")
    .text(dtext);
}

// Fires continuously with mouse movement in region 3
// If mouseover a timespan, display it
function mousemove_region3() {
  d3.event.stopPropagation();
  var m = d3.mouse(this);
  var xpx = m[0];		// Remember so we can check for tiny movement in mousemove
  var ypx = m[1];		// Remember so we can check for tiny movement in mousemove
  if (xpx < 0) return;
  if (region3width < xpx) return;
  if (ypx < 0) return;
  if (region234height < ypx) return;

  var deltamx = Math.abs(mx1 - xpx);
  var deltamy = Math.abs(my1 - ypx);
  if ((deltamx < 5) && (deltamy < 5)) {return;}	// Move at least 5 px

  mx1 = xpx;
  my1 = ypx;
  clearTimeout(timerHandle);
  timerHandle = setTimeout(delayed_mousemove_event, 50, xpx, ypx);  // msec
}

// Set event dst_d to be at the time and cpu/pid/rpc of src_d.
// Choose pid/rpc based on matching row, else choose cpu 
function setPosition(row, src_d, dst_d) {
//console.log("setPosition", src_d, dst_d);
  if ((row == -1) && (0 <= cpu(src_d))) {		// Default
    set_ts(dst_d, ts(src_d));
    set_cpu(dst_d, cpu(src_d));
    set_pid(dst_d, -1);
    set_rpc(dst_d, -1);
  } else if ((getrow(src_d, prefixCpu) == row) && (0 <= cpu(src_d))) {
    set_ts(dst_d, ts(src_d));
    set_cpu(dst_d, cpu(src_d));
    set_pid(dst_d, -1);
    set_rpc(dst_d, -1);
  } else if ((getrow(src_d, prefixPid) == row) && (0 < pid(src_d))) {
    set_ts(dst_d, ts(src_d));
    set_pid(dst_d, pid(src_d));
    set_cpu(dst_d, -1);
    set_rpc(dst_d, -1);

  } else if ((getrow(src_d, prefixRpc) == row) && (0 < rpc(src_d))) {
    set_ts(dst_d, ts(src_d));
    set_rpc(dst_d, rpc(src_d));
    set_cpu(dst_d, -1);
    set_pid(dst_d, -1);
  }
  // Else nothing matched; do not change position
//console.log("    ", dst_d);
}

// The global variable "event" is magically defined when we reach an on(mouse...) function

// Drag callout bubble to new position
function doCalloutBubble(d) {
//console.log("doCalloutBubble", d, event);
  event.stopPropagation();
    
  // Set globals for drag
  mx1 = event.clientX;		// Remember so we can calc delta-movement
  my1 = event.clientY;		// Remember so we can calc delta-movement
  originalx = arg0(d);
  originaly = ret(d);

  dragging_bubble = true;
  dragging_spike = false;
  svgdotg.on("mousemove", function () {bubblemove(d)});
  //svgdotg.on("mouseup", mouseup_callout);
}

function mouseup_callout() {
//console.log("mouseup_callout");
  dragging_bubble = false;
  dragging_spike = false;
  svgdotg.on("mousemove", mousemove_region3);
}


// Drag just bubble part of a callout
function bubblemove(d) {
//console.log("bubblemove", d, event);
  event.stopPropagation();
  event.preventDefault();

  var mx2 = event.clientX;
  var my2 = event.clientY;
  var deltaX = mx2 - mx1;		// in px
  var deltaY = my2 - my1;		// in px
  // One row is  ypixpertrack * maxTrackHeight pixels 
  deltaY /= (ypixpertrack * maxTrackHeight);

  set_delta_x(d, Rnd0(originalx + deltaX));	// delta-x in px
  set_delta_row(d, Rnd1(originaly + deltaY));	// delta-y in rows

  // redisplay -- this will reenable mousedown and mousemove
  d3.selectAll(".extra").remove();
  drawExtras();
}

function doCalloutSpike(d, e) {
//console.log("doCalloutSpike", d, e);
  event.stopPropagation();
    
  // Set globals for drag
  mx1 = event.clientX;		// Remember so we can calc delta-movement
  my1 = event.clientY;		// Remember so we can calc delta-movement

  dragging_bubble = false;
  dragging_spike = true;
  svgdotg.on("mousemove", function () {spikemove(d)});
}

// Drag entire callout
function spikemove(d) {
//console.log("spikemove");
//console.log("spikemove", d, event);
  event.stopPropagation();
  event.preventDefault();

  var mx2 = event.clientX;
  var my2 = event.clientY;
  var row = doSnap_y(my2);	// We want this to be an onscreen row
//console.log("spikemove", row, ytop_row, ybot_row);

  // ClientX and Y pixels are wrt the outer SVG. Map to Region3 pixels
  var mx3 = mx2 - region2width;
  var my3 = my2 - region1height;

  var deltaX = mx3 - mx1;		// in px
  var deltaY = my3 - my1;		// in px
//console.log("  ", mx1, my1, mx2, my2, mx3, my3, deltaX, deltaY);

  // Find nearest data object to our drag point, no use of hilo
  var i = FindNearestEvent(mx3, my3, false); 
  if (i < 0) {return;}

  // Events can be displayed on multiple rows. 
  // We want the one nearest the mouse
  d2 = data.events[i];
  if (startsonscreen_x(d2)) {
    // Move d to start at time of d2, same onscreen row
//console.log("  row", row, "near", i, d2);
    setPosition(row, d2, d);
  }

  // redisplay -- this will reenable mousedown and mousemove
  d3.selectAll(".extra").remove();
  drawExtras();
}

// For input event d, apply ff to each cpu, pid, rpc, res field
// Do nothing for pid=0 and for rpc=0
function applyevent(d, i, ff, arg) {
  if (0 <= cpu(d)) {ff(arg, d, i, prefixCpu, low16of(cpu(d)));}
  if (0 < pid(d))  {ff(arg, d, i, prefixPid, low16of(cpu(d)));}
  if (0 < rpc(d))  {ff(arg, d, i, prefixRpc, low16of(cpu(d)));}
  if (0 <= res(d)) {ff(arg, d, i, prefixRes, low16of(cpu(d)));}
}

function getrow_cpu(d, prefix) {
  if ((prefix == prefixCpu) && (0 <= cpu(d))) {
    return eventToRow[prefix * 65536 + low16of(cpu(d))];
  }
  return -1;
}

function getrow_cpu2(d, prefix) {
  if ((prefix == prefixCpu) && (0 <= cpu2(d))) {
    return eventToRow[prefix * 65536 + low16of(cpu2(d))];
  }
  return -1;
}

function getrow_pid(d, prefix) {
  if ((prefix == prefixPid) && (0 < pid(d))) {
    return eventToRow[prefix * 65536 + low16of(pid(d))];
  }
  return -1;
}

function getrow_pid2(d, prefix) {
  if ((prefix == prefixPid) && (0 < pid2(d))) {
    return eventToRow[prefix * 65536 + low16of(pid2(d))];
  }
  return -1;
}

function getrow(d, prefix) {
  if ((prefix == prefixCpu) && (0 <= cpu(d))) {
    return eventToRow[prefix * 65536 + low16of(cpu(d))];
  }
  if ((prefix == prefixPid) && (0 < pid(d))) {
    return eventToRow[prefix * 65536 + low16of(pid(d))];
  }
  if ((prefix == prefixRpc) && (0 < rpc(d))) {
    return eventToRow[prefix * 65536 + low16of(rpc(d))];
  }
  if ((prefix == prefixRes) && (0 <= res(d))) {
    return eventToRow[prefix * 65536 + low16of(res(d))];
  }
  return -1;
}

function goodonscreenrow(row) {
  if (row < 0) {return false;}
  if (rowToHeight[row] == 0) {return false;}
  if (rowToTrack[row] < state2.ytop_track) {return false;}
  if (state2.ybot_track < rowToTrack[row]) {return false;}
  return true;
}

// Find a row for d that is visible onscreen and non-zero height
function getfirstrow(d) {
  var row;
  row = getrow(d, prefixCpu);
  if (goodonscreenrow(row)) {return row;}
  row = getrow(d, prefixPid);
  if (goodonscreenrow(row)) {return row;}
  row = getrow(d, prefixRpc);
  if (goodonscreenrow(row)) {return row;}
  return -1;
}
 
// Scan events array and in this row, setting hilite on/off
function setEventHilites(hilite_row, newhilite) {
  for (var i = 0; i < data.events.length; ++i) {
    var d = data.events[i];

    var cpurow = getrow(d, prefixCpu);
    var pidrow = getrow(d, prefixPid);
    var rpcrow = getrow(d, prefixRpc);
    var resrow = getrow(d, prefixRes);
    // Highlight arc if either end's CPU or PID is highlighted
    // We overload rpcrow and resrow
    if (is_arc(d)) {
      rpcrow = getrow_cpu2(d, prefixCpu);
      resrow = getrow_pid2(d, prefixPid);
    }

    if ((cpurow == hilite_row) || 
        (pidrow == hilite_row) || 
        (rpcrow == hilite_row) || 
        (resrow == hilite_row)) {
      if (newhilite) {hilite_event_set.add(i);}
      else {hilite_event_set.delete(i);}
    }
  }
//console.log("hilite_event_set", hilite_event_set);
}

// m has mouse pixel coordinates [x,y]
function mousedown_y(m) {
//console.log("mousedown_y", d3.event.button);
  // If not shiftKey, pass it on
  if (!d3.event.shiftKey) {
    return;
  }
  d3.event.stopPropagation();


  // Backmap and turn on highlights here
  var hilite_row = doSnap_y(m[1]);
//console.log("  hilite_row", hilite_row, sortName[hilite_row], state2.rowToHilite[hilite_row]);
  var newhilite = !state2.rowToHilite[hilite_row];
  //if ((newhilite < 0) || (1 < newhilite)) {newhilite = 0;}	// Make robust
  
  // Shift-click highlights one row
  // Shift-RIGHT-click highlights all rows with matching name prefix
  if (0 == d3.event.button) {
     state2.rowToHilite[hilite_row] = newhilite;
  } else {
    var matchlen = sortName[hilite_row].length;	// Exact match
    //var prefixlen = sortName[hilite_row].search("[^a-zA-Z0-9_-]");
    var prefixlen = sortName[hilite_row].search("[^a-zA-Z_-]");	// Stop at digits
    if (0 < prefixlen) {matchlen = prefixlen;}
    var prefix = sortName[hilite_row].substr(0, matchlen);
    // Update all matching rows
    for (var row = 0; row < sortNumSize; ++row) {
      if (sortName[row].startsWith(prefix)) {
        state2.rowToHilite[row] = newhilite;
      }
    }
  }
  CheckHilites("Click", state2.rowToHilite);

  
  // Now highlight all the events in currently-highlighted rows
  RecalcEventHilites();		// from rowToHilite
  RecalcGroupHilites();		// from rowToHilite
 
//console.log("hilite_event_set", hilite_event_set);
//console.log("grouphashilite", state2.grouphashilite);

  updateTracks();
  y.domain([0, maxTrack]);
  resetRangeDomain();
  calcPixHeightsPerTrack(ypixpertrack);

  redrawEventsEtc();
  redrawYlabels();
}

function mousemove_y() {
//console.log("mousemove_y");
    d3.event.stopPropagation();

    var m = d3.mouse(this);
    var mx2 = m[0];
    var my2 = m[1];

    var deltamx = Math.abs(mx1 - mx2);
    var deltamy = Math.abs(my1 - my2);
    if ((deltamx < 5) && (deltamy < 5)) {return;}	// Move at least 5 px
}

// m has mouse pixel coordinates;
function mouseup_y(m) {
console.log("mouseup_y");
  // We can get callout dragging mouseup events here. Stop that dragging.
  mouseup_callout();
 
  // If shift key is still down, remove all highlights. DEFUNCT
  if (d3.event.shiftKey) {
  }
  d3.event.stopPropagation();

  updateTracks();
  y.domain([0, maxTrack]);
  resetRangeDomain();
//console.log("resetRangeDomain in mouseup_y");
  calcPixHeightsPerTrack(ypixpertrack);

  redrawEventsEtc();
  redrawYlabels();
}

//BUG: after save, gray goes away
 
 
// Inputs: 
// winWidth and winHeight, which either come from window.innerWidth/Height
//   or from one of the reset buttons.
// sortNumSize from the number of rows to display
//
// For reasons that don't make much sense, I decided to tile this area in rectangles
// that are multiples of 120x160 pixels, with a minimum of 2x2 such rectangles. This 
// lends itself to onscreen pictures that have a little bit of reproducible sizes.
//
// Much of the spacing and text sizing is based on the number of such tiles, so they 
// adjust within limits as the browser window changes size.

// There are four text sizes, chosen emperically:
//  title, used for top title:           for tiles [2..12], range is [16..96] pixels
//  label, used for X and Y axis labels: for tiles [2..12], range is [10..60] pixels
//  tick, used for axis values:          for tiles [2..12], range is [9..42] pixels
//  smalltick, used for annotations:     for tiles [2..12], range is [9..15] pixels
//
// Within the window, these margins are first reserved for borders and the top buttons 
//  winMargin: width=10px height=40px
//
// Within the remaining window, these drawing margins are reserved
//  top:    title + 3
//  left:   label + 3 + 5_chars * tick + 6 (assumes char width is 0.6 tick)
//  right:  5_chars * tick
//  bottom: 2 * tick + label * 1.25 + 6
//
// The active area for the diagram, sans title, axes, labels is
//  width: (winWidth/160) * 160 - margin.left - margin.right - winMargin.width 
//  height: (winHeight/120) * 120 - margin.top - margin.bottom - winMargin.height;
//
// The main SVG area is allocated around the active area with [0,0] at [left, top] and  
//  SVG width:  (winWidth/160) * 160  - winMargin.width
//  SVG height: (winHeight/120) * 120 - winMargin.height
// and within this SVG area the diagram height is SVG hight bounded by
//  60px * (sortNumSize + 1.5)
// so that the CPU rtracks are not excessively spaced out if drawn in a tall window.
//
// With the outer SVG area there is a group, svgdotg, with origin also at [left, top]
//
// Within this group is an inner SVG area 
//  inner width:  SVG width - margin.left, allowing drawing into margin.right
//  inner height: SVG height
//
// And finally...
// With the inner SVG area there is a group, innersvgdotg, and all the real drawing is
// put within this group.
// 
 
 
function myFilter() {
  return !d3.event.shiftKey;	// FIREFOX event is not defined
}


// Return outermost outline, grey for kernel
function l4color(d, gray) {
  if (is_fault(d)) {return "#FFFFFF";}
  if (is_irq(d)) {return gray ? fadecolor : "#000000";}
  if (is_syscall(d)) {return gray ? fadecolor :"#808080";}
  return"#55AA55";
}
function l3color(d, gray) {
  if (is_kernel(d)) {
    if (gray) {return fadecolor;}
    return kernelcolor[ev(d) >> 8];
  }
  return"#FFFFFF";
}
// For deferred events only
function l3darkcolor(d, gray) {
  if (is_kernel(d)) {
    if (gray) {return fadecolor;}
    return darkkernelcolor[ev(d) >> 8];
  }
  return"#FFFFFF";
}



// The multiplies keep the colors for n and n+1 somewhat uncorrelated
// The plus keeps the colors for 0 somewhat apart
// 17 colors (multiplier is relatively prime to 17)
function l2color(d, gray) {
  if (gray) {return fadecolor;}
  if (is_idle(d)) {return "#FFFFFF";}		// white
  if (is_sched_syscall(d)) {return "#E0E0E0";}	// light gray
  return currentcolor.color1[(ev(d) * 4) % currentcolor.mod1];
}

// 15 colors (multiplier should be relatively prime to 15)
function l1color(d, gray) {
  if (gray) {return fadecolor;}
  if (is_idle(d)) {return gray ? fadecolor : "#000000";}
  if (is_sched_syscall(d)) {return "#7070A0";}	// dark blue-gray
  if (is_bottom_half(d)) {return l1color_arg(arg0(d), gray);}
  return currentcolor.color2[(ev(d) * 7 + 6) % currentcolor.mod2];
}

// For coloring PC samples -- different multipliers, different color tables
// 17 colors
function l2color_arg(arg, gray) {
  if (gray) {return fadecolor;}
  return currentcolor.lightcolor1[(arg * 5) % currentcolor.mod1];
}
//15 colors
function l1color_arg(arg, gray) {
  if (gray) {return fadecolor;}
  return currentcolor.lightcolor2[(arg * 7 + 6) % currentcolor.mod2];
}

// For coloring locks -- different multipliers, different color tables
// 17 colors
function l2color_arg_dark(arg, gray) {
  if (gray) {return fadecolor;}
  return currentcolor.color1[(arg * 5) % currentcolor.mod1];
}
//15 colors
function l1color_arg_dark(arg, gray) {
  if (gray) {return fadecolor;}
  return currentcolor.color2[(arg * 7 + 6) % currentcolor.mod2];
}

// 64 colors DEFUNCT
function l0color(d, gray) {
  if (is_idle(d)) {return gray ? fadecolor : "#000000";}
  return color64_0[(ev(d) * 9) % 64];
}



// This is where the ratings below came from historically
// Proportions   k4 : 30, k3 : 28, k2 : 16, k1 : 6,  
//                   1.00     0.93     0.53     0.20
//                       idle : 2, u2 : 12, u1 : 8
//                            0.07     0.40     0.27
// Newer:
//  linewidth.k3 = Math.round(linewidth.k4 - 2);	0.93
//  linewidth.k2 = Math.round(linewidth.k3 * 4 / 7);	0.53
//  linewidth.k1 = Math.round(linewidth.k2 * 3 / 8);	0.20
//  linewidth.u2 = Math.round(linewidth.k4 * 1 / 4);	0.23
//  linewidth.u1 = Math.round(linewidth.u2 * 2 / 3);	0.15
//  linewidth.idle = Math.round(linewidth.k4 / 15);	0.07


function l4width(d, eventheight) {
  if (is_kernel(d)) {return Rnd1(1.00 * eventheight);}
  return 0;
}
function l3width(d, eventheight) {
  if (is_kernel(d)) {return Rnd1(0.93 * eventheight);}
  return 0;
}
function l2width(d, eventheight) {
  if (is_idle(d)) {return 0;}
  if (is_kernel(d)) {return Rnd1(0.53 * eventheight);}
  return Rnd1(0.23 * eventheight);
}
function l1width(d, eventheight) {
  if (is_idle(d)) {return Rnd1(0.07 * eventheight);}
  if (is_kernel(d)) {return Rnd1(0.20 * eventheight);}
  return Rnd1(0.15 * eventheight);
}


// Set up the initial scaling for X-axis and Y-axis
function setInitialRangeDomain() {
//console.log("setInitialRangeDomain", dataTsLo, dataTsHi, 0, maxTrack); 
  // Set the scales and ranges
  // Initialize the left and right values
  state2.savedtransformX = d3.zoomIdentity;
  state2.savedtransformY = d3.zoomIdentity;

//console.log("***** initial state2.savedtransformY", state2.savedtransformY);


  // To keep circles all visible within the svg clipping rectangle, 
  // we need ~5px of padding around the range
  x = d3.scaleLinear()
      .range([axesmargin, region3width - axesmargin])
      .nice(10);
  x.domain([dataTsLo, dataTsHi]);
  state2.rangeX = (region3width - 2 * axesmargin) * 1.0;
//console.log("x.range", axesmargin, region3width - axesmargin, "x.domain", dataTsLo, dataTsHi); 

  // Zoomed version of x scale above
  x_zoomed = state2.savedtransformX.rescaleX(x);

  // Initial X-axis bounds just after data load, before any pan/zoom
  reset_globals_x(dataTsLo, dataTsHi, dataTsHi);
//console.log("reset_globals_x in setInitialRangeDomain");

  y = d3.scaleLinear()
      .range([axesmargin, region234height - axesmargin])
      .nice(sortNumSize);
  y.domain([0, maxTrack]);	// N tracks
  state2.rangeY = region234height - 2 * axesmargin;


  // Zoomed version of x scale above
  y_zoomed = state2.savedtransformY.rescaleY(y);

  // Initial Y-axis bounds just after data load, before any pan/zoom
  reset_globals_y(0, maxTrack);
}


// Set up the scaling for X-axis and Y-axis after zoom/pan of either
function resetRangeDomain() {
//console.log("resetRangeDomain");

  // Zoomed version of x scale above
  x_zoomed = state2.savedtransformX.rescaleX(x);

//console.log("resetRangeDomain state2.savedtransformX", state2.savedtransformX);

  // New X-axis bounds
  resetonscreen_x();

  // Zoomed version of x scale above
  y_zoomed = state2.savedtransformY.rescaleY(y);

//console.log("  y_zoomed.domain", y_zoomed.domain());
//console.log("  y_zoomed.range", y_zoomed.range());

  // New Y-axis bounds 
  resetonscreen_y();
}


function calculateRowTracks() {
}


// To keep the display digits manageable for the X-axis, we want to remove 
// all the common leading time and display it once as basetime.
// To do this, we first converted the incoming data.tracebase string,
//  2016-11-19_10:04:00
// to numeric seconds 0..86400
//
// Set up x zoom and call resetonscreen_x before calling this
function calculateBasetime() {
  // Current X-axis ranges from realxleft to realxright seconds from
  // dataBaseSecond
  var xaxisspan = realxright - realxleft;	// seconds
//console.log("calculateBasetime", dataBaseSecond , realxleft, realxright, xaxisspan);
 
  // We want offsets in seconds from the base seconds, no hh:mm
  var baseleft = realxleft;
  var fullbaseleft = dataBaseSecond + baseleft;
  var secfmt = "02d";

  if (60 <= xaxisspan) {
    xaxisfmt.basetime = (Math.floor(fullbaseleft / 3600) * 3600) - dataBaseSecond;	
    // Hour hh:00:00 with multiple of 10, 100, 1000 seconds shown as mm:ss
    xaxisfmt.deltamul = 0;
    xaxisfmt.deltamax = 60;
    xaxisfmt.ovflnext = Math.floor(fullbaseleft / 3600 + 1) % 60;
    xaxisfmt.ovflformat = "02d"; 
    xaxisfmt.deltaformat = "05.2f";
    document.getElementById("xaxislabel").innerHTML = "Time (mm:ss)";
  } else if (1.0 <= xaxisspan) {
    // Minute hh:mm:00
    xaxisfmt.basetime = Math.floor(fullbaseleft / 60) * 60 - dataBaseSecond;		
    xaxisfmt.deltamul = 1;
    xaxisfmt.deltamax = 60;
    xaxisfmt.ovflnext = Math.floor(fullbaseleft / 60 + 1) % 60;
    xaxisfmt.ovflformat = "02d"; 
    xaxisfmt.deltaformat = "04.1f";
    document.getElementById("xaxislabel").innerHTML = "Time (sec)";
  } else if (0.001 <= xaxisspan) {
    xaxisfmt.basetime = Math.floor(baseleft);			// Second hh:mm:ss	
    xaxisfmt.deltamul = 1000; 	
    xaxisfmt.deltamax = 1000;
    xaxisfmt.ovflnext = Math.floor(fullbaseleft + 1) % 60;
    xaxisfmt.ovflformat = "02d"; 
    xaxisfmt.deltaformat = "05.1f";
    if (0.010 <= xaxisspan) {xaxisfmt.deltaformat = "03d";}
    document.getElementById("xaxislabel").innerHTML = "Time (msec)";
  } else if (0.000001 <= xaxisspan) {
    xaxisfmt.basetime = Math.floor(baseleft * 1000) / 1000;	// Msec hh:mm:ss.mmm
    secfmt = "06.3f";
    xaxisfmt.deltamul = 1000000;
    xaxisfmt.deltamax = 1000;
    xaxisfmt.ovflnext = Math.floor(baseleft * 1000 + 1) % 1000;
    xaxisfmt.ovflformat = "03d"; 
    xaxisfmt.deltaformat = "05.1f";
    if (0.000010 <= xaxisspan) {xaxisfmt.deltaformat = "03d";}
    document.getElementById("xaxislabel").innerHTML = "Time (&#x00b5;sec)";	// micro
  } else {
    xaxisfmt.basetime = Math.floor(baseleft * 1000000) / 1000000; // Usec hh:mm:ss.uuuuuu
    secfmt = "09.6f";
    xaxisfmt.deltamul = 1000000000;
    xaxisfmt.deltamax = 1000;
    xaxisfmt.ovflnext = Math.floor(baseleft * 1000000 + 1) % 1000;
    xaxisfmt.ovflformat = "03d"; 
    xaxisfmt.deltaformat = "05.1f"; 	
    if (NSEC10 <= xaxisspan) {xaxisfmt.deltaformat = "03d";}
    document.getElementById("xaxislabel").innerHTML = "Time (nsec)";
  }
  // If bare, suppress basetime text
  // If relative to row starts (rowToMinT), change hh:mm:ss to "relative"
  if (0 < state2.basetime0_value) {
    document.getElementById("basetimetext").innerHTML = 
      dataBaseDate + " relative";
  } else {
    document.getElementById("basetimetext").innerHTML = 
      dataBaseDate + " " + hhmmss(dataBaseSecond + xaxisfmt.basetime, secfmt);
  }

//console.log("basetime=", hhmmss(xaxisfmt.basetime, secfmt));
}

// Create and draw initial X-axis
function draw_initial_xaxis() {
//console.log("draw_initial_xaxis");

  // Define axes using scales x and y
  // This draws below Region3, labelling each X-axis tick with seconds/msec/etc.
  ticknumber = 1;
  xAxis = d3.axisBottom(x)
          .ticks(textsize.xnice)
          .tickSize(4)
          .tickPadding(2)
          .tickFormat(shortxtime);
  // This draws the grid lines themselves
  xGrid = d3.axisBottom(x)
          .ticks(textsize.xnice)
          .tickSize(-region234height)
          .tickFormat(function() { return null; });

  // Add the x Axis group and draw the X-axis
  gX = svgdotg.append("g")
      .attr("class", "axis axis--x")
      .attr("transform", "translate(0," + region234height + ")")
      .call(xAxis);

  // Set the font size on the X-axis tick labels
  d3.selectAll(".tick")
    .attr("class", "tick noselect")
    .attr("className", "tick noselect")
    .style("font-size", textsize.smalltick + "px");

  // Put the gridlines on top
  // This draws on top of Region3 (after drawing events/marks/ipc)
  gXgrid = svgdotg.append("g")
    .attr("class", "axis--grid")
    .attr("transform", "translate(0," + region234height + ")")
    .call(xGrid);
  gXgrid.selectAll(".axis--grid line")  // select all the line elements for the grid
          .attr("style", "stroke-width:1px; fill:none; stroke:#F0F0F0");  // Almost white
  gXgrid.select(".domain").remove();	// Avoid drawing endpoint lines

  // Text label for the entire X axis is updated in calculateBasetime
}

// Create and draw initial Y-axis
// Degenerate form -- just to get the domain line
function draw_initial_yaxis() {
//console.log("draw_initial_yaxis");
  // This draws to the left of Region3
  //yAxis = d3.axisLeft(y).ticks(sortNumSize + 1);
  yAxis = d3.axisLeft(y).ticks(0).tickSize(4);

  // Add the y Axis group
  gY = svgdotg.append("g")
      .attr("class", "axis axis--y")
      .call(yAxis);
  d3.selectAll(".tick")
    .attr("class", "tick noselect")
    .attr("className", "tick noselect")
    .style("font-size", textsize.smalltick + "px");
 }

// Redraw the zoomed X-axis and grid
function redrawXaxis() {
  ticknumber = 1;
  gX.call(xAxis.scale(x_zoomed));

  d3.selectAll(".tick")
    .attr("class", "tick noselect")
    .attr("className", "tick noselect")
    .style("font-size", textsize.smalltick + "px");

  gXgrid.call(xGrid.scale(x_zoomed));

  gXgrid.selectAll(".axis--grid line")  // select all the line elements for the grid
          .attr("style", "stroke-width:1px; fill:none; stroke:#F0F0F0");  // almost white
  gXgrid.select(".domain").remove();	// Avoid drawing endpoint lines
  // Text label for the entire X axis is updated in calculateBasetime
}


// Redraw the zoomed Y-axis 
function redrawYaxis() {
//console.log("redrawYaxis");
  gY.call(yAxis.scale(y_zoomed));
  d3.selectAll(".tick")
    .attr("class", "tick noselect")
    .attr("className", "tick noselect")
    .style("font-size", textsize.smalltick + "px");
  // Text label for the entire Y axis is unchanged
}

// BLACK RIGHT-POINTING TRIANGLE (U+25B6)
// BLACK DOWN-POINTING TRIANGLE (U+25BC)
// middle dot U+00B7
// Show arrow for Y-axis group name
function expandArrow(prefix) {
  return (state2.groupcompress[prefix] == 0) ? "\u25B6" : (book ? "\u00B7" : "\u25BC"); 
}


function draw_ylabel(row, fontsize) {
  if (rowToHeight[row] == 0) {return;}
  var off = perTracksize[rowToHeight[row]].eventoffset;
  var y0 = Rnd1(y_zoomed(rowToTrack[row]) + off);
  var labelypos = y0 + 0.4 * fontsize;
  var labelid = "labeltext";
  var labelxpos = region2width - 6;
  var labelanchor = "end";
  var labelweight = "normal";
  var labelfill = "black";
  var labeltext = sortName[row];
  // var labeltext = row + " " + sortName[row];
  var is_group_label = (sortNum[row] < 0);
  if (is_group_label) {
    // Always show group names 
    // Left-justify group names
    var prefix = -sortNum[row] - 1;
    state2.grouplabelypos[prefix] = labelypos;
    labelid += (prefix);	// "labeltext0", labeltext1", etc.
    labelxpos = 0;
    labelanchor = "start";
    labelweight = "bold";
    // Light gray group label if no items, else blue
    labelfill = (state2.grouplength[prefix] == 0) ? "#c0c0c0" : "blue";
    labeltext = expandArrow(prefix) + " " + sortName[row] + 
      " (" + state2.grouplength[prefix] + ")"; 
  } else {
    // Keep regular labels spaced out, but always draw first one after group label
    if (y0 < next_ylabel_y0) {
      // Little tick mark as visual clue something is missing
      var path = "M" + Rnd1(region2width) + "," + Rnd1(y0) + " h-2";
      region2dotg.append("path")
        .attr("class", "labeltick")
        .attr("d",  path)
        .attr("stroke-width", "1")	
        .attr("stroke", "#000000");		// black
      return;
    }
    next_ylabel_y0 = y0 + fontsize;

    // Bold labels for shift-click explicitly-highlighted rows
    if (state2.rowToHilite[row]) {labelweight = "bold";}
  }

  var label = region2dotg.append("text") 
      .attr("id", labelid) 
      .attr("class", "labeltext noselect")           
      .attr("transform", "translate(" + (labelxpos) + ", " +
            (labelypos) + ")")
      .attr("width", region2width)
      .attr("font-size", fontsize + "px")
      .attr("font-family", "sans-serif")
      .attr("font-weight", labelweight)
      .style("text-anchor", labelanchor)
      .style("fill", labelfill)
      .text(labeltext);

  // Tick mark
  if (! is_group_label ) {
    var path = "M" + Rnd1(region2width) + "," + Rnd1(y0) + " h-4";
    region2dotg.append("path")
      .attr("class", "labeltick")
      .attr("d",  path)
      .attr("stroke-width", "1")	
      .attr("stroke", "#000000");		// black
  }
}


// Redraw the zoomed Y-axis 
function redrawYlabels() {
//console.log("redrawYlabels");
  d3.selectAll("#labeltext").remove();
  d3.selectAll("#labeltext0").remove();
  d3.selectAll("#labeltext1").remove();
  d3.selectAll("#labeltext2").remove();
  d3.selectAll("#labeltext3").remove();
  d3.selectAll(".labeltick").remove();

  // The labels
  next_ylabel_y0 = 0;
  for (var row = 0; row < sortNumSize; ++row) {
    draw_ylabel(row, textsize.smalltick);
  }

  if (state2.grouplength[prefixCpu] > 0) {
    document.getElementById("labeltext0").addEventListener("click", doToggleCompress0);
  }
  if (state2.grouplength[prefixPid] > 0) {
    document.getElementById("labeltext1").addEventListener("click", doToggleCompress1);
  }
  if (state2.grouplength[prefixRpc] > 0) {
    document.getElementById("labeltext2").addEventListener("click", doToggleCompress2);
  }
  if (state2.grouplength[prefixRes] > 0) {
    document.getElementById("labeltext3").addEventListener("click", doToggleCompress3);
  }
}


function draw_callout_mouse() {}

function draw_initial_pan_zoom() {
  // Last, add the rectangle we anchor the zoom action to
  // If we add it earlier, we can't zoom if exactly over the line or dots
  // This draws on top of Region3 
  panzoomrect_x = svgdotg.append("rect")
    .attr("id", "panzoomrect_x")    
    .attr("x", axesmargin)       
    .attr("width", region3width - axesmargin)
    .attr("height", region234height + region5height/2)
    .attr("style", "fill:rgb(255,240,255); opacity:0");	// Transparent. 0=>0.5 to make visible

  // Define a zoom, no attach, no draw; define watcher
  // Initializes with zoomIdentity
  myZoom_x = d3.zoom()
    .filter(myFilter)
    .on("zoom", do_zoomed_x);
 
  // Apply zoom function to entire inner rectangle; sets watcher
  panzoomrect_x.call(myZoom_x);     // This just enables on the rect itself, passive


  panzoomrect_y = svgdotg.append("rect")
    .attr("x", -(0.75 * region2width))
    .attr("id", "panzoomrect_y")            
    .attr("width", (0.75 * region2width) - 5)
    .attr("height", region234height - axesmargin)
    .attr("style", "fill:rgb(255,255,240); opacity:0"); // Transparent. 0=>0.5 to make visible

  // Define a zoom, no attach, no draw; define watcher
  // Initializes with zoomIdentity
  myZoom_y = d3.zoom()
    .filter(myFilter)
    .on("zoom", do_zoomed_y);
 
  // Apply zoom function to entire inner rectangle; sets watcher
  panzoomrect_y.call(myZoom_y);     // This just enables on the rect itself, passive

}

function redrawPanZoom() {
}

function resetDefer(row) {
  defer_dur_i[row] = 0; 
  defer_dur_u[row] = 0; 
  defer_dur_k[row] = 0; 
  defer_dur[row] = 0; 
  defer_event_u[row] = 0;
  defer_event_k[row] = 0;
}

function initializeDrawingState() {
  // Initialize deferred
  for (var row = 0; row < sortNumSize; ++row) {
    resetDefer(row);
    highwater_marks[row] = 0;
    highwater_mark_type[row] = 0;
    rowToLabelk[row] = 0;		// Rotate event labels separately for each row
    rightmostnum[row] = 0;		// Spread apart mark_d numbers
    pending_locks[row] = [];		// Empty array of pending locks
  }
}


function redrawEvents() {
  //console.log("redrawEvents");
  InitMatchcount();
  fastdrawevents();
  if (state.hilite_evnum != 0) {
    ShowMatchcount();
  }
  svgdotg.on("mousemove", mousemove_region3);
}


//// Called when starting all over with new window size
// Outer and inner SVGs exist but are empty
//
// Order of drawing:
//  +Set x and y domain & range
//  Calculate row height and tracks
//  Calculate basetime
//
//  Draw all the events in region3
//  Draw marks
//  Draw IPC
//
//  Draw Y-axis in region2
//  Draw X-axis in region5
//  Draw grid in region3
//
//  Draw scroll in region3
//  Draw scroll in region2
//
// Remove and reset annotations, calc X and Y scaling, draw events, draw IPC legend
// Set up x zoom and call resetonscreen_x before calling this
// Do this before drawing axes, which depend on calculateBasetime
//
function redrawEventsEtc() {
  //console.log("redrawEventsEtc", data.events.length, "events");

  resetlines();
  resetRotatingLabelk();

  // Calculate per-row track height and position
  calculateRowTracks();

  // Calculate and display basetime
  calculateBasetime();

  // Draw all events onscreen, plus marks, plus IPC
  redrawEvents();

  // If there is a previous single annotation and it is onscreen, draw it
  if (state2.annotated_one_d != -1) {
    // Also put just it into annotated list so that further pan/zoom finds it
    state.annotated_d = [];
    state.annotated_d.push(state2.annotated_one_d);  // Keep
    var d = data.events[state2.annotated_one_d];
    if (startsonscreen_x(d)) {
      var short = false;
      var rotate = true;
      // Force full name even if using simple annots
      var temp = state2.annottype_value;
      state2.annottype_value = 0;
      annotatespan(d, "first", short, -1, rotate);
      state2.annottype_value = temp;
    }
  }

  // Draw IPC legend (possibly overlays events)
  redrawIpcLegend();

  // redrawMarks();

};

function delayed_redraw_events_axes() {
  redrawEventsEtc();
  redrawXaxis();
  redrawYlabels();
}

function redraw_events_axes() {
  clearTimeout(timerHandle);
  timerHandle = setTimeout(delayed_redraw_events_axes, 50);  // msec
}

 
// Draw axes and labels and pan/zoom overlays, reset X and Y bounds
function draw_initial_axes() {
  // Draw X-axis and grid
  draw_initial_xaxis();

  // Draw Y-axis labels
  draw_initial_yaxis();
  redrawYlabels();

  // Draw pan/zoom overlays
  redrawPanZoom();
} 



//========
// Routines for initialization 


// fraction3 is [percentage, minimum, maximum]
function calcfraction(total, fraction3) {
  var temp = Math.round(total * fraction3[0] / 100);
  if (fraction3[1] != 0) {temp = Math.max(temp, fraction3[1]);}
  if (fraction3[2] != 0) {temp = Math.min (temp, fraction3[2]);}
  return temp;
}

// Called whenever the browser window changes or any controls that affect the
// region layout, such as aspect, Y-axis label chars or pixels, bare.
// Note that restore can change some of these
// Sets global basictextsize
function calc_regionheightwidth() {
//console.log("calc_regionheightwidth", svgWidth, svgHeight);
  ////var txtmul = textmultiplier_tbl[state.textmultiplier_value];
  var txtmul = 1.0;
  //if (presentation) {txtmul *= 1.2;}

  var approxregion5height = calcfraction(svgHeight, region5paramH);
  // Two lines of text; subtract 6 px padding for the axis and its ticks
  basictextsize = (approxregion5height - 6) / 2;
  basictextsize = Rnd0(txtmul * calcfraction(basictextsize, basictextparamH));
  // If region2px set, force textsize to that instead
  if (0 < textsize.region2px) {basictextsize = textsize.region2px;}
console.log("=== basictextsize", basictextsize);
  resetText(document.getElementById("DebugRegion2Px"), basictextsize);

  region5height = 6 + basictextsize * 2;
  region1height = region5height;
  region234height = svgHeight - region1height - region5height; 

  var approxregion2width = Rnd0(txtmul * calcfraction(svgWidth, region2paramW));
  // Average character width is 0.6 em
  var region2chars = approxregion2width / (basictextsize * 0.6);
  region2chars = Rnd0(calcfraction(region2chars, region2charsparamW));

  // Use the value set manually
  if (0 < textsize.region2chars) {region2chars = textsize.region2chars;}

  region1width = svgWidth;
  region2width = Rnd0(region2chars * (basictextsize * 0.6));
  region2widthshort = region2width - basictextsize;	// Allow some room at x-axis
  // If bare, suppress region 4
  region4width = Rnd0(calcfraction(svgWidth, region4paramW));
  region3width = svgWidth - region2width - region4width;
  // Safety move for too-narrow aspect ratios
  while (region3width <= 0) {
    region2width /= 2;
    region2widthshort /= 2;
    region2chars /= 2;
    region4width /= 2;
    region3width = svgWidth - region2width - region4width; 
  }
  textsize.region2chars = region2chars;

  // Allow some slop room for labels and timelines to hang out into region4
  region3clipwidth = region3width + (region4width / 2); 
  // region5width = svgWidth;
console.log("    region 1 234 5 heights(px) =", region1height, region234height, region5height);
console.log("    region 2 3 4 widths(px) =", region2width, region3width, region4width);
}


// Try basing everything on the X-axis text size, approx 4% of total height
// but min 12px max 30px
// Region5 gets room for 2 lines of X-axis numbers plus label plus 
//  1/2 line for axis/tics/padding = 2.5 lines
// Region1 gets the same: 1 line 2x font size plus 1/2 line padding  
function new_calc_layout() {
  // Sets basictextsize, among other things
  calc_regionheightwidth();

  // Apply debug row count if forced
  var cur_tracks = maxTrack;
  if (0 < textsize.rows) {
    cur_tracks = rowToTrack[textsize.rows + 1] - rowToHeight[0];
  }

// Track height and subdivide rows for spans and annot text
  ypixpertrack = region234height / cur_tracks;
  calcPixHeightsPerTrack(ypixpertrack);

  // Set up matching text sizes
  // Title text size is 0.9 * region1height, which in turn is function of textmultiplier 
  // X-axis text size is 0.9 * region5height / 2
  // Small X-axis text size is 0.75 * region5height / 2
  // Annotation text size is ... Figure 16 CPU lines + 4 group labels + 1 or so = 21
  //  lines in 80% of the height, so each line is about 4% of the height
  //  we want lines of small text to fit into half this 

  textsize.region2px = basictextsize;
  textsize.title = 2 * basictextsize;	// Top title
  textsize.label = basictextsize;	// X axis label
  textsize.tick  = basictextsize;	// Basetime
  textsize.annot = basictextsize;	// Annotations
  textsize.smalltick = basictextsize;	// Delta, X numbers, Y row label, IPC, bracket
}


function allocOuterSvg() {
  winWidth = window.innerWidth;
  winHeight = window.innerHeight;

  // Leave approximate room for Regions 0 and 6 (HTML lines)
  svgWidth = winWidth - (winMargin2.left + winMargin2.right);
  svgHeight = winHeight - (winMargin2.top + winMargin2.bottom);
  if (winWidth < winMargin2.width1) {
    // Make room for two lines at top and two at bottom if narrow window
    svgHeight = winHeight - (winMargin2.top2x + winMargin2.bottom2x);
  }

  // To allow more consistent screen shots, force aspect ratio
  // and make height granular as multiples of 100px. Height drives width.
  if (0 < aspectv) {	// Auto = 0:0 signifies no forcing
    svgHeight = Math.trunc(svgHeight / 100) * 100;
    svgWidth = Math.round((svgHeight * aspecth) / aspectv);
  }

//console.log("winWidth", winWidth, "winHeight", winHeight);
//console.log("svgWidth", svgWidth, "svgHeight", svgHeight);

  // TEMP: Size feedback onscreen for 5 seconds
  document.getElementById("debugout").innerHTML = svgWidth + "x" + svgHeight;
  clearTimeout(timerHandle);
  setTimeout(function () {document.getElementById("debugout").innerHTML = "";}, 4000);

  // Remove any existing outersvg and all its contents
  d3.select("svg").remove() ;
 
  // Allocate new one
  outersvg = d3.select("span#anchorsvg").append("svg")
    .attr("class", "svgtop")
    .attr("width", svgWidth)
    .attr("height", svgHeight);

  new_calc_layout();


  // TEMP show outer SVG with very light green
  //outersvg.append("rect")
  //  .attr("width", "100%")
  //  .attr("height", "100%")
  //  .attr("fill", "#FFFFF8");	// very light yellow

  // TEMP: Show the origin with (quarter) green circle
  //outersvg.append("circle")
  //  .attr("cx", 0)
  //  .attr("cy", 0)
  //  .attr("r", 7)
  //  .attr("fill", "green");
//console.log("  green circle should be showing");

  // The outer SVG has an offset group to contain all its children
  svgdotg =  outersvg.append("g")
    .on("dblclick", mouse_dblclick)
    .on("mousedown", mousedown_x)
    .on("mouseup", mouseup_x)
    .on("mousemove", mousemove_region3)
    .attr("class", "svgdotg")
    .attr("transform", "translate(" + region2width + "," + region1height + ")");

  // And within outer, the inner SVG, aka Region 3
  innersvg = svgdotg.append("svg")
    .attr("class", "region3")
    .attr("width", region3clipwidth)  // Allow room for labels to hang out right
    .attr("height", region234height);

  // And that inner SVG has a group to contain all its children
  // We catch mouse events in it also, for annotate
  innersvgdotg = innersvg.append("g");

// Bare: can't get mouse events to callouts otherwise...
  svgdotgbare = outersvg.append("g")
    .attr("transform", "translate(" + region2width + "," + region1height + ")");
  innersvgbare = svgdotgbare.append("svg")
    .attr("width", region3clipwidth)  // Allow room for labels to hang out right
    .attr("height", region234height);
  innersvgdotgbare = innersvgbare.append("g");


  // TEMP: show innersvg with light red/gray for debug
  // innersvgdotg.append("rect")
  //   .attr("width", region3width)
  //  .attr("height", region234height)
  //  .attr("fill", "#FFF0F0");	// light red/gray

  // And within outer, an SVG area for Region 1
  region1 = svgdotg.append("svg")	// We want clipping to this rectangle
    .attr("class", "region1")
    .attr("x", -region2width)
    .attr("y", 0)
    .attr("width", region1width)
    .attr("height", region1height);

  // And within outer, an SVG area for Region 2
  region2 = svgdotg.append("svg")	// We want clipping to this rectangle
    .attr("class", "region2")
    .attr("x", -region2width)
    .attr("y", 0)
    .attr("width", region2width)
    .attr("height", region234height);

 // And within that, a group for Region 2
  region2dotg = region2.append("g"); 

  // And within outer, a group for Region 4
  region4 = svgdotg.append("g")
    .attr("class", "region4")
    .attr("x", region3width)
    .attr("y", 0)
    .attr("width", region4width)
    .attr("height", region234height);

  // And within outer, a group for Region 5
  region5 = svgdotg.append("g")
    .attr("class", "region5")
    .attr("x",  -region2width)
    .attr("y", region234height)
    .attr("width", svgWidth)
    .attr("height", region5height);

  // Show the base point in red (decorateDot cyan if colorblind toggle is 1)
  reddot = svgdotg.append("circle")
    .attr("id", "reddot")
           .attr("cx", 0)
           .attr("cy", region234height)
           .attr("r", 7)
           .attr("fill", "red");
  // Set listeners for red dot
  reddot.on("click", resetzoom);	// shift-click toggles secondary menu

//console.log("  red circle should be showing");
}


// Button is unavailable if val < 0
// Button is inactive if val == 0
// Button is active if val >= 0
function decorateButton(elem, val) {
  if (val > 0) {
    // active
    elem.style="background-color: rgb(240,240,255); color: rgb(0,0,0); border-color: rgb(0,0,255); border-radius:6px";
  } else if (val == 0) {
    // inactive
    elem.style="background-color: rgb(255,255,255); color: rgb(0,0,0); border-color: rgb(192,192,192); border-radius:6px";
  } else {
    // -1 = unavailable
    elem.style="background-color: rgb(255,255,255); color: rgb(224,224,224); border-color: rgb(224,224,224); border-radius:6px";
  }
}

// Three values. 0:blank, 1:center, 2:fill
function decorateButton3(elem, val) {
  if (val > 0) {
    // active
    if (val == 1) {
      elem.style="background-image: linear-gradient(to right, white, rgb(192,192,255), white); color: rgb(0,0,0); border-color: rgb(0,0,255); border-radius:6px";
    } else {
      elem.style="background-color: rgb(240,240,255); color: rgb(0,0,0); border-color: rgb(0,0,255); border-radius:6px";
    }    
  } else if (val == 0) {
    // inactive
    elem.style="background-color: rgb(255,255,255); color: rgb(0,0,0); border-color: rgb(192,192,192); border-radius:6px";
  } else {
    // -1 = unavailable
    elem.style="background-color: rgb(255,255,255); color: rgb(224,224,224); border-color: rgb(224,224,224); border-radius:6px";
  }
}


// Four values. 0:blank, 1:left, 2:right, 3: fill
function decorateButton4(elem, val) {
  if (val > 0) {
    // active
    if (val == 1) {
      elem.style="background-image: linear-gradient(to right, white, rgb(192,192,255)); color: rgb(0,0,0); border-color: rgb(0,0,255); border-radius:6px";
    } else if (val == 2) {
      elem.style="background-image: linear-gradient(to right, rgb(192,192,255), white); color: rgb(0,0,0); border-color: rgb(0,0,255); border-radius:6px";
    } else {
      elem.style="background-color: rgb(240,240,255); color: rgb(0,0,0); border-color: rgb(0,0,255); border-radius:6px";
    }    
  } else if (val == 0) {
    // inactive
    elem.style="background-color: rgb(255,255,255); color: rgb(0,0,0); border-color: rgb(192,192,192); border-radius:6px";
  } else {
    // -1 = unavailable
    elem.style="background-color: rgb(255,255,255); color: rgb(224,224,224); border-color: rgb(224,224,224); border-radius:6px";
  }
}

// Save is active if val == true
function decorateSave(elem, val) {
  var fontsize = 24;
  var c = elem.textContent[0];
  if (('0' <= c) && (c <= '~')) {fontsize = 20;} 
  if (val) {
    // active
    elem.style="fill: rgb(0,0,0); font-weight: bold; color:blue; font-size:" + fontsize + "px;";
  } else {
    // inactive
    elem.style="fill: rgb(128,128,128); color:gray; font-size:" + fontsize + "px;";
  }
}

// Save is active if val == true
function decorateMore(elem, val) {
 if (val) {
    // active
    elem.style="fill: rgb(0,0,0); font-weight: bold; color:black; font-size:12px;";
  } else {
    // inactive
    elem.style="fill: rgb(128,128,128); color:gray; font-size:12px;";
  }
}

// Flip reddot to light cyan if color-blind is set
function decorateDot(elem, val) {
 if (val) {
    elem.style="fill: rgb(128,255,255);";
  } else {
    elem.style="fill: rgb(255,0,0);";
  }
}

function resetText(elem, val) {
  elem.value = val;
  if (IsActiveText(val)) {elem.style.backgroundColor = kActiveBackColor;}
  else                   {elem.style.backgroundColor = kInactiveBackColor;}
}

function resetNum(elem, num) {
  if (num == 0) {
    elem.value = "";
    elem.style.backgroundColor = kInactiveBackColor;
  } else {
    elem.value = num;
    elem.style.backgroundColor = kActiveBackColor;
  }
}

// Fill in UI text and buttons entirely from current state
// Must be called after fillOuterSvg establishes save0text, etc.
function redrawUI() {
  if (state.hilite_evnum == 0) {
    resetText(document.getElementById("SearchText"), state2.search_text);
  }
  resetNum(document.getElementById("SearchMin"), state2.usec_lo_time);
  resetNum(document.getElementById("SearchMax"), state2.usec_hi_time);

  document.getElementById("matchcount").innerHTML = "";
  document.getElementById("showversion").innerHTML = script_edit_date;


  ////decorate_annottype();
  document.getElementById("annottype").innerHTML = annottype_name[state2.annottype_value];
  decorateButton(document.getElementById("annotateuser"), state2.annotateuser_value);
  decorateButton(document.getElementById("annotateall"), state2.annotateall_value);

  decorateButton4(document.getElementById("showmarks"), state2.marks_value);
  decorateButton3(document.getElementById("showarcs"), state2.arcs_value);
  decorateButton3(document.getElementById("showlock"), state2.lock_value);
  decorateButton3(document.getElementById("showfreq"), state2.freq_value);

  decorateButton4(document.getElementById("showipc"), state2.ipc_value);
  decorateButton3(document.getElementById("showsamp"), state2.samp_value);
  decorateButton(document.getElementById("showcb"), state2.cb_value);

  ////decorateButton(document.getElementById("AddAnnot"), 0);

  decorateButton(document.getElementById("searchnot"), state2.searchnot_value);

  if (0 == legend_value) {	// No red dot if legend showing
    decorateDot(document.getElementById("reddot"), state2.cb_value);
  }

  if ((typeof data.kernelVersion === 'undefined') && 
      (typeof data.cpuModelName === 'undefined')) {
    // nevermind
  } else {
    if (state.just_reset) {
      document.getElementById("matchcount").innerHTML = 
        data.kernelVersion + ", " + data.cpuModelName;
      if (is_riscv) {
          document.getElementById("debugout").innerHTML = 
        "<span style=\"font-weight:bold; color:red\">LOW-RESOLUTION TIMER</span>";
      }
    } else {
      document.getElementById("matchcount").innerHTML = "";
    }
  }

  if (debug) {
    resetText(document.getElementById("DebugRegion2Chars"), textsize.region2chars);
    resetText(document.getElementById("DebugRegion2Px"), textsize.region2px);
    resetText(document.getElementById("DebugSpn"), textsize.spn);
    resetText(document.getElementById("DebugTxt"), textsize.txt);
    decorateButton3(document.getElementById("legend"), legend_value);
    decorateButton(document.getElementById("fade"), fade_value);
  }
}


// If shift-click, cycle 0, max, max-1, ...
// If plain click, toggle between 0 and max
function doToggle(shifted, val, maxv) {
  var newval;
  if (val == -1) {return val;}	// This item not available
  if (shifted) {
    if (val == 0) {newval = maxv;} else {newval = val - 1;}
  } else {
    if (val == 0) {newval = maxv;} else {newval = 0;}
  }
//console.log ("  doToggle", shifted, val, maxv, "=", newval);
  //d3.event.stopPropagation();
  return newval;
}

// If no highlighted rows, skip color-only state
function doToggleCmp(shifted, val, maxv) {
  var newval = doToggle(shifted, val, maxv);
  return newval;
}

// Draw a light gray rectangle at the right margin, beyond the scroll-select area
// This is relative to svgdotg, which is offset by <region1height, region2width>
function redrawIpcLegend() {
  var legendwidth = svgWidth - region2width - region4width;
  if (state2.ipc_value > 0) {
    draw_ipcllclegend(legendwidth, 0, region234height, region4width); 
  } else {
    draw_emptylegend(legendwidth, 0, region234height, region4width);
  }
}


function redrawMarks() {
  // Remove any previous markmarks
  resetmark();

  redrawUI();
  if (state2.marks_value > 0) {
    // Draw markmarks
    redrawEvents();
  }
}

function doToggleIpc(e) {
  if (state2.ipc_value < 0) {return;}	// unavailable
  state2.ipc_value = doToggle(true, state2.ipc_value, ipc_value_max);
  redrawUI();
  //redrawIpcLegend();
  redrawEventsEtc();
}

function doToggleMarks(e) {
  state2.marks_value = doToggle(true, state2.marks_value, marks_value_max);
  redrawUI();
  //redrawMarks();
  redrawEventsEtc();
}

function doToggleArcs(e) {
  state2.arcs_value = doToggle(true, state2.arcs_value, arcs_value_max);
  redrawUI();
  redrawEventsEtc();
}

// UNUSED
function doToggleWaits(e) {
  state2.waits_value = doToggle(e.shiftKey, state2.waits_value, waits_value_max);
  redrawUI();
  redrawEventsEtc();
}

function doToggleFreq(e) {
  state2.freq_value = doToggle(true, state2.freq_value, freq_value_max);
  redrawUI();
  redrawEvents();
}

function doToggleOvhd(e) {
  state2.ovhd_value = doToggle(e.shiftKey, state2.ovhd_value, ovhd_value_max);
  redrawUI();
  redrawEvents();
}

function doToggleLock(e) {
  state2.lock_value = doToggle(true, state2.lock_value, lock_value_max);
  redrawUI();
  redrawEvents();
}

// If shift-click, toggle hack_value instead
function doToggleCb(e) {
  if (e.shiftKey) {
    state2.hack_value = doToggle(e.shiftKey, state2.hack_value, hack_value_max);
    redrawUI();
    redrawEvents();
    return;
  }
  state2.cb_value = doToggle(e.shiftKey, state2.cb_value, cb_value_max);
  currentcolor = (state2.cb_value == 0) ? normalcolor : colorblindcolor;
  redrawUI();
  redrawEvents();
}

// UNUSED
function doToggleAspect(e) {
  //state.aspect_value = doToggle(e.shiftKey, state.aspect_value, aspect_value_max);
  //allocOuterSvgEtc();
}
// end UNUSED

// Use shift key to get to the hidden show idle PC state
function doToggleSamp(e) {
  if (state2.samp_value < 0) {return;}	// unavailable
  state2.samp_value = doToggle(e.shiftKey, state2.samp_value, samp_value_max);
  redrawUI();
  redrawEvents();
}

////function decorate_annottype() {
////  document.getElementById("annottype").innerHTML = annottype_name[state2.annottype_value];
////}

function doToggleAnnottype(e) {
  state2.annottype_value = doToggle(true, state2.annottype_value, annottype_max);
  // Turn off annotateuser and annotateall 
  state2.annotateuser_value = 0;
  state2.annotateall_value = 0;
  redrawUI();
}

function doToggleAnnotateuser(e) {
  state2.annotateuser_value = doToggle(e.shiftKey, state2.annotateuser_value, 
    annotateuser_value_max);

  // Turn off annotateall if annotateuser is active
  if (state2.annotateuser_value > 0) {state2.annotateall_value = 0;}

  // Remove any previous lines. 
  resetlines();
  redrawUI();

  if (state2.annotateuser_value > 0) { 
    draw_annotateuser();
  }
}

function doToggleAnnotateall(e) {
  state2.annotateall_value = doToggle(e.shiftKey, state2.annotateall_value, 
    annotateall_value_max);

  // Turn off annotateuser if annotateall is active
  if (state2.annotateall_value > 0) {state2.annotateuser_value = 0;}

  // Remove any previous lines. 
  resetlines();
  redrawUI();

  if (state2.annotateall_value > 0) { 
    draw_annotateall();
  }
}

function doToggleSearchnot(e) {
  state2.searchnot_value = doToggle(e.shiftKey, state2.searchnot_value, 
    searchnot_value_max);

  // Turn off annotateall/user when search is active
  state2.annotateall_value = 0;
  state2.annotateuser_value = 0;

  // Remove any previous lines. 
  resetlines();
  redrawUI();

  annotatesearch2(document.getElementById("SearchText").value, 
                  document.getElementById("SearchMin").value,
                  document.getElementById("SearchMax").value);
}

function decorate_units() {
  document.getElementById("minmax_units").innerHTML = units_name[state2.minmax_units_value];
}

function doToggleUnits(e) {
  state2.minmax_units_value = doToggle(true, state2.minmax_units_value, minmax_units_max);
  decorate_units();
  annotatesearch2(document.getElementById("SearchText").value, 
                  document.getElementById("SearchMin").value,
                  document.getElementById("SearchMax").value);
}

// This rotates thru  normal, landscape legend, portrait legend
// saving and restoring normal
function doToggleLegend(e) {
  if (legend_value == 0) {DoSave(0);}
  legend_value = doToggle(true, legend_value, legend_max);
  allocOuterSvgEtc();
  if (legend_value == 0) {DoRestore(0);}
}

function doToggleFade(e) {
  fade_value = doToggle(true, fade_value, fade_max);
  redrawUI();
  redrawEvents();
}

// extra[n] is a 10-item event
// savedview[n][0] is a name 
// savedview[n][1] is null or a json string with compressed rowToHilite array
// The exported text replaces the last line of the corresponding .json file
function doToggleExport(e) {
  const el = document.createElement('textarea');
  el.value = "],";

  // Export the callout array
  el.value += '\n"extra" : [';
  for (i = 0; i < data.extra.length; ++i) {
    if (0 < i) {el.value += ',';}
    el.value += "\n  " + JSON.stringify(data.extra[i]); 
  }
  el.value += '\n],';

  // Export the view array
  el.value += '\n"savedview" : [';
  for (i = 0; i < data.savedview.length; ++i) {
    if (0 < i) {el.value += ',';}
      el.value += "\n  [\"" + data.savedview[i][0] + "\", " + data.savedview[i][1] + "]"; 
  }
  el.value += '\n]}';
//console.log(el.value);

  document.body.appendChild(el);
  el.select();
  document.execCommand('copy');
  alert("Copied " + +data.extra.length + " callouts, " + data.savedview.length + " views\nto clipboard");
  document.body.removeChild(el);
}

function doToggleBasetimetext(e) {
console.log("doToggleBasetimetext");
  state2.basetime0_value = doToggle(e.shiftKey, state2.basetime0_value, 
    basetime0_value_max);
  redrawUI();
  redrawEventsEtc();

  // Click shows what's there for debug (max 16)
  for (i=0; i < 16; ++i) {
    if (localStorage.key(i)) {
      console.log("localStorage.key(", i, ")", localStorage.key(i), "size=", 
        localStorage.getItem(localStorage.key(i)).length);
    }
  }
  // Shift-click clears
  if (e.shiftKey) {
    console.log("localStorage.clear()");
    window.localStorage.clear();
    console.log("localStorage.key(0)",  state2.basetime0_value, localStorage.key(0));
  }
}

function getTracksAbove(n) {
  return state2.groupfirsttrack[n];
}



// Not sure of best consistency for adding/removing rows
//  The verytemp version keeps top of screen nearly constant
function updateCompressEtc(n) {
  // Remember the clicked label Y position
  var tracksabove = getTracksAbove(n);
  var oldlabelypos = state2.grouplabelypos[n];
  var oldmaxTrack = maxTrack;

//VERYTEMP
var temp = state2.savedtransformY;

  // Renumber the track heights per row based on new expand/compress value
  updateTracks();

  var newmaxTrack = maxTrack;

  // Y-axis range is (2 * axesmargin) short
  // oldlabelypos = region234height * above/oldmax
  // newlableypos = region234height * above/newmax
  var delta = region234height * (tracksabove/oldmaxTrack - tracksabove/newmaxTrack);

//console.log("updateCompressEtc", oldlabelypos, tracksabove, oldmaxTrack, newmaxTrack, delta);

  // WANT TO alter y_zoomed to put clicked row back where it was vertically 
  // So move Y down by delta
  state2.savedtransformY = state2.savedtransformY.translate(0, delta);
//VERYTEMP
state2.savedtransformY = temp;

  // These are needed to have the restored transforms stick
  panzoomrect_y.call(myZoom_y.transform, state2.savedtransformY);
  panzoomrect_y.call(myZoom_y);

//console.log("1 state2.savedtransformY", state2.savedtransformY);
 
  // Use y_zoomed when drawing everything after this  
  // Set up y_zoomed, x_zoomed and Recalculate Y- and X- on-screen limits

  updateTracks();
  y.domain([0, maxTrack]);
  resetRangeDomain();
//console.log("resetRangeDomain in updateCompressEtc");
  calcPixHeightsPerTrack(ypixpertrack);

  redrawEventsEtc();

  // Axes depend on calculateBasetime in redrawEventsEtc
  // Redraw the zoomed Y-axis
  redrawYlabels();
//console.log("2 state2.savedtransformY", state2.savedtransformY);
}

// CPU group
function doToggleCompress0(e) {
  state2.groupcompress[0] = 
    doToggleCmp(state2.grouphashilite[0], state2.groupcompress[0], compress_value_max);
  updateCompressEtc(0);
}

// PID group
// If no highlighted rows, just toggle on/off. If any highlighted, rotate thru compressed
function doToggleCompress1(e) {
  state2.groupcompress[1] = 
    doToggleCmp(state2.grouphashilite[1], state2.groupcompress[1], compress_value_max);
  updateCompressEtc(1);
}

// RPC group
function doToggleCompress2(e) {
  state2.groupcompress[2] = 
    doToggleCmp(state2.grouphashilite[2], state2.groupcompress[2], compress_value_max);
  updateCompressEtc(2);
}

// Resource group
function doToggleCompress3(e) {
  state2.groupcompress[3] = 
    doToggleCmp(state2.grouphashilite[3], state2.groupcompress[3], compress_value_max);
  updateCompressEtc(3);
}


// Pure save
function DoSave(n) {
    // Capture current pan/zoom transforms
    data.savedview[n][1] = packupState();
    decorateSave(document.getElementById("view" + n), true);
}

// Pure restore and then redraw
function DoRestore(n) {
    unpackState(data.savedview[n][1]);

    var px0 = x.range()[0];	// New if window changed
    var px1 = x.range()[1];	// New if window changed
    var ts0 = x.domain()[0];	// Unchanged
    var ts1 = x.domain()[1];	// Unchanged
    var py0 = y.range()[0];	// New if window changed
    var py1 = y.range()[1];	// New if window changed
    var trk0 = y.domain()[0];	// Unchanged
    var trk1 = y.domain()[1];	// Unchanged

//console.log("Restore", n, "range", px0, px1, "domain", ts0, ts1);
//console.log("       ", region2width, region3width, region4width);
//console.log("       ", state2.savedtransformX);

// New calculation 2020.08.03
    var xleft = state2.xleft_ts;		//// Unchanged
    var new_xk = state2.savedtransformX.k;	// Unchanged
    var new_xx = -((xleft - ts0) * (px1 - px0) / (ts1 - ts0) + px0) * new_xk;
    var new_xy = state2.savedtransformX.y;	// Unchanged

    var ytop = state2.ytop_track;		// Unchanged
    var new_yk = state2.savedtransformY.k;	// Unchanged
    var new_yx = state2.savedtransformY.x;	// Unchanged
    var new_yy = -((ytop - trk0) * (py1 - py0) / (trk1 - trk0) + py0) * new_yk;

    // These reset the transformations to identity, overwriting 
    // savedtransformX/Y, which cannot easily be assigned because 
    // they are functions, not just simple variables.
    redrawUI(); 
    resizeWindowEtc();

    state2.savedtransformX = d3.zoomIdentity.translate(new_xx, new_xy).scale(new_xk);
//console.log("       ", state2.savedtransformX);

    // These are needed to have the restored transforms stick
    panzoomrect_x.call(myZoom_x.transform, state2.savedtransformX);
    panzoomrect_x.call(myZoom_x);

    state2.savedtransformY = d3.zoomIdentity.translate(new_yx, new_yy).scale(new_yk);
    // These are needed to have the restored transforms stick
    panzoomrect_y.call(myZoom_y.transform, state2.savedtransformY);
    panzoomrect_y.call(myZoom_y);

    // Renumber the track heights per row, to auto-prune rows no longer visible
    updateTracks();
    y.domain([0, maxTrack]);
    resetRangeDomain();
    calcPixHeightsPerTrack(ypixpertrack);
 
    redrawEventsEtc();
    redrawXaxis();
    redrawYlabels();

    // Restore text about x/y start and width
    document.getElementById("xstartwidth").innerHTML =  
      RndUsec(state2.xleft_ts) + "+" + Rnd0(RndUsec(state2.xright_ts - state2.xleft_ts) * 1000000);
    document.getElementById("ystartwidth").innerHTML =  
      (state2.ytop_track) + "+" + (state2.ybot_track - state2.ytop_track);
}

function doSaveRestore(shifted, n) {
console.log("doSaveRestore -----");

  if (shifted) {
    // ------ Save -----------------------------------------------------------//
    DoSave(n);
    redrawUI();

    // blink region 3 to give feedback that save happened
    var oldfade = fade_value;
    fade_value = 1 - fade_value; redrawEvents();
    setTimeout(function() {fade_value = oldfade; redrawEvents();}, 100);

    console.log("doSaveRestore[", n, "] ------ saved");

  } else {
    if (data.savedview[n][1] == null) {
      // ------ Nothing to Restore -------------------------------------------//
      // No state was saved; cannot restore
      console.log ("doSaveRestore[", n, "] ------ nothing to RESTORE");
      return;
    }

    // ------ Restore --------------------------------------------------------//
    // Swap 0 <== current, current <== n
    console.log("doSaveRestore[", n, "] ------ restoring ...");

    // Save current state unless we are doing Back to savedstate[0]
    if (n != 0) {
      data.savedview[0][1] = packupState();
      decorateSave(document.getElementById("view0"), true);
    }

    DoRestore(n);
console.log("doSaveRestore[", n, "] ------ restored");
  }
}


function doToggleState0(e) {
  doSaveRestore(e.shiftKey, 0);
}

function doToggleState1(e) {
  doSaveRestore(e.shiftKey, 1);
}

function doToggleState2(e) {
  doSaveRestore(e.shiftKey, 2);
}

function doToggleState3(e) {
  doSaveRestore(e.shiftKey, 3);
}

function doToggleState4(e) {
  doSaveRestore(e.shiftKey, 4);
}

// Add a callout at annotated_one_d or if none, at middle of region 3 
// user to drag to final place next
function doAddCallout(e) {
  var lbl = prompt("New callout name:", "1");
  if ((lbl === null) || (lbl == "")) {return;}

  // Default: a callout in the middle of Region 3, CPU 0
  // [ 56.50190483, 0.00000001, 0, 0, 0, -4, 10, -0.2, 0, "label"]
  var newcallout = [(state2.xleft_ts + state2.xright_ts) / 2, 0.00000001, 0, -1, -1, -4, 10, -0.4, 0, lbl];

  if (state2.annotated_one_d != -1) {
    var d = data.events[state2.annotated_one_d];
    if (startsonscreen_x(d)) {
      setPosition(-1, d, newcallout);
    }
  }

  // Append to extra array
  data.extra.push(newcallout);
  overlaydraw(data.extra[data.extra.length - 1], false);
//console.log(data.extra);
}

// Add a view button with underlying savedview[n] at current view
function doAddView(e) {
  var lbl = prompt("New view name: (no spaces)", "fig1-1");
  if ((lbl === null) || (lbl == "")) {return;}
  // Append to savedview array
  var nextViewNumber = data.savedview.length;
  data.savedview.push([lbl, packupState()]);
//console.log(data.savedview);

  // Insert new view button
  var element = document.getElementById("saveplus");
  var newElement = "<span id=\"view" + nextViewNumber + "\" style=\"border-radius:6px\";>" + lbl + "&nbsp;&nbsp;</span>";
  element.insertAdjacentHTML('beforebegin', newElement);

  var docElement = document.getElementById("view" + nextViewNumber);
//console.log("docElement", docElement);
//console.log("docElement.id", docElement.id);
//console.log("docElement.id#", docElement.id.substr(4));

  docElement.onclick = doToggleView;
  decorateSave(docElement, data.savedview[nextViewNumber][1] != null);
}

// Pick subscript number out of clicked span id and save/restore there
function doToggleView(e) {
//console.log("doToggleView", e.currentTarget);
  var viewnum = e.currentTarget.id.substr(4);
  doSaveRestore(e.shiftKey, viewnum);
}


function doToggleMore(e) {
  more_text = !more_text;
  if (more_text) {
    document.getElementById("moretoggle").innerHTML = "[less]";
    document.getElementById("moretext").style.display = "";
    // Scroll down a bit so that the user can see the new text
    window.scrollBy(0, 50);
  } else {
    document.getElementById("moretoggle").innerHTML = "[more]";
    document.getElementById("moretext").style.display = "none";
  }
}



function fillOuterSvg() {
  // Region 1
  // Add the graph title centered over region3
  // 0.8 is approximation that shifts up to allow descenders
  outersvg.append("text") 
      .attr("id", "titletext") 
      .attr("class", "title-text noselect")
      .attr("transform", "translate(" + (region2width + region3width/2) + ", " + 
                                    (0.9 * region1height) + ")")
      .attr("font-size", textsize.title + "px")
      .attr("font-family", "serif")
      .attr("font-weight", "bold")
      .style("text-anchor", "middle")
      .style("fill", "#0000AA")		// medium dark blue
      .text(data.title);

  var xaxisHeight = region5height / 2;

  // Region 5
  // Add the X-axis basetime 
  outersvg.append("text") 
      .attr("id", "basetimetext")            
      .attr("transform", "translate(" + (region2widthshort) + ", " +
            (svgHeight - 1.2 * xaxisHeight) + ")")
      .attr("width", region2widthshort)
      .attr("font-size", textsize.tick + "px")
      .attr("font-family", "sans-serif")
      .attr("class", "noselect")
      .style("text-anchor", "end")
      .style("fill", "blue")
      .text("");				// placeholder

  // X-axis label
  outersvg.append("text")  
      .attr("id", "xaxislabel")           
      .attr("transform", "translate(" + (region2width + region3width/2) + ", " +
            (svgHeight - 0.2 * xaxisHeight) + ")")
      .attr("font-size", textsize.label + "px")
      .attr("font-family", "serif")
      .attr("font-weight", "bold")
      .attr("class", "title-text noselect")
      .style("text-anchor", "middle")
      .style("fill", "#0000AA")		// medium dark blue
      //.text(data.axisLabelX);		
      .text("");				// placeholder		


  // Set listeners for text areas
  document.getElementById("basetimetext").addEventListener("click", doToggleBasetimetext);
  //document.getElementById("save0text").addEventListener("click", doToggleState0);
  //document.getElementById("save1text").addEventListener("click", doToggleState1);
  //document.getElementById("save2text").addEventListener("click", doToggleState2);
  //document.getElementById("save3text").addEventListener("click", doToggleState3);
  //document.getElementById("save4text").addEventListener("click", doToggleState4);
  document.getElementById("saveplus").addEventListener("click", doAddView);

  document.getElementById("moretoggle").addEventListener("click", doToggleMore);

  // If we use shift-rightclick, prevent it popping up the default context menu
  document.oncontextmenu = function(event) {
    if (event.defaultPrevented) return;
    event.preventDefault();
    //alert("Document context menu");
  };


}



// row is sortNum subscript
function getGroup(row) {
  if (row < sortNum_pid_start)      {return prefixCpu;}
  else if (row < sortNum_rpc_start) {return prefixPid;}
  else if (row < sortNum_res_start) {return prefixRpc;}
  else                              {return prefixRes;}
}

// row is sortNum subscript
function getGroupcompress(row) {
  return state2.groupcompress[getGroup(row)];
}


// We want group labels always to be high enough that the next label does not run into them.
// BUT we are updating tracks before we know max track, but we need maxTrack to calculate 
// group-label height in tracks.
// We break this loop by calculating initial total tracks without this adjustment
// and use that to assign group label sizes. Not perfect but close enough. 

function AdjustHeight(sortnum, rowheight, group_tracks) {
  if (sortnum < 0) {
    // Group label. We want its height to be at least the number of tracks that cover fontsize
    return Math.max(rowheight, group_tracks);
  }
  return rowheight;
}

// Assign heights for each sortNum, and then which tracks they occupy:
// Height is 10 if row is a group label
// Height is 0 if group is not expanded
// Height is 0 if row is auto-pruned because it's not on screen
// Height is maxTrackHeight if row has highlighted events 
// Height is 0/0/5/20 based on group compress 
//
function updateTracks() {
  for (var row = 0; row < sortNum.length; ++row) {
    var groupcmpr = getGroupcompress(row);
//console.log("updateTracks row compress", row, groupcmpr, grayHeight[groupcmpr], 
//rowHilite(row));
    if (sortNum[row] < 0) {
      rowToHeight[row] = maxTrackHeight / 2;  	// group labels are short
    } else if (groupcmpr == 0) {
      rowToHeight[row] = 0;			// Group is not expanded
    } else if (rowFullsize(row)) {
      rowToHeight[row] = maxTrackHeight;	// Row has highlighted events
    } else if (!rangeonscreen_x(rowToMinT[row], rowToMaxT[row])) {
      rowToHeight[row] = 0;			// Auto-prune if not onscreen
    } else {
      rowToHeight[row] = grayHeight[groupcmpr];	// non-highlighted
    }
  }

  // We keep N+1 rows, to make it easy to have the first-unused track number
  rowToHeight[sortNum.length] = 0;

  // Given all the row heights in 0..20 tracks, now assign cumlative track numbers
  // Pass 1 without group label height adjustment
  var pass1_total_tracks = 1;
  var pass1_groups = 0;
  for (var row = 0; row < sortNum.length; ++row) {
    pass1_total_tracks += rowToHeight[row];
  }
  // We know the initial total tracks and we know region234height in px
  // we need to know the fontsize used for group labels textsize.smalltick +/-
  var pxpertrack = region234height / pass1_total_tracks;
  // We also need to take the current zoom into account
  var group_tracks = Rnd0((textsize.smalltick / pxpertrack) / state2.savedtransformY.k); 
//console.log("total_tracks, pxpertrack, group_tracks", pass1_total_tracks, pxpertrack, group_tracks);

  // Pass 2
  var prefix = 0;
  rowToTrack[0] = 0;
  for (var row = 1; row <= sortNum.length; ++row) {	// Note <=
    var height_tracks = AdjustHeight(sortNum[row - 1], rowToHeight[row - 1], group_tracks);
    rowToTrack[row] = rowToTrack[row - 1] + height_tracks;
    // Remember where each group label starts
    if (sortNum[row] < 0) {
//console.log(sortName[row], "at", rowToTrack[row]); 
      prefix = -sortNum[row] - 1;
      state2.groupfirsttrack[prefix] = rowToTrack[row];
      // We want the next row to be textheight below this group label, but
      // we cannot just make rowToHeight bigger than maxTrackHeight because
      // it is used as a subscript. So we fix it here.
    }
  }
  // First unused track is rowToTrack[sortNum.length]
  maxTrack = rowToTrack[sortNum.length];

//console.log("maxTrack", maxTrack);
//console.log("rowToHeight", rowToHeight);
//console.log("rowToTrack", rowToTrack);

  // NO. y is set up a bit later. Remap the Y-axis to reflect the current maxTrack
  //y.domain([0, maxTrack]);
}

// Split the incoming traceable string into the day part (as a string)
// and the time part (as numeric seconds). Then we can do arithmetic 
// on the seconds part to offset the base time of the X-axis.
//   "tracebase" : "2016-11-19_10:04:00"
//                  dddddddddd tttttttt
function splitTracebase() {
  dataBaseDate = data.tracebase.substr(0,10);
  dataBaseSecond = 
    (+data.tracebase.substr(11,2) * 3600) +
    (+data.tracebase.substr(14,2) * 60) +
    (+data.tracebase.substr(17,2));
//console.log(dataBaseDate, dataBaseSecond, hhmmss(dataBaseSecond + 0.123), "06.3f");
}

function low16of(x) {
  return x & 0xFFFF;
}

// True for rowsumm and allsumm files with presorted rows and not-sorted timestamps
function RowsArePresorted() {
  if (typeof data.presorted === 'undefined') {return false;}
  return data.presorted; 
}

// Pick out all the unique CPU, PID. RPCID, and Resource values with their names
function getDataMetadata(data) {
  // First pass over all the data
  let uniqCpu = new Map();
  let uniqPid = new Map();
  let uniqRpc = new Map();
  let uniqRes = new Map();
  // Ignore all -1 items
  data.events.forEach(function(d) {
    var cpu16 = low16of(cpu(d));
    var pid16 = low16of(pid(d));
    var rpc16 = low16of(rpc(d));
    if (is_rpcdef(d)) {
      rpc16 = low16of(arg0(d));	// Just for REQ/RESP/MID rpcid
    }
    var res16 = low16of(res(d));

    // Captures name at first use. Without !has test, would get last use
    // All events go into CPU set
    if ((0 <= cpu(d)) && !uniqCpu.has(cpu16)) {
      if (RowsArePresorted()) {
        uniqCpu.set(cpu16, name(d));
      } else {
        uniqCpu.set(cpu16, cpu(d).toString());
      }

    }
    // Only user-mode PID names and left-right marks go into PID set
    // Ignore pid=0 (idle job) 
    if ((0 < pid(d)) && !uniqPid.has(pid16) && (is_user(d) || is_lr_mark(d))) {
      uniqPid.set(pid16, name(d));
//console.log("PID=", pid16, name(d), ts(d));
    }
    // Only RPC req/resp or left-right marks go into RPC set
    // Ignore rpc=0 (no RPC active)
    // Ignore switching back to rpc=0 with name ".0" 2021.02.05
    // Keep longest name
    if (is_rpcdef(d) || is_lr_mark(d)) {
      // rpcid is in arg0 for rpcdef
      var use_name = true;
      if ((rpc(d) <= 0) && (arg0(d) == 0)) {use_name = false;}	// skip rpc=-1, arg0=0
      if ((name(d) == ".0")) {use_name = false;}
      if (uniqRpc.has(rpc16) && (uniqRpc.get(rpc16).length >= name(d).length)) {use_name = false;}
      if (use_name) {uniqRpc.set(rpc16, name(d));}
    }
    // No resources implemented at the moment (maybe never)
    if ((0 < res(d)) && !uniqRes.has(res16)) {
      uniqRes.set(res16, name(d));
    }
  });

// Sets are in order of insertion. 
// This is what we want for RPC IDs, but not the others, which we want 
//   ascending numerically. We will sort those below
//console.log("metadata Cpu", uniqCpu);
//console.log("metadata Pid", uniqPid);
//console.log("metadata Rpc", uniqRpc);
//console.log("metadata Res", uniqRes);
  var sortCpu = [...uniqCpu];	//[ [k1,v1], [k2,v2], ... ]
  var sortPid = [...uniqPid];
  var sortRpc = [...uniqRpc];
  var sortRes = [...uniqRes];

  // If not presorted, sort CPU by number, sort PID by number, leave RPC unchanged
  if (!RowsArePresorted()) {
    sortCpu.sort(function(a, b){return a[0]-b[0]});
    sortPid.sort(function(a, b){return a[0]-b[0]});
    // Leave sortRpc alone -- already in order of occurrence
    sortRes.sort(function(a, b){return a[0]-b[0]});
  }

//console.log("metadata Cpu", sortCpu);
//console.log("metadata Pid", sortPid);
//console.log("metadata Rpc", sortRpc);
//console.log("metadata Res", sortRes);

  var sortCpuNum = sortCpu.map(function(v){ return v[0] });
  var sortCpuName = sortCpu.map(function(v){ return v[1] });

  var sortPidNum = sortPid.map(function(v){ return v[0] });
  var sortPidName = sortPid.map(function(v){ return v[1] });

  var sortRpcNum = sortRpc.map(function(v){ return v[0] });
  var sortRpcName = sortRpc.map(function(v){ return v[1] });

  var sortResNum = sortRes.map(function(v){ return v[0] });
  var sortResName = sortRes.map(function(v){ return v[1] });

//console.log(sortPidNum);
//console.log(sortPidName);

  // Build sortNum list of every row we can ever display
  // Negative values are rows for group names
  // If the size of a group is zero, delete it entirely
  sortNum = [];
  if (sortCpuNum.length > 0) {sortNum = sortNum.concat([-prefixCpu-1, ...sortCpuNum]);}
  if (sortPidNum.length > 0) {sortNum = sortNum.concat([-prefixPid-1, ...sortPidNum]);}
  if (sortRpcNum.length > 0) {sortNum = sortNum.concat([-prefixRpc-1, ...sortRpcNum]);}
  if (sortResNum.length > 0) {sortNum = sortNum.concat([-prefixRes-1, ...sortResNum]);}

  //sortNum = [-prefixCpu-1, ...sortCpuNum,
  //               -prefixPid-1, ...sortPidNum,
  //               -prefixRpc-1, ...sortRpcNum,
  //               -prefixRes-1, ...sortResNum];
//console.log("sortNum ", sortNum);
  sortNumSize = sortNum.length;


  // Parallel array of names for Y-axis labels. 
  // We only separate these to make RowToTrack[EventToRow[prefix + f(d)]] fast
  // If the size of a group is zero, delete it entirely
  // MAYBE separation is not needed
  sortName = [];
  if (sortCpuName.length > 0) {sortName = sortName.concat(["CPU", ...sortCpuName]);}
  if (sortPidName.length > 0) {sortName = sortName.concat(["PID", ...sortPidName]);}
  if (sortRpcName.length > 0) {sortName = sortName.concat(["RPC", ...sortRpcName]);}
  if (sortResName.length > 0) {sortName = sortName.concat(["RES", ...sortResName]);}

  // sortName = ["CPU", ...sortCpuName,
  //             "PID", ...sortPidName,
  //             "RPC", ...sortRpcName,
  //             "RES", ...sortResName];
//console.log("sortName ", sortName);

  // NEXT: make one 256K-entry array mapping (prefixCpu * 65536) + low16of(cpu), etc. 
  //   to sortNum subscript 
  eventToRow = new Array(256 * 1024).fill(-1);

  // Group names at sortNum[0] and three later, so the +1's
  sortNum_cpu_start = 1;
  sortNum_pid_start = 1 + sortCpu.length + 1;
  sortNum_rpc_start = 1 + sortCpu.length + 1 + sortPid.length + 1;
  sortNum_res_start = 1 + sortCpu.length + 1 + sortPid.length + 1 + sortRpc.length + 1;
  state2.grouplength[prefixCpu] = sortCpu.length;
  state2.grouplength[prefixPid] = sortPid.length;
  state2.grouplength[prefixRpc] = sortRpc.length;
  state2.grouplength[prefixRes] = sortRes.length;

  sortCpuNum.forEach(function(d, i) {
    eventToRow[prefixCpu * 65536 + d] = sortNum_cpu_start + i;});
  sortPidNum.forEach(function(d, i) {
    eventToRow[prefixPid * 65536 + d] = sortNum_pid_start + i;});
  sortRpcNum.forEach(function(d, i) {
    eventToRow[prefixRpc * 65536 + d] = sortNum_rpc_start + i;});
  sortResNum.forEach(function(d, i) {
    eventToRow[prefixRes * 65536 + d] = sortNum_res_start + i;});

//console.log("eventToRow ", eventToRow);

  // Next, find start/end range for each sortNum
  // Second pass over all the data
  rowToMinT = new Array(sortNum.length).fill(999);
  rowToMaxT = new Array(sortNum.length).fill(-1);

  // Also capture initial X-axis bounds
  dataTsLo = 999;
  dataTsHi = 0;
  var row;
  data.events.forEach(function(d) {
    if (0 <= cpu(d)) {
      row  = eventToRow[prefixCpu * 65536 + low16of(cpu(d))];
      if (row >= 0) {
        rowToMinT[row] = Math.min(rowToMinT[row], ts(d));
        rowToMaxT[row] = Math.max(rowToMaxT[row], tsend(d));
        dataTsLo =  Math.min(dataTsLo, ts(d));
        dataTsHi = Math.max(dataTsHi, tsend(d));
      }
    }
    if (0 < pid(d)) {		// Do not draw pid=0, idle
      row  = eventToRow[prefixPid * 65536 +low16of(pid(d))];
      if (row >= 0) {
        rowToMinT[row] = Math.min(rowToMinT[row], ts(d));
        rowToMaxT[row] = Math.max(rowToMaxT[row], tsend(d));
      }
    }
    if (0 < rpc(d)) {		// Do not draw rpc=0, not inside an RPC
      row  = eventToRow[prefixRpc * 65536 +low16of(rpc(d))];
      if (row >= 0) {
        rowToMinT[row] = Math.min(rowToMinT[row], ts(d));
        rowToMaxT[row] = Math.max(rowToMaxT[row], tsend(d));
      }
    }
    //if (0 <= res(d)) {...}	// Later: locks, disks, networks
  });

  // Initial X-axis bounds just after data load, before any pan/zoom
  realxleft = dataTsLo;
  realxright = dataTsHi;
  state2.xleft_ts = dataTsLo;
  state2.xright_ts = dataTsHi;
  realxrightmost = dataTsHi;	  // includes the margin.right padding
  // Update x start and width text
  document.getElementById("xstartwidth").innerHTML =  
      (RndUsec(state2.xleft_ts)) + "+" + (RndUsec(state2.xright_ts - state2.xleft_ts) * 1000000);

//console.log("rowToMinT ", rowToMinT);
//console.log("rowToMaxT ", rowToMaxT);
//console.log("X: ", realxleft, "..", realxright);

  // Initialize rowToHighlighted
  state2.rowToHilite = new Array(sortNum.length).fill(false);

  // Now fill in rowToHeight  and rowToTrack 
  rowToHeight = new Array(sortNum.length).fill(10);
  rowToTrack  = new Array(sortNum.length).fill(0);
  // No update of y.domain here -- not created yet
  updateTracks();

//console.log("rowToHeight ", rowToHeight);
//console.log("rowToTrack ", rowToTrack);

  // Pick off trace IDs
  state.traceid =  data.tracebase;
  state.randomid = data.randomid || 0;
  if (typeof(data.randomid) !== 'undefined') {
    localStorageKey = data.randomid;
  } else {
    localStorageKey = data.tracebase;	// For old files
  }
console.log("localStorageKey", localStorageKey);

  // Pick off date and time
  splitTracebase();
}

/***
Sets in state:
  traceid
  randomid

Sets in state2:

Sets outside of state:
  dataBaseDate
  dataBaseSecond
***/


// Use new data
function newdata2(data2) {
  //console.log("newdata2:", data2);
  initializeState();	// With new data, start entirely clean on state
 
  // format the data
  var has_pc_samples = false;
  var has_locks = false;
  var has_freq_varies = false;
  freq_min = 99999999;
  freq_max = 0;
  data2.events.forEach(function(d) {
      d[0] = +d[0];	// start_ts
      d[1] = +d[1];	// duration
      d[2] = +d[2];	// cpu
      d[3] = +d[3];	// pid
      d[4] = +d[4];	// rpc
      d[5] = +d[5];	// event
      d[6] = +d[6];	// arg0
      d[7] = +d[7];	// ret
      d[8] = +d[8];	// ipc
 
      // Track what the cat dragged in
      has_pc_samples |= is_pc_samp(d); 
      has_locks |= is_lock_line(d); 
      if (is_pstate(d)) {
        // Fixup old files, from before standardizing on MHz units
        if (d[6] < 100) {
          // Assume it is in units of 100 MHz; convert to MHz
          d[6] *= 100;
        } else if (100000 < arg0(d)) {
          // Assume it is in units of KHz; convert to MHz
          d[6] /= 1000;
        }
        freq_min = Math.min(freq_min, arg0(d));
        freq_max = Math.max(freq_max, arg0(d));
      }
  });
 
  data = data2;
  // Remove the 999.0 marker at the end
  data.events.pop();
  
  // Save space by deleting data2 content
  data2 = [];

  // If no views to restore, create the default ones
//console.log(data.savedview);
  if (typeof data.savedview === 'undefined') {
    createSavedview();
  }

  // If any views were in the incoming json, they need to be converted back to strings
  for (var i = 0; i < data.savedview.length; ++i) {
//console.log("data.savedview[i][1] type", typeof data.savedview[i][1]);
    //Convert an incoming non-null object to a string
    if (data.savedview[i][1] == null) {continue;}
    if (typeof data.savedview[i][1] !== 'string') {
      data.savedview[i][1] = stringifyState(data.savedview[i][1]);
    }
  }
  // Populate HTML views
  populateSavedviewHtml();

  // If no extra array, make one
  if (typeof data.extra === 'undefined') {
    data.extra = [];
  }

  // Grab the IPC and LLC flags
  has_ipc = (data.flags & 128) != 0;
  has_llc = (data.flags & 32) != 0;
  has_both = has_ipc & has_llc;

  // ipc_value=-1 means no IPC or LLC data available
  state2.ipc_value = 0;
  if (!has_ipc & !has_llc) {state2.ipc_value = -1;}

  // Set the IPC/LLC button text
  document.getElementById("showipc").textContent = "IPC"
  if (!has_ipc & has_llc) {document.getElementById("showipc").textContent = "LLC";}
  if (has_ipc & has_llc) {document.getElementById("showipc").textContent = "Both";}
  
  // Remember if this is from a high-overhead RISC-V CPU
  is_riscv = false;
  if ((typeof data.cpuModelName !== 'undefined') && 
      (data.cpuModelName.search("u74-mc") >= 0)) {
    is_riscv = true;
    gOverheadNs    = kRVOverheadNs;	  // Approximate KUtrace overhead for one event
    gOverheadIpcNs = kRVOverheadIpcNs;// Approximate KUtrace overhead for one event with IPC recording
    gMinShowOvhd   = kRVMinShowOvhd;  // Always show overhead if width lt this (seconds)
  }
 
  // Set the flags; *_value=-1 means no items available
  if (!has_pc_samples) {state2.samp_value = -1;}
  if (!has_locks) {state2.lock_value = -1;}

  // Set the freq flag, and mapping
  freq_range = freq_max - freq_min;
  freq_divisor =  Math.floor(freq_range / 8);
  // Could be none or 800..800, but usually more like 800..3900
  // Testing for 9=< instead of 0=< avoids zdiv in degenerate cases
  has_freq_varies = (9 <= freq_range);	
  if (!has_freq_varies) {state2.freq_value = -1;}
//console.log("freq_min/max", freq_min, freq_max);
//console.log("freq_divisor", freq_divisor);
 
  // Get the side arrays
  getDataMetadata(data);
};

function newdata2_resize(data2) {
  if (data2 == null) {
    data2 = kDummyData;
  }
  newdata2(data2);
  resizeWindowEtc();
}
 
// Want at least one character for active searchng
function IsActiveText(t) {
  return t.length >= 1;
}

// Want nonzero for active searchng
function IsActiveNumber(n) {
  return +n >= 1;
}


// Input must be h:v three characters only
function keyupAspect(e) {
  // Setting aspect ratio
  var inputTextValue = e.target.value;
  if (inputTextValue.length != 3) {return;}
  if (inputTextValue[1] != ":") {return;}

  var temph = +inputTextValue[0];
  var tempv = +inputTextValue[2];
  if (!((0 <= temph) && (temph <= 9)) || !((0 <= tempv) && (tempv <= 9))) {return;}
  if ((temph == 0) || (tempv == 0)) {tempv = temph = 0;}
  aspecth = temph
  aspectv = tempv;
  DoSave(0);
  allocOuterSvgEtc();
  DoRestore(0);

  return;
}

function removeShowSearchList() {
  var ssl = document.getElementById("ShowSearchList");
  if ((typeof ssl !== 'undefined') && (ssl !== null)) {
    ssl.parentNode.removeChild(ssl);
  }
}

function removeFromList(str) {
  for (i = 0; i < searchlist.length; ++i) {
    if (searchlist[i] == str) {
      // Move up later elements and shrink
      for (j = i+1; j < searchlist.length; ++j) {searchlist[j-1] = searchlist[j];}
      searchlist.length -= 1;
      break;
    }
  }
  // Not found -- no change
}

function doSearch(inputTextValue) {
  // Turn off annotateall/user when search is active
  state2.annotateall_value = 0;
  state2.annotateuser_value = 0;

  annotatesearch2(document.getElementById("SearchText").value, 
                  document.getElementById("SearchMin").value,
                  document.getElementById("SearchMax").value);
}

function frontOfList(str) {
  searchlist.unshift(str);
}

// Add/move string to front of searchlist
function updateSearchList(str) {
  if (str.length == 0) {return;}
  removeFromList(str);
  frontOfList(str);
  if (searchlistMax < searchlist.length) {
    searchlist.length = searchlistMax;
  }
//console.log(searchlist);
}

// Set search box to selected ShowSearchList element, update list, do search
function searchlistClick(e) {
  var str = e.target.innerText;
  document.getElementById("SearchText").value = str;
  state2.search_text = str;
  updateSearchList(str);
  removeShowSearchList();
  doSearch(str);
}


// Maintain a list of recent search strings, displaying while typing search text
function maintainSearchList(keycode) {
  var str = document.getElementById("SearchText").value;
  // Remove prompts
  removeShowSearchList();
  if (str.length == 0) {
    // Empty search box
    return;
  }
  // <CR> adds search box contents to the list
  if (keycode == CR) {
    // Add/move this entry to front of searchlist
    updateSearchList(str);
  } else {
    // Recreate prompt list and show it
    var box = document.getElementById("SearchTable");
    ssl = document.createElement("div");
    ssl.setAttribute("id", "ShowSearchList");
    ssl.setAttribute("style", "position:absolute; left:2px; width:150px; top:125%; z-index:1;");
//console.log("append1", box, ssl);
    box.appendChild(ssl);
    var dispcount = 0;
    for (i = 0; i < searchlist.length; ++i) {
      if (0 <= searchlist[i].indexOf(str)) {
        var b = document.createElement("div");
        b.innerHTML = searchlist[i];
        //b.setAttribute("value", i.toString());
        //b.setAttribute("style", "background-color:#EEEEFF; width:100%;");
        b.setAttribute("style", "background:#EEEEFF; width:100%;");
        b.setAttribute("style", "font-family:sans-serif;");
        b.addEventListener("click", searchlistClick);
//console.log("append2", box, a);
        ssl.appendChild(b);
        ++dispcount;
        if (searchlistMaxDisplay <= dispcount) {break;}
      }
    }
  }

}

function keyupSearch(e) {
  var keycode = e.keyCode;
  // Maintain a list of recent search strings
  maintainSearchList(keycode);

  // Setting the input text to the global Javascript Variable for every key press
  var inputTextValue = e.target.value;
  state2.search_text = inputTextValue;
  if (IsActiveText(inputTextValue)) {e.target.style.backgroundColor = kActiveBackColor;}
  else                              {e.target.style.backgroundColor = kInactiveBackColor;}
  doSearch(inputTextValue);
}

function keyupSearchMin(e) {
  // Setting your input text to the global Javascript Variable for every key press
  var inputTextValue = e.target.value;
  state2.usec_lo_time = +inputTextValue;

  // Turn off annotateall/user when search is active
  state2.annotateall_value = 0;
  state2.annotateuser_value = 0;

  if (IsActiveNumber(inputTextValue)) {e.target.style.backgroundColor = kActiveBackColor;}
  else                                {e.target.style.backgroundColor = kInactiveBackColor;}
 
  annotatesearch2(document.getElementById("SearchText").value, 
                  document.getElementById("SearchMin").value,
                  document.getElementById("SearchMax").value);
  return;
}

function keyupSearchMax(e) {
  // Setting your input text to the global Javascript Variable for every key press
  var inputTextValue = e.target.value;
  state2.usec_hi_time = +inputTextValue;

  // Turn off annotateall/user when search is active
  state2.annotateall_value = 0;
  state2.annotateuser_value = 0;

  if (IsActiveNumber(inputTextValue)) {e.target.style.backgroundColor = kActiveBackColor;}
  else                                {e.target.style.backgroundColor = kInactiveBackColor;}
 
  annotatesearch2(document.getElementById("SearchText").value, 
                  document.getElementById("SearchMin").value,
                  document.getElementById("SearchMax").value);
  return;
}


// Manually set the number of characters in Y-axis labels 
function keyupDebugRegion2Chars(e) {
  var inputTextValue = +e.target.value;
  if (inputTextValue <= 4) {return;}
  textsize.region2chars = inputTextValue;
  DoSave(0);
  allocOuterSvgEtc();
  DoRestore(0);
}

// Manually set the number of pixels high per character in Y-axis labels
function keyupDebugRegion2Px(e) {
  var pix = +e.target.value;
  if (pix <= 4) {return;}
  textsize.region2px = pix;
  DoSave(0);
  allocOuterSvgEtc();
  DoRestore(0);
}

// For the next two, row space will be apportioned as spn / (spn + txt) and
//  txt / (spn + txt) rspectively, giving txt lines of annotations
//  where total row height = basictextsize * (spn + txt)

// Manually set how many sub-rows for time spans
// Row space will be apportioned as spn / (spn + txt)
function keyupDebugSpn(e) {
  var inputTextValue = +e.target.value;
  if (inputTextValue <= 0) {return;}
  textsize.spn = inputTextValue;
  calcPixHeightsPerTrack(ypixpertrack);

  redrawEventsEtc();
  redrawXaxis();
  redrawYlabels();
}

// Manually set how many sub-rows for annotation text
function keyupDebugTxt(e) {
  var inputTextValue = Math.max(0, +e.target.value);
  if (inputTextValue < 0) {return;}  // Allow zero. If so, have minimum span
  textsize.txt = inputTextValue;
  calcPixHeightsPerTrack(ypixpertrack);

  redrawEventsEtc();
  redrawXaxis();
  redrawYlabels();
}



 
// Main JavaScript program
// Wait for a file name keyup = CR

<!-- selfcontained2 -->

// ---------- OVerall HTML legend ----------------------------------------
// Constants for overall legend at 150px per inch
const OVconstH = {
  width : 900,
  height : 600,
  tab_incr : 35,
  cr_incr : 20,
  col_incr : 300,
  col_margin : 10,
  bigtextsize : 30,
  medtextsize : 20,
  textsize : 13,
  kheight : 30,
  uheight : 15,
  tinywidth : 5,
  tiny2width : 10,
  narrowwidth : 15,
  normalwidth : 30,
  widewidth : 60, 
  exwidewidth : 75, 

  dummy : 0	// No comma here
};

var OVconst;	//  modified

// These change as we draw the overall legend
var OVdata = {
  xpos : 0,
  ypos : 0,
  save_xpos : 0,
  save_ypos : 0,

  lmargin : 0 ,

  dummy : 0	// No comma here
};

// Top left corner
function OVreset() {
  OVdata.xpos = 0;
  OVdata.ypos = 0;
  OVdata.save_xpos = 0;
  OVdata.save_ypos = 0;
  OVdata.lmargin = 0;
}

// Horizontal
function OVresetH() {
  OVconst = OVconstH;
  OVconst.width = 900;
  OVconst.height = 600;
  OVconst.col_incr = 300;
  OVreset();
}

// Vertical 
function OVresetV() {
  OVconst = OVconstH;
  OVconst.width = 650;
  OVconst.height = 900;
  OVconst.col_incr = 350;
  OVreset();
}

function OVsavex()    {OVdata.save_xpos = OVdata.xpos;}
function OVrestorex() {OVdata.xpos = OVdata.save_xpos;}
function OVsavey()    {OVdata.save_ypos = OVdata.ypos;}
function OVrestorey() {OVdata.ypos = OVdata.save_ypos;}

function OVcrlf(n) {
  OVdata.xpos = OVdata.lmargin;
  OVdata.ypos += n * OVconst.cr_incr;
}
function OVcrtext(n) {
  OVdata.xpos = OVdata.lmargin;
  OVdata.ypos += n * OVconst.textsize;
}

function OVcr() {OVdata.xpos = OVdata.lmargin;}

function OVgap(w) {OVdata.xpos += w;}

function OVsetcol(n) {
  OVdata.lmargin = OVconst.col_margin + n * OVconst.col_incr;
  OVdata.xpos = OVdata.lmargin
  OVdata.ypos = 0;
}

// Tab multiples are from lmargin, not zero
function OVtab(n) {
  OVdata.xpos -= (OVdata.xpos - OVdata.lmargin) % OVconst.tab_incr;
  OVdata.xpos += n * OVconst.tab_incr;
}

function OVbigtext(s) {
  innersvgdotg.append("text")
      .attr("class", "noselect")
      .attr("xml:space", "preserve")
      .attr("transform", "translate(" + (OVdata.xpos) + "," + (OVdata.ypos) + ")")
      .attr("dy", OVconst.bigtextsize)
      .attr("font-size", OVconst.bigtextsize + "px")
      .attr("fill", "blue")
      .attr("font-family", "sans-serif")
      .attr("font-weight", "bold")
      .style("text-anchor", "middle")
      .text(s);
}

function OVmedtext(s) {
  innersvgdotg.append("text")
      .attr("class", "noselect")
      .attr("xml:space", "preserve")
      .attr("transform", "translate(" + (OVdata.xpos) + "," + (OVdata.ypos) + ")")
      .attr("dy", OVconst.bigtextsize)
      .attr("font-size", OVconst.medtextsize + "px")
      .attr("fill", "black")
      .attr("font-family", "sans-serif")
      .attr("font-weight", "bold")
      .text(s);
}

// Assumes we are cented in a row
function OVtext(line, s) {
  innersvgdotg.append("text")
      .attr("class", "noselect")
      .attr("xml:space", "preserve")
      .attr("transform", "translate(" + (OVdata.xpos) + "," + (OVdata.ypos - OVconst.cr_incr + line * OVconst.textsize) + ")")
      .attr("dy", OVconst.textsize)
      .attr("font-size", OVconst.textsize + "px")
      .attr("fill", "black")
      .attr("font-family", "sans-serif")
      .text(s);
}

function OVcircle(r) {
  innersvgdotg.append("circle")
       .attr("cx", OVdata.xpos + r)
       .attr("cy", OVdata.ypos)
       .attr("r", r)
       .attr("fill", "gray");
}

function OVaxes(widthpx, height, text) {
  var path = "M" + Rnd1(OVdata.xpos) + "," + Rnd1( OVdata.ypos) + 
    " v" + (height) + " h" + (widthpx);
  innersvgdotg.append("path")
    .attr("d",  path)
    .attr("fill", "none")
    .attr("stroke-width", 1)
    .attr("stroke", "#000000");	// black
  innersvgdotg.append("text")
      .attr("class", "noselect")
      .attr("transform", "translate(" + (OVdata.xpos) + "," + (OVdata.ypos + height) + ")")
      .attr("dy", OVconst.textsize)
      .attr("xml:space", "preserve")
      .attr("font-size", OVconst.textsize + "px")
      .attr("fill", "black")
      .attr("font-family", "sans-serif")
      .style("text-anchor", "middle")
      .text(text);
}

function OVline(widthpx, height, color, text) {
  var path = "M" + Rnd1(OVdata.xpos + widthpx) + "," + Rnd1( OVdata.ypos) + 
    " v" + (height);
  innersvgdotg.append("path")
    .attr("d",  path)
    .attr("fill", "none")
    .attr("stroke-width", 1)
    .attr("stroke", color);
  innersvgdotg.append("text")
      .attr("class", "noselect")
      .attr("transform", "translate(" + (OVdata.xpos + widthpx) + "," + (OVdata.ypos + height) + ")")
      .attr("dy", OVconst.textsize)
      .attr("xml:space", "preserve")
      .attr("font-size", OVconst.textsize + "px")
      .attr("fill", "black")
      .attr("font-family", "sans-serif")
      .style("text-anchor", "middle")
      .text(text);
}

// Updates xpos
function OVevent(widthpx, evnum, arg, ipc, name) {  
  // [ts, dur, cpu, pid, rpc, ev, arg0, ret, ipc, name]
  var d = [0, 1, -1, 0, 0,   evnum, arg, 0, ipc, name];
  var x0 = OVdata.xpos;
  var y0 = OVdata.ypos;
  var x1 = OVdata.xpos + widthpx;
  var height = OVconst.kheight;
//console.log("OV", d);
  if (is_user(d)) {
    // xx(d, x0, y0, x1, gray, e_height)
    draw_user(d, x0, y0, x1, false, height);
    var oldfade = fade_value; fade_value = 0;
      draw_one_ipcllc(d, x0 + (widthpx/2), y0, height * 0.67);
    fade_value = oldfade;
  } else if (is_kernel(d) && !is_ovhd(d) && !is_rxtx(d)) {
    draw_kernel(d, x0, y0, x1, false, height);
    var oldfade = fade_value; fade_value = 0;
      draw_one_ipcllc(d, x0 + (widthpx/2), y0, height * 0.67);
    fade_value = oldfade;
  } else if (is_idle(d)) {
    draw_idle(d, x0, y0, x1, false, height);
  } else if (is_arc(d)) {
    state2.arcs_value = arcs_value_max;
    var ypostop = (y0 - height/2);
    draw_arc(d, x0 + arg, ypostop + ipc, x0, ypostop);
    state2.arcs_value = 0;
  } else if (is_wait(d)) {
    state2.waits_value = 1;
    draw_wait(d, x0, y0 - (height/4), x1, false, l1width(d, height) / 2);
    state2.waits_value = 0;
  } else if (is_c_exit(d)) {
    draw_c_exit(d, x0, y0, x1, false, height);
  } else if (is_ovhd(d)) {	// Test before kernel
    draw_ovhd(d, x0, y0, x1, height);
  } else if (is_rpcpkt(d)) {
    d[4] = arg;			// RPCID
    var pktpx = OVconst.normalwidth;
    draw_rx_tx(d, x0, y0 - height, x1, l1width(d, height) / 2, pktpx);
  } else if (is_pc_samp(d)) {
    state2.samp_value = samp_value_max;
    d[2] = 0;	// CPU 0
    d[3] = 1;	// PID 1
    draw_pc_samp(d, x0, y0 - height * 0.55, x1, false, l1width(d, height) / 2);
  } else if (is_lock_line(d)) {
    state2.lock_value = lock_value_max;
    draw_lock_line(d, x0, y0 - height * 0.55, x1, false, l1width(d, height) / 2, 0);
  } else if (is_queued(d)) {
    state2.waits_value = 1;
    draw_queue(d, x0, y0 - height / 4, x1, false, l1width(d, height));
    state2.waits_value = 0;
  } else if (is_pstate(d)) {
    freq_min = 800;
    freq_max = 3900;
    freq_range = 3100;
    freq_divisor =  Math.floor(freq_range / 8);
    state2.freq_value = freq_value_max;
    draw_pstate(d, x0, y0, x1, false, height);
  } else if (is_callout(d)) {
    // Center at x0,y0 and point at x1,y1
    var bub_x1 = x0;
    var bub_y1 = y0 - height / 2;
    var bub_x0 = x1;
    var bub_y0 = bub_y1 - OVconst.cr_incr;
    draw_bubble(d, bub_x0, bub_y0, bub_x1, bub_y1, height / 2);
    return;	// No increment of xpos
  } else if (is_mark(d)) {
    state2.marks_value = marks_value_max;
    var marktextsize = height / 2;
    var skiptext = false;
    var rel_y0 = height / 2;
    draw_mark_abcd(d, x0, y0, rel_y0, name, marktextsize, skiptext);
  } else if (is_annot(d)) {
    draw_one_annot(d, x0, y0 + height,  y0 + height/2, arg - height/2,  y0 - height/2, ipc - height/2,  name, height/2, "");
  } else {
    // more...
  }
  
  OVdata.xpos += widthpx;
}

function OVfade(widthpx, evnum, arg, ipc, name) {
  fade_value = 1;
  OVevent(widthpx, evnum, arg, ipc, name);
  fade_value = 0;
}

function OV1() {
  OVmedtext("Execution (measured)"); OVcrlf(3);

  // Kernel
  OVevent(OVconst.normalwidth, syscallbase+123, 0, 0, ""); OVtab(1);
  OVevent(OVconst.normalwidth, irqbase+123, 0, 0, ""); OVtab(1);
  OVevent(OVconst.normalwidth, faultbase+123, 0, 0, ""); OVtab(1);
  OVevent(OVconst.normalwidth, schedbase, 0, 0, ""); OVtab(1);
  OVtext(0.5, "Kernel syscall 123;");
  OVtext(1.5, "interrupt; fault; scheduler");
  OVcrlf(2);

  // User
  OVfade(OVconst.tiny2width, syscallbase+123, 0, 0, ""); 
  OVevent(OVconst.normalwidth, userbase+1234, 0, 0, ""); 
  OVgap(OVconst.tinywidth); 
  OVevent(OVconst.normalwidth, userbase+1235, 0, 0, "");  
  OVfade(OVconst.tiny2width, syscallbase+123, 0, 0, ""); 
  OVtab(2); OVtext(1, "User process 1234; 1235");
  OVcrlf(2);

  // Idle
  OVfade(OVconst.tiny2width, syscallbase+123, 0, 0, ""); 
  OVevent(OVconst.normalwidth, idlebase, 0, 0, "");  
  OVgap(OVconst.tinywidth); 
  OVevent(OVconst.normalwidth, idlebase, 1, 0, ""); 
  OVfade(OVconst.tiny2width, syscallbase+123, 0, 0, ""); 
  OVtab(2); OVtext(1, "Idle; low-power idle");
  OVcrlf(2);

  // IPC
  state2.ipc_value = ipc_value_max;
  OVfade(OVconst.normalwidth, userbase+1234, 0, 3, "");  
  OVfade(OVconst.normalwidth, syscallbase+123, 0, 8, ""); 
  OVfade(OVconst.normalwidth, userbase+1235, 0, 15, "");  
  OVtab(2); OVtext(1, "IPC 3/8; 1.0; 3.5");
  OVcrlf(2);
  state2.ipc_value = 0;

  // Arc
  force_annot = true;
  OVfade(OVconst.normalwidth, userbase+1234, 0, 0, "");  
  OVfade(OVconst.narrowwidth, syscallbase+123, 0, 0, ""); 
  OVfade(OVconst.normalwidth, userbase+1234, 0, 0, "");  
  OVfade(OVconst.narrowwidth, userbase+1234, 0, 0, "");  
  OVtab(2); OVtext(0.5, "Wakeup"); OVtext(1.5, "  from make-runnable");
  OVcrlf(2);
  OVfade(OVconst.normalwidth, idlebase, 0, 0, "");  
  OVfade(OVconst.narrowwidth, idlebase, 1, 0, ""); 
  OVfade(OVconst.normalwidth, schedbase, 0, 0, ""); 
  OVevent(0, arcbase, -OVconst.normalwidth, -OVconst.cr_incr * 2, ""); 
  OVfade(OVconst.narrowwidth, userbase+5678, 0, 0, ""); 
  OVtab(2); OVtext(1, "  to actually running");
  OVcrlf(1);
  force_annot = false;
}

function OV2() {
  OVmedtext("Non-execution"); OVcrlf(2.5);

  // Waiting
  OVfade(OVconst.tiny2width, syscallbase+123, 0, 0, ""); 
  OVevent(OVconst.exwidewidth, waitbase + 2, 0, 0, ""); 	// c
  OVfade(OVconst.tiny2width, syscallbase+123, 0, 0, ""); 
  OVtab(2); OVtext(1, "Waiting for CPU");
  OVcrlf(2);

  OVsavey();
  OVcrtext(-0.33);
  OVgap(OVconst.tiny2width); 
  OVevent(OVconst.exwidewidth, waitbase + 3, 0, 0, "");		// d 
  OVgap(OVconst.tin2ywidth);
  OVcrtext(1);
  OVgap(OVconst.tiny2width); 
  OVevent(OVconst.exwidewidth, waitbase + 11, 0, 0, ""); 	// l
  OVgap(OVconst.tiny2width); 
  OVcrtext(1);
  OVgap(OVconst.tiny2width); 
  OVevent(OVconst.exwidewidth, waitbase + 12, 0, 0, "");	// m 
  OVgap(OVconst.tiny2width); 
  OVcrtext(1);
  OVrestorey();

  OVtab(4);  
    OVtext(0, "  disk");
    OVtext(1, "  lock");
    OVtext(2, "  memory");
  OVcrlf(2);

  OVsavey();
  OVcrtext(-0.33);
  OVgap(OVconst.tiny2width); 
  OVevent(OVconst.exwidewidth, waitbase + 13, 0, 0, ""); 	// n
  OVgap(OVconst.tiny2width); 
  OVcrtext(1);
  OVgap(OVconst.tiny2width); 
  OVevent(OVconst.exwidewidth, waitbase + 15, 0, 0, "");	// p 
  OVgap(OVconst.tiny2width); 
  OVcrtext(1);
  OVgap(OVconst.tiny2width); 
  OVevent(OVconst.exwidewidth, waitbase + 19, 0, 0, "");	// t 
  OVgap(OVconst.tiny2width); 
  OVcrtext(1);
  OVgap(OVconst.tiny2width); 
  OVevent(OVconst.exwidewidth, queued, 1, 0, "");		// RPC queue 1 
  OVgap(OVconst.tiny2width); 
  OVcrtext(1);
  OVrestorey();

  OVtab(4);  
    OVtext(0, "  network");
    OVtext(1, "  pipe");
    OVtext(2, "  timer");
    OVtext(3, "queued RPC [sw library]");
  OVcrlf(1.5);

  OVmedtext("Synthesized (approximate)"); OVcrlf(3);

  // C-exit
  OVfade(OVconst.normalwidth, idlebase, 1, 0, ""); 
  OVevent(OVconst.widewidth, cexitbase, 0, 0, ""); 
  OVfade(OVconst.tiny2width, syscallbase+123, 0, 0, ""); 
  OVtab(2); OVtext(1, "Exiting low-power idle");
  OVcrlf(2);

  // Overhead
  OVevent(OVconst.normalwidth, userbase+1234, 0, 0, "");  
  OVsavex(); OVevent(OVconst.normalwidth, syscallbase+123, 0, 0, ""); OVrestorex(); 
  OVsavex(); OVevent(OVconst.tiny2width, ovhdbase, 0, 0, ""); OVrestorex();
  OVgap(OVconst.normalwidth);
  OVsavex(); OVevent(OVconst.normalwidth, userbase+1235, 0, 0, ""); OVrestorex(); 
  OVsavex(); OVevent(OVconst.tiny2width, ovhdbase, 0, 0, ""); OVrestorex();
  OVgap(OVconst.normalwidth);

  OVtab(2); OVtext(0.5, "KUtrace overhead: diag."); OVtext(1.5, "  white lines (10-50ns)");
  OVcrlf(2);
}

function OV3() {
  OVmedtext("Time-aligned data"); OVcrlf(3);

  // CPU frequency
  OVsavex();
    OVfade(OVconst.widewidth, userbase+1234, 0, 0, ""); 
    OVfade(OVconst.tinywidth, syscallbase+123, 0, 0, "");  
    OVfade(OVconst.widewidth, userbase+1234, 0, 0, "");  
  OVrestorex(); 

  OVsavex();
    OVevent(OVconst.normalwidth + OVconst.narrowwidth, freqbase, 800, 0, ""); 
    OVevent(OVconst.normalwidth, freqbase, 3000, 0, "");  
    OVevent(OVconst.normalwidth, freqbase, 3900, 0, "");  
  OVrestorex(); 
  OVtab(4); OVtext(0.5, "CPU frequency overlay"); OVtext(1.5, "  slow; medium; normal"); 

  OVcrlf(3);

  // PC samples
  OVsavex();
    OVfade(OVconst.tinywidth, syscallbase+123, 0, 0, "");  
    OVfade(OVconst.widewidth, userbase+1234, 0, 0, ""); 
    OVfade(OVconst.tinywidth, syscallbase+123, 0, 0, "");  
    OVfade(OVconst.widewidth, userbase+1234, 0, 0, ""); 
    OVfade(OVconst.tinywidth, syscallbase+123, 0, 0, "");  
  OVrestorex();

  force_annot = true;
  OVsavex(); 
    OVgap(OVconst.tinywidth);
    OVevent(OVconst.widewidth, pcbase+1, 122, 0, ""); 
    OVgap(OVconst.tinywidth);
    OVevent(OVconst.widewidth, pcbase+0, 5678, 0, ""); 
    OVgap(OVconst.tinywidth);
  OVrestorex();
  force_annot = false;

  OVtab(4); OVtext(0.5, "PC samples kernel; user"); OVtext(1.5, "  sample at peak"); 
  OVcrlf(2);

  // Packets
  OVcrlf(1);	// Space above
  OVsavex();
    OVfade(OVconst.narrowwidth, userbase+1234, 0, 0, ""); 
    OVfade(OVconst.widewidth, userbase+1234, 0, 0, ""); 
    OVfade(OVconst.narrowwidth, syscallbase+123, 0, 0, "");  
    OVfade(OVconst.normalwidth, userbase+1234, 0, 0, ""); 
  OVrestorex(); 

  OVsavex();
    OVgap(OVconst.narrowwidth);
    OVevent(OVconst.widewidth, msgrxbase, 1234, 0, ""); 
    OVgap(OVconst.narrowwidth);
    OVgap(OVconst.tinywidth);  
    OVevent(OVconst.normalwidth, msgtxbase, 5678, 0, ""); 
  OVrestorex();
  OVtab(4); OVtext(0, "RPC msg Rx; Tx"); 
    OVtext(1, "  approx. packets shown");
    OVtext(2, "  [sw library, tcpdump]"); 
  OVcrlf(3);

  // Contended locks
  OVsavex();
    OVfade(OVconst.widewidth, userbase+1234, 0, 0, ""); 
    OVfade(OVconst.tinywidth, syscallbase+123, 0, 0, "");  
    OVfade(OVconst.widewidth, userbase+1234, 0, 0, "");  
  OVrestorex(); 

  OVsavex();
    OVgap(OVconst.narrowwidth); 
    OVevent(OVconst.widewidth, lockbase+1, 123, 0, "");
    OVgap(OVconst.tinywidth);   
    OVevent(OVconst.normalwidth, lockbase+0, 123, 0, "");  
  OVrestorex(); 

  OVtab(4); OVtext(0, "Contended lock"); 
    OVtext(1, "  trying; holding"); 
    OVtext(2, "  [sw library]"); 
  OVcrlf(2);
}

function OV4() {
  OVmedtext("Manually-inserted"); OVcrlf(3);

  // Callouts
  OVcrlf(2);
  OVsavex();
    OVfade(OVconst.widewidth, userbase+1234, 0, 0, ""); 
    OVfade(OVconst.normalwidth, syscallbase+123, 0, 0, "");  
    OVfade(OVconst.normalwidth, userbase+1234, 0, 0, "");  
  OVrestorex(); 

  OVsavex();
    OVgap(OVconst.widewidth); 
    OVevent(-10, calloutbase, 0, 0, "Hi");  
    OVgap(OVconst.normalwidth); 
    OVevent(10, calloutbase, 0, 0, "there");  
  OVrestorex(); 

  OVtab(4); OVtext(1, "Callouts"); 
  OVcrlf(2);

  // Markers
  OVsavex();
    OVfade(OVconst.widewidth, userbase+1234, 0, 0, ""); 
    OVfade(OVconst.tinywidth, syscallbase+123, 0, 0, "");  
    OVfade(OVconst.widewidth, userbase+1234, 0, 0, "");  
  OVrestorex(); 

  OVsavex();
    OVgap(OVconst.narrowwidth); 
    OVevent(0, markbase+0, 0, 0, "A");  
    OVgap(OVconst.normalwidth); 
    OVevent(0, markbase+1, 0, 0, "B");  
    OVgap(OVconst.normalwidth); 
    OVevent(0, markbase+2, 0, 0, "C");  
    OVgap(OVconst.normalwidth); 
    OVevent(0, markbase+3, 0, 0, "123");  
  OVrestorex(); 

  OVtab(4); OVtext(1, "Markers a; b; c; d"); 
  OVcrlf(3);

  OVcrlf(1);
  OVgap(OVconst.narrowwidth);
  OVtext(1, "  [User software libraries insert:");
  OVtext(2, "   RPCIDs for CPU work, RPC message");
  OVtext(3, "   packet times, queued RPC work,");
  OVtext(4, "   lock try/acquire/release]");
  OVcrlf(4);
}

function OV5() {
  OVmedtext("Annotations"); OVcrlf(3);

  // Annotations
  OVaxes(6 * OVconst.normalwidth, 8 * OVconst.cr_incr, "");

  OVsavey();
    OVcrlf(2);
    OVevent(OVconst.normalwidth, userbase+1234, 0, 0, ""); 
    OVevent(OVconst.narrowwidth, syscallbase+123, 0, 0, "");  
    OVevent(OVconst.narrowwidth, userbase+1234, 0, 0, ""); 
    OVevent(OVconst.widewidth, userbase+1234, 0, 0, ""); 
    OVevent(OVconst.normalwidth, userbase+1234, 0, 0, ""); 
    OVcrlf(3);
    OVevent(OVconst.widewidth, userbase+1235, 0, 0, ""); 
    OVevent(OVconst.normalwidth, userbase+1235, 0, 0, ""); 
    OVevent(OVconst.normalwidth, syscallbase+124, 0, 0, "");  
    OVevent(OVconst.normalwidth, userbase+1236, 0, 0, ""); 
    OVcrlf(1);
  OVrestorey();

  OVsavey();
    OVcrlf(2);
    OVgap(2); 	// 2px in
    OVevent(OVconst.normalwidth, annotbase, 6 * OVconst.cr_incr, -1 * OVconst.cr_incr, "123.45 myprogram 4.06us ipc=3/4"); 
    OVcrlf(3);
    OVgap(OVconst.widewidth); 
    OVgap(OVconst.normalwidth); 
    OVevent(OVconst.normalwidth, annotbase, 3 * OVconst.cr_incr, -4 * OVconst.cr_incr, "143.67 fstat 8.55us ipc=1/8"); 
  OVrestorey();

  OVcrlf(9);
  OVtext(0, "Search results, shift-click:");
  OVtext(1, "  timestamp name duration IPC");
  OVtext(2, "  line below, dots above");
  OVcrlf(2);
}

function OV6() {
  OVmedtext("Grid"); OVcrlf(3);

  // Grid
  OVsavey();
    OVcrlf(2);
    OVfade(OVconst.normalwidth, userbase+1234, 0, 0, ""); 
    OVfade(OVconst.narrowwidth, syscallbase+123, 0, 0, "");  
    OVfade(OVconst.narrowwidth, userbase+1234, 0, 0, ""); 
    OVfade(OVconst.widewidth, userbase+1234, 0, 0, ""); 
    OVfade(OVconst.normalwidth, userbase+1234, 0, 0, ""); 
    OVcrlf(3);
    OVfade(OVconst.widewidth, userbase+1235, 0, 0, ""); 
    OVfade(OVconst.normalwidth, userbase+1235, 0, 0, ""); 
    OVfade(OVconst.normalwidth, syscallbase+124, 0, 0, "");  
    OVfade(OVconst.normalwidth, userbase+1236, 0, 0, ""); 
    OVcrlf(1);
  OVrestorey();

  OVaxes(6 * OVconst.normalwidth, 8 * OVconst.cr_incr, "");
  OVgap(OVconst.tinywidth); 
  OVline(1 * OVconst.normalwidth, 8 * OVconst.cr_incr, "#C0C0C0", "");
  OVline(3 * OVconst.normalwidth, 8 * OVconst.cr_incr, "#C0C0C0", "");
  OVline(5 * OVconst.normalwidth, 8 * OVconst.cr_incr, "#C0C0C0", "");
  OVcrlf(8);

  OVgap(OVconst.tinywidth); 
  OVline(1 * OVconst.normalwidth, 3, "#000000", "100");
  OVline(3 * OVconst.normalwidth, 3, "#000000", "200");
  OVline(5 * OVconst.normalwidth, 3, "#000000", "300");

  OVcrlf(1);
  OVtext(2, "Light gray X-axis gridline overlay");
  OVcrlf(2);
}

// Mark intended row beginnings
function OV0() {
console.log("OV0");
  OVreset();
  var row, col;
  for (col = 0; col < Math.floor(OVconst.width / OVconst.col_incr); ++col) {
console.log("  col", col);
    OVsetcol(col);
    OVcrlf(5);	// 2.5 rows down
    for (row = 0; row < Math.floor(OVconst.height / (OVconst.cr_incr * 2)); ++row) {
      OVcircle(10); OVcrlf(2);
    }
  }
  OVreset();
}


// This draws a fixed-size 600x900px page of teh KUtrace drawing legend
function DrawOverallLegend() {
  // Remove any existing outersvg and all its contents
  d3.select("svg").remove();
  fade_value = 0;
 
  // Allocate new one
  outersvg = d3.select("span#anchorsvg").append("svg")
    .attr("class", "svgtop")
    .attr("xmlns", "http://www.w3.org/2000/svg")
    .attr("width", OVconst.width)
    .attr("height", OVconst.height);

  // And that SVG has a group to contain all its children
  // We call it innersvgdotg because that is what all the drawing routines modify
  innersvgdotg = outersvg.append("g");
  innersvgdotgbare = innersvgdotg;

  OVdata.xpos = OVconst.width / 2;
  OVdata.ypos = 0;

  initializeDrawingState();

  OVbigtext("KUtrace HTML Legend");
  OVreset();
  // OV0();
}




function DrawOverallLegendH() {
  OVresetH();
  DrawOverallLegend();
  // Column 0
  OVsetcol(0);
  OVcrlf(2);	// one row down
  OV1();
  OV2();

  // Column 1
  OVsetcol(1);
  OVcrlf(2);	// one row down
  OV3();
  OV4();

  // Column 2
  OVsetcol(2);
  OVcrlf(2);	// one row down
  OV5();
  OV6();
}

function DrawOverallLegendV() {
  OVresetV();
  DrawOverallLegend();
  // Column 0
  OVsetcol(0);
  OVcrlf(2);	// one row down
  OV1();
  OV2();
  OV5();

  // Column 1
  OVsetcol(1);
  OVcrlf(2);	// one row down
  OV3();
  OV4();
  OV6();
}

// ---------- End OVerall HTML legend ----------------------------------------


// Re-allocate outer SVG as function of browser window, and 
// redraw everything inside as function of state, data
function allocOuterSvgEtc() {
console.log("allocOuterSvgEtc");
//console.log("3 state2.savedtransformY", state2.savedtransformY);

  // Detour out to the overall legend (horizontal) if set 
  if (legend_value == 2)  {
    DrawOverallLegendH();
    redrawUI();
    return;
  }
  // Detour out to the overall legend (vertical) if set 
  if (legend_value == 1) {
    DrawOverallLegendV();
    redrawUI();
    return;
  }

  allocOuterSvg();
  fillOuterSvg();
  redrawUI();
		
  // Renumber the track heights per row, to auto-prune rows no longer visible
  // No update of y.domain here -- not created yet
  updateTracks();
  setInitialRangeDomain();

  // xnice=10 gives 15 or 16 max gridlines at 5 chars per gridline number e.g. 123.5 
  // If region3 width is < chars_width * textsize.label, set tickalternating = true
  tickalternating = (state2.rangeX < (5 * 0.5 * textsize.xnice * 1.5 * textsize.smalltick));

  // redrawData does redrawIpc, redrawMarks. redrawLast.
  redrawEventsEtc();

  // Axes depend on calculateBasetime in redrawEventsEtc
  draw_initial_axes();

  draw_initial_pan_zoom();
//console.log("5 state2.savedtransformY", state2.savedtransformY);

  draw_callout_mouse();
}

// Start over with new data but old window
function newdataEtc(mydata) {
  newdata2(mydata);
  allocOuterSvgEtc();
}

// Start over with new window size but old data, if any
function resizeWindowEtc() {
  allocOuterSvgEtc();
}

//----------------------------------------------------------------------------//
// Initialization function definition                                         //
//----------------------------------------------------------------------------//

function initAll() {
  // Initialize button values and other state
  initializeState();

  // Set listeners for buttons
  document.getElementById("annottype").onclick = doToggleAnnottype;
  document.getElementById("annotateuser").onclick = doToggleAnnotateuser;
  document.getElementById("annotateall").onclick = doToggleAnnotateall;

  document.getElementById("showmarks").onclick = doToggleMarks;
  document.getElementById("showarcs").onclick = doToggleArcs;
  document.getElementById("showlock").onclick = doToggleLock;
  document.getElementById("showfreq").onclick = doToggleFreq;

  document.getElementById("showipc").onclick = doToggleIpc;
  document.getElementById("showsamp").onclick = doToggleSamp;
  document.getElementById("showcb").onclick = doToggleCb;

  document.getElementById("searchnot").onclick = doToggleSearchnot;
  document.getElementById("minmax_units").onclick = doToggleUnits;

  // Secondary menu
  document.getElementById("legend").onclick = doToggleLegend;
  document.getElementById("fade").onclick = doToggleFade;
  document.getElementById("callout").onclick = doAddCallout;
  document.getElementById("export").onclick = doToggleExport;

  // Set listener for windowsize
  window.addEventListener("resize", resizeWindowEtc);
  // Load initial data, if any
  if (typeof(myString) !== 'undefined') {
    var data2 = JSON.parse(myString);
    newdata2(data2);
  }

  // Use the initial window size to calculate svg size
  resizeWindowEtc();

  // Import from localStorage
  // If there is an Item saved for this document, use it to initialize savedview[0],
  // so the user can then use the back arrow to go it. 
  // We do not automatically go to it it because it might be old and bogus, or 
  // the user might not want that view.
  var tempstr = localStorage.getItem(localStorageKey);
  if (tempstr) {
    data.savedview[0][1] = tempstr;
    decorateSave(document.getElementById("view0"),true);
    redrawUI();
  }
}

// Notes on export of views:
// There is not an easy way to put a view back into an HTML file so that when the file
// is next loaded, the saved views are intact. As a workaround, the secondary menu
// export button copies callouts and save4d views to the clipboard, leaving it up
// to the user to paste this into the end of a json file and then runmakeself.
//
// savedview[n][0] is the name of the saved view, a string.
// The canonical form of savedview[n][1] is the saved view as returned by JSON.stringify,
// a string. But when we export this as text for insertion into a foo.json file,
// it goes out as pure JSON, not a string. We fix that at import. 
//
// There is a separate mechanism to save the current state to localStorage at exit,
// and to optionally reload it at file open on the same machine. For this mechanism,
// the localStorage key is a random number inserted by eventtospan3 as
//   data.randomid : 1112321071 
// or if that is not available in an older file, the tracebase data/time string
//   data.tracebase : "2020-09-19_13:19:00",
//

// Export to localStorage
// Put current state into savedstate[0] and export it to localStorage
// Called when we are closing this document
function unloadSaveState() {
  data.savedview[0][1] = packupState();
  decorateSave(document.getElementById("view0"),true);

// Test, allowed getting out without save
  if (true || state2.basetime0_value == 0) {
    localStorage.setItem(localStorageKey, data.savedview[0][1]);
  }
}

// List of UI active areas
// Text input
//  File: Search: usec: lo/hi
// Buttons
//  CB 	color blind colors 	off/on
//  IPC instructions per cycle	off/both/user/kernel
//  Marks                       off/both/txt/number
//  Annotate annotate all	off/on
//  User-mode annotate user	off/on
//  !	invert search		off/on
//  || 1 2 3 4 5		shift-click:save, click:restore
//  [more]			off/on
//  Red dot resets display	
// Active but no indication
//  Title	text size	1.0/1.25/1.50/0.75
//  Y-axis group triangles	collapse/expand/gray=one-fourth/gray=one-twentyith
//  X-axis labels (and body)	pan/zoon
//  Y-axis labels		pan/zoom
//  Y-axis labels		shift-click:normal/highlight
//  Basetime date		normal/relative to row start

//============================================================================//
// End JavaScript. Body follows                                               //
//============================================================================//
</script>
</head>
 

<body onload="initAll()" onunload="unloadSaveState()" class="noselect">

<!-- Primary menu -->
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<span id="annottype" style="font-weight:bold; color:blue">Annot:</span>
<button id="annotateuser"  style="border-radius:6px";>User</button>
<button id="annotateall"  style="border-radius:6px";>All</button>

&nbsp;&nbsp;&nbsp;Option:
<button id="showmarks"  style="border-radius:6px";>Mark</button>
<button id="showarcs"  style="border-radius:6px";>Arc</button>
<button id="showlock"  style="border-radius:6px";>Lock</button>
<button id="showfreq"  style="border-radius:6px";>Freq</button>

<button id="showipc"  style="border-radius:6px";>IPC</button>
<button id="showsamp"  style="border-radius:6px";>Samp</button>
<button id="showcb"  style="border-radius:6px";>CB</button>

&nbsp;&nbsp;&nbsp;Search: 
<button id="searchnot"; 
 style="background-color:#FFFFFF; color:#808080; borderColor:#C0C0C0; border-radius:6px";
 >!</button>

  <span id="SearchTable" style="position:relative; style="width: 150px;">
    <input name="SearchText" type="text" maxlength="100" id="SearchText" class="Searchtext" 
     onkeyup="keyupSearch(event)" style="width: 150px; background-color:#FFFFFF"/> 
  </span>

<span id="minmax_units" style="font-weight:bold; color:blue">usec:</span>

<input name="SearchMin" type="text" maxlength="20" id="SearchMin" class="Searchmin" onkeyup="keyupSearchMin(event)" style="width: 30px; background-color:#FFFFFF"/>
..
<input name="SearchMax" type="text" maxlength="20" id="SearchMax" class="Searchmax" onkeyup="keyupSearchMax(event)" style="width: 30px; background-color:#FFFFFF"/>

<span id="matchcount" style="font-family: sans-serif; font-size: 10pt"></span>
<span id="debugout" style="font-family: sans-serif; font-size: 10pt"></span>

<br>
<!--End Primary menu -->


<!-- All the drawing is done in an SVG element allocated inside here -->
<span id="anchorsvg"></span>


<!--  
  // Six save/restore clickable items 
  // 21d0 left double arrow
  // 2b05 black left arrow
  // Up down double arrow U+21d5
  // Dingbat circled sans-serif digit one..five U+2780..2785
 -->
<!-- left arrow --
<span style="font-family:sans-serif; font-size:20px; color:blue" id="save0text" class="noselect">&#x21d0;</span>
<!-- circle-one --
<span style="font-family:sans-serif; font-size:20px; color:blue" id="save1text" class="noselect">&#x2780;</span>
<!-- circle-two --
<span style="font-family:sans-serif; font-size:20px; color:blue" id="save2text" class="noselect">&#x2781;</span>
<!-- circle-three --
<span style="font-family:sans-serif; font-size:20px; color:blue" id="save3text" class="noselect">&#x2782;</span>
<!-- circle-four --
<span style="font-family:sans-serif; font-size:20px; color:blue" id="save4text" class="noselect">&#x2783;</span>
<!-- circle-plus -->
<span style="font-family:sans-serif; font-size:24px; color:blue" id="saveplus" class="noselect">&#x2295;</span>
<!-- lock -->
<!-- <span style="font-family:sans-serif; font-size:20px; color:blue" id="savelocked" class="noselect">&#x1f512;</span> -->

<!-- Small and large UI hints -->
<span style="font-family:sans-serif; font-size:12px; color:black">
  Shift-click 1-4 to save, click to restore. Axes: scroll wheel to zoom, 
  drag to pan. Items: shift-click-unclick to annotate. 
  Shift-click-unshift to keep, shift-drag to measure. 
  Red dot resets. 
  <span style="font-weight: bold; color:blue" id="moretoggle">[more] </span>
  
  <span id="moretext" style=”display:none”>
<pre>
   +------------------------------------------------------------------------+
   |            (0) UI controls (HTML)                                      |
   +------------------------------------------------------------------------+
   |            (1) Title                                                   |
   +----+--------------------------------------------------------------+----+
   |(2) |                                                              |(4) |
   |Y-  |       (3) Main SVG drawing area                              |IPC |
   |axis|                                                              |    |
   |    |                                                              |    |
   +----+--------------------------------------------------------------+----+
   |            (5) X-axis                                                  |
   +----+--------------------------------------------------------------+----+
   |            (6) UI hint text (HTML)                                     |
   +----+--------------------------------------------------------------+----+

   List of UI active areas
   In general, click toggles buttons while shift-click cycles through more choices
   In general, shift-click-unclick to annotate/highlight; shift-click-unshift to 
     keep multiple results onscreen at once

   (0) Text and buttons
     User annotate PID names	 off/on
     All  annotate all items	 off/on

     Mark mark_x events	 	 off/both/text/numbers
     Arc  wakeup arcs		 off/on
     Lock lock-held lines	 off/on
     Slow CPU clock freq	 off/gradient
     IPC  instructions per cycle off/both/user/kernel
     Samp PC samples		 off/on
     CB   color blind colors	 off/on

     Search:    regex string match 
     !	        invert search	 off/on, like grep -v
     usec:      match only event duration in [lo..hi]
  
   (1) Title	text size	1.0/1.25/1.50/0.75 times default
  
   (2) Y-axis group triangles	collapse/expand/gray=one-fourth/gray=one-twentieth
       Y-axis labels		mouse and wheel to pan/zoom vertically
       Y-axis labels		shift-click:normal/highlight
  
   (3) anywhere 		mouse and wheel to pan/zoom horizontally	
     Red dot 			resets display
     Yellow overlay indicates slow CPU clock rate (power saving)
    
   (5) Basetime date		normal/relative to row start
  
   (6) Buttons
     1 2 3 4 5			shift-click:save, click:restore current view
     double-arrow		toggles between last two views
     [more]			this text off/on

International Morse Code (for wait events)
  A    .-    B    -...  Cpu  -.-.  Disk -.. 
  E    .     F    ..-.  G    --.   H    ....
  I    ..    J    .---  tasK -.-   Lock .-..
  Mem  --    Net  -.    O    ---   Pipe .--.
  Q    --.-  R    .-.   Sche ...   Time -   
  U    .. -  V    ...-  W    .--   X    -..-
  Y    -.--  Z    --..
</pre>
  </span>

</span>

<br>

<!-- Secondary menu, normally invisible. Toggle on/off wiith ctrl-click reddot -->
<span id="debugtext" hidden>
&nbsp;&nbsp;&nbsp;
Aspect
<input name="ShowAspect" type="text" maxlength="20" id="ShowAspect" class="ShowAspect"
  onkeyup="keyupAspect(event)" value="0:0" style="width: 30px; background-color:#FFFFFF; text-align:center"/> 
Ychars
<input name="DebugRegion2Chars" type="text" maxlength="10" id="DebugRegion2Chars" class="DebugRegion2Chars" 
  onkeyup="keyupDebugRegion2Chars(event)" style="width: 20px; background-color:#FFFFFF"/> 
Ypx
<input name="DebugRegion2Px" type="text" maxlength="10" id="DebugRegion2Px" class="DebugRegion2Px" 
  onkeyup="keyupDebugRegion2Px(event)" style="width: 20px; background-color:#FFFFFF"/> 
<!--
rows
<input name="DebugRows" type="text" maxlength="10" id="DebugRows" class="DebugRows"   
  onkeyup="keyupDebugRows(event)" style="width: 20px; background-color:#FFFFFF"/> 
-->
txt
<input name="DebugTxt" type="text" maxlength="10" id="DebugTxt" class="DebugTxt" 
  onkeyup="keyupDebugTxt(event)" style="width: 20px; background-color:#FFFFFF"/> 
spn
<input name="DebugSpn" type="text" maxlength="10" id="DebugSpn" class="DebugSps"   
  onkeyup="keyupDebugSpn(event)" style="width: 20px; background-color:#FFFFFF"/> 

&nbsp;
<button id="legend"  style="border-radius:6px";>Legend</button>
<button id="fade"  style="border-radius:6px";>Fade</button>
<button id="callout"  style="border-radius:6px; font-weight: bold; background-color: white; color: black; border-color: rgb(192,192,192); ">&#x1f5e8;</button>
<button id="export"  style="border-radius:6px";>Export</button>
&nbsp;&nbsp;&nbsp;
x:
<span id="xstartwidth" style="font-family: sans-serif; font-size: 10pt"></span>
&nbsp;
y:
<span id="ystartwidth" style="font-family: sans-serif; font-size: 10pt"></span>
&nbsp;
<span id="showversion" style="font-family: sans-serif; font-size: 10pt"></span>

<br>
</span>
<!-- End Secondary menu -->

 
</body> 
</html>

