#include <sys/syscall.h>
#include <unistd.h>
#include <stdio.h>
#include <errno.h>
#include <string.h>

int main() {
    printf("Testing KUTrace syscall 511...\n");
    
    // Test version command (command = 0)
    errno = 0;
    long result = syscall(512, 0, 0);
    printf("syscall(512, 0, 0) returned: %ld\n", result);
    if (result == -1) {
        printf("Error: %s (errno=%d)\n", strerror(errno), errno);
    } else {
        printf("Success! Version: 0x%lx\n", result);
    }
    
    return 0;
}
