#include <linux/module.h>
#define INCLUDE_VERMAGIC
#include <linux/build-salt.h>
#include <linux/vermagic.h>
#include <linux/compiler.h>

BUILD_SALT;

MODULE_INFO(vermagic, VERMAGIC_STRING);
MODULE_INFO(name, KBUILD_MODNAME);

__visible struct module __this_module
__section(".gnu.linkonce.this_module") = {
	.name = KBUILD_MODNAME,
	.init = init_module,
#ifdef CONFIG_MODULE_UNLOAD
	.exit = cleanup_module,
#endif
	.arch = MODULE_ARCH_INIT,
};

#ifdef CONFIG_RETPOLINE
MODULE_INFO(retpoline, "Y");
#endif

static const struct modversion_info ____versions[]
__used __section("__versions") = {
	{ 0xd8302880, "module_layout" },
	{ 0x3cf0f622, "param_ops_long" },
	{ 0xf9a482f9, "msleep" },
	{ 0x18161b5e, "kutrace_global_ops" },
	{ 0xb9876eeb, "kutrace_net_filter" },
	{ 0x999e8297, "vfree" },
	{ 0xd6ee688f, "vmalloc" },
	{ 0x6cbbfc54, "__arch_copy_to_user" },
	{ 0x84391afa, "has_capability" },
	{ 0x12a4e128, "__arch_copy_from_user" },
	{ 0x86332725, "__stack_chk_fail" },
	{ 0x4829a47e, "memcpy" },
	{ 0x8f678b07, "__stack_chk_guard" },
	{ 0x43b0c9c3, "preempt_schedule" },
	{ 0xc5850110, "printk" },
	{ 0xb4bc0d3e, "cpufreq_cpu_get_raw" },
	{ 0xd35cce70, "_raw_spin_unlock_irqrestore" },
	{ 0x7a2af7b4, "cpu_number" },
	{ 0x34db050b, "_raw_spin_lock_irqsave" },
	{ 0x5e2d7875, "cpu_hwcap_keys" },
	{ 0x14b89635, "arm64_const_caps_ready" },
	{ 0x3feea40, "cpumask_next" },
	{ 0x3928efe9, "__per_cpu_offset" },
	{ 0xd8a17f84, "kutrace_traceblock_per_cpu" },
	{ 0xdcb764ad, "memset" },
	{ 0x17de3d5, "nr_cpu_ids" },
	{ 0x5e3240a0, "__cpu_online_mask" },
	{ 0xb456a31f, "kutrace_tracing" },
	{ 0x8344ac91, "kutrace_pid_filter" },
};

MODULE_INFO(depends, "");

