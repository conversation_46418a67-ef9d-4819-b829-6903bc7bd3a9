# Copy one file $1 from directory $2 to directory $3, making subdirectories as needed
# dsites 2021.10.23

## Make the target directory if it does not not alreay exist
if [ ! -d $3 ] 
  then 
    mkdir $3 
fi

# If the file name has no slash, don't make any subdirectory
mypath=${1%/*}
if [ $mypath != $1 ]
  then
    mkdir -p $3/$mypath
fi

# Copy the files
cp $2/$1.original $3/$1.original
cp $2/$1.patched $3/$1.patched
