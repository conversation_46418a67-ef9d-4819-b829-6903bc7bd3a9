#!/bin/bash
# Fix kernel configuration for KUTrace ARM N2 support
# This script enables necessary kernel options and recompiles

KERNEL_DIR="/home/<USER>/linux-5.10.211"

echo "Fixing kernel configuration for KUTrace ARM N2 support..."
echo "Kernel directory: $KERNEL_DIR"

cd "$KERNEL_DIR"

# Check if .config exists
if [ ! -f ".config" ]; then
    echo "No .config found. Creating default configuration..."
    make defconfig
fi

echo ""
echo "Current configuration status:"
echo "=============================="

# Check current configurations
if grep -q "CONFIG_KUTRACE=y" .config; then
    echo "KUTRACE: ENABLED"
else
    echo "KUTRACE: DISABLED (will be enabled)"
fi

if grep -q "CONFIG_MODVERSIONS=y" .config; then
    echo "MODVERSIONS: ENABLED"
else
    echo "MODVERSIONS: DISABLED (will be enabled)"
fi

if grep -q "CONFIG_HZ_PERIODIC=y" .config; then
    echo "HZ_PERIODIC: ENABLED"
else
    echo "HZ_PERIODIC: DISABLED (will be enabled)"
fi

echo ""
echo "Enabling required kernel options..."

# Enable KUTRACE
sed -i 's/# CONFIG_KUTRACE is not set/CONFIG_KUTRACE=y/' .config

# Enable MODVERSIONS
sed -i 's/# CONFIG_MODVERSIONS is not set/CONFIG_MODVERSIONS=y/' .config

# Enable HZ_PERIODIC
sed -i 's/# CONFIG_HZ_PERIODIC is not set/CONFIG_HZ_PERIODIC=y/' .config

# Disable NO_HZ options
sed -i 's/CONFIG_NO_HZ=y/# CONFIG_NO_HZ is not set/' .config
sed -i 's/CONFIG_NO_HZ_IDLE=y/# CONFIG_NO_HZ_IDLE is not set/' .config
sed -i 's/CONFIG_NO_HZ_FULL=y/# CONFIG_NO_HZ_FULL is not set/' .config

# Set HZ to 100
sed -i 's/CONFIG_HZ=[0-9]*/CONFIG_HZ=100/' .config

echo ""
echo "Updated configuration status:"
echo "=============================="

if grep -q "CONFIG_KUTRACE=y" .config; then
    echo "KUTRACE: ENABLED"
else
    echo "KUTRACE: DISABLED"
fi

if grep -q "CONFIG_MODVERSIONS=y" .config; then
    echo "MODVERSIONS: ENABLED"
else
    echo "MODVERSIONS: DISABLED"
fi

if grep -q "CONFIG_HZ_PERIODIC=y" .config; then
    echo "HZ_PERIODIC: ENABLED"
else
    echo "HZ_PERIODIC: DISABLED"
fi

echo ""
echo "Preparing kernel for compilation..."

# Clean and prepare
make clean
make defconfig
make modules_prepare

echo ""
echo "Compiling kernel modules..."

# Compile modules
make modules

echo ""
echo "Installing modules..."

# Install modules
make modules_install

echo ""
echo "Installing kernel..."

# Install kernel
make install

echo ""
echo "Kernel compilation complete!"
echo ""
echo "Next steps:"
echo "1. Reboot the system: reboot"
echo "2. After reboot, compile KUTrace module:"
echo "   cd /home/<USER>/KUtrace/linux/module && make clean && make"
echo "3. Load KUTrace module:"
echo "   sudo insmod kutrace_mod.ko tracemb=20" 