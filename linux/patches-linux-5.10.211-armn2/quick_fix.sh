#!/bin/bash
# Quick fix for KUTrace module version issue
# This script enables MODVERSIONS and recompiles only the module

KERNEL_DIR="/home/<USER>/linux-5.10.211"
MODULE_DIR="/home/<USER>/KUtrace/linux/module"

echo "Quick fix for KUTrace ARM N2 module version issue..."
echo "Kernel directory: $KERNEL_DIR"
echo "Module directory: $MODULE_DIR"

cd "$KERNEL_DIR"

# Enable MODVERSIONS in kernel config
echo "Enabling MODVERSIONS in kernel configuration..."
sed -i 's/# CONFIG_MODVERSIONS is not set/CONFIG_MODVERSIONS=y/' .config

# Enable KUTRACE
echo "Enabling KUTRACE in kernel configuration..."
sed -i 's/# CONFIG_KUTRACE is not set/CONFIG_KUTRACE=y/' .config

# Enable HZ_PERIODIC
echo "Enabling HZ_PERIODIC in kernel configuration..."
sed -i 's/# CONFIG_HZ_PERIODIC is not set/CONFIG_HZ_PERIODIC=y/' .config

# Disable NO_HZ options
echo "Disabling NO_HZ options..."
sed -i 's/CONFIG_NO_HZ=y/# CONFIG_NO_HZ is not set/' .config
sed -i 's/CONFIG_NO_HZ_IDLE=y/# CONFIG_NO_HZ_IDLE is not set/' .config
sed -i 's/CONFIG_NO_HZ_FULL=y/# CONFIG_NO_HZ_FULL is not set/' .config

# Set HZ to 100
echo "Setting HZ to 100..."
sed -i 's/CONFIG_HZ=[0-9]*/CONFIG_HZ=100/' .config

echo ""
echo "Updated configuration:"
echo "======================"
grep -E "CONFIG_(KUTRACE|MODVERSIONS|HZ_PERIODIC|HZ)=" .config

echo ""
echo "Preparing kernel for module compilation..."

# Prepare kernel for external modules
make modules_prepare

echo ""
echo "Compiling KUTrace module..."

# Compile the module
cd "$MODULE_DIR"
make clean
make

echo ""
echo "Checking module version magic..."
if [ -f "kutrace_mod.ko" ]; then
    echo "Module compiled successfully!"
    echo "Module info:"
    modinfo kutrace_mod.ko | grep -E "(version|depends|vermagic)"
    
    echo ""
    echo "Testing module loading..."
    echo "Note: This will fail if kernel is not rebooted, but will show version info"
    insmod kutrace_mod.ko tracemb=20 2>&1 | head -5
    
    echo ""
    echo "Next steps:"
    echo "1. Reboot the system: reboot"
    echo "2. After reboot, load the module:"
    echo "   sudo insmod /home/<USER>/KUtrace/linux/module/kutrace_mod.ko tracemb=20"
    echo "3. Start KUTrace:"
    echo "   cd /home/<USER>/KUtrace && ./kutrace_control"
else
    echo "Module compilation failed!"
    exit 1
fi 