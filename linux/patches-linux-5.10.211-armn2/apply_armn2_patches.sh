#!/bin/bash
# Apply KUTrace patches to ARM N2 Linux kernel
# This script applies the necessary patches to enable KUTrace on ARM Neoverse N2

KERNEL_DIR="/home/<USER>/linux-5.10.211"
PATCH_DIR="/home/<USER>/KUtrace/linux/patches-linux-5.10.211-armn2"

echo "Applying KUTrace patches to ARM N2 kernel..."
echo "Kernel directory: $KERNEL_DIR"
echo "Patch directory: $PATCH_DIR"

# Check if kernel directory exists
if [ ! -d "$KERNEL_DIR" ]; then
    echo "Error: Kernel directory $KERNEL_DIR does not exist"
    exit 1
fi

# Check if patch directory exists
if [ ! -d "$PATCH_DIR" ]; then
    echo "Error: Patch directory $PATCH_DIR does not exist"
    exit 1
fi

cd "$KERNEL_DIR"

# Create backup of original files
echo "Creating backups of original files..."

# Backup kernel files
if [ -f "kernel/Makefile" ]; then
    cp kernel/Makefile kernel/Makefile.backup
fi

if [ -f "kernel/softirq.c" ]; then
    cp kernel/softirq.c kernel/softirq.c.backup
fi

# Create kutrace directory if it doesn't exist
mkdir -p kernel/kutrace

# Apply kernel patches
echo "Applying kernel patches..."

# Apply Makefile patch
if [ -f "$PATCH_DIR/kernel/Makefile.patched" ]; then
    cp "$PATCH_DIR/kernel/Makefile.patched" kernel/Makefile
    echo "Applied kernel/Makefile patch"
fi

# Apply softirq.c patch
if [ -f "$PATCH_DIR/kernel/softirq.c.patched" ]; then
    cp "$PATCH_DIR/kernel/softirq.c.patched" kernel/softirq.c
    echo "Applied kernel/softirq.c patch"
fi

# Apply kutrace files
if [ -f "$PATCH_DIR/kernel/kutrace/Makefile.patched" ]; then
    cp "$PATCH_DIR/kernel/kutrace/Makefile.patched" kernel/kutrace/Makefile
    echo "Applied kernel/kutrace/Makefile patch"
fi

if [ -f "$PATCH_DIR/kernel/kutrace/kutrace.c.patched" ]; then
    cp "$PATCH_DIR/kernel/kutrace/kutrace.c.patched" kernel/kutrace/kutrace.c
    echo "Applied kernel/kutrace/kutrace.c patch"
fi

# Apply include patches
echo "Applying include patches..."

# Create include/linux directory if it doesn't exist
mkdir -p include/linux

if [ -f "$PATCH_DIR/include/linux/kutrace.h.patched" ]; then
    cp "$PATCH_DIR/include/linux/kutrace.h.patched" include/linux/kutrace.h
    echo "Applied include/linux/kutrace.h patch"
fi

# Apply other include patches
find "$PATCH_DIR/include" -name "*.patched" | while read file; do
    target_file=$(echo "$file" | sed 's|.*/include/|include/|' | sed 's|\.patched$||')
    target_dir=$(dirname "$target_file")
    mkdir -p "$target_dir"
    cp "$file" "$target_file"
    echo "Applied $target_file patch"
done

echo "KUTrace patches applied successfully!"
echo ""
echo "Next steps:"
echo "1. Configure the kernel: make menuconfig"
echo "2. Enable KUTRACE option in the menu"
echo "3. Ensure HZ_PERIODIC is enabled and NO_HZ options are disabled"
echo "4. Compile the kernel: make -j$(nproc)"
echo "5. Install the kernel: make modules_install && make install"
echo "6. Reboot the system"
echo ""
echo "After reboot, compile and load the KUTrace module:"
echo "cd /home/<USER>/KUtrace/linux/module && make"
echo "sudo insmod kutrace_mod.ko tracemb=20" 