# Copy patched and original files from one directory to another
# $1 is the source directory, $2 the destination
# dsites 2021.10.23
#
# Produces lines in /tmp/foo.sh like
# ./mycopy.sh include/linux/kutrace.h ../linux-5.10.46p tmpdir
#

cd $1
find . -name "*.patched" |sed 's/^\.\//\.\/mycopy.sh /' |sed "s|\.patched$| $1 $2|" >/tmp/foo.sh
cd -

chmod +x /tmp/foo.sh
echo "/tmp/foo.sh written. Execute it next."
