# KUTrace ARM Neoverse N2 支持

本文档说明如何在ARM Neoverse N2架构上配置和使用KUTrace。

## 概述

KUTrace现在支持ARM Neoverse N2服务器架构，包括以下特性：

- 运行时ARM N2 CPU检测（CPU part 0xD49）
- 动态时间计数器频率检测
- ARM N2特定的性能计数器配置
- L3缓存miss监控支持
- 优化的指令计数和周期计数

## 系统要求

- ARM Neoverse N2处理器（CPU part 0xD49）
- Linux内核 5.10.211
- 支持ARM64的编译工具链

## 安装步骤

### 1. 应用内核补丁

```bash
cd /home/<USER>/KUtrace/linux/patches-linux-5.10.211-armn2
./apply_armn2_patches.sh
```

### 2. 配置内核

```bash
./configure_armn2_kernel.sh
```

在menuconfig中确保以下选项已启用：
- `CONFIG_KUTRACE=y` (Kernel hacking -> KUTrace support)
- `CONFIG_HZ_PERIODIC=y` (Processor type and features -> Timer frequency -> Periodic timer ticks)
- `CONFIG_HZ=100` (或更高)

确保以下选项已禁用：
- `CONFIG_NO_HZ=n`
- `CONFIG_NO_HZ_IDLE=n`
- `CONFIG_NO_HZ_FULL=n`

### 3. 编译内核

```bash
cd /home/<USER>/linux-5.10.211
make -j$(nproc)
make modules_install
make install
```

### 4. 重启系统

```bash
reboot
```

### 5. 编译和加载KUTrace模块

```bash
cd /home/<USER>/KUtrace/linux/module
make
sudo insmod kutrace_mod.ko tracemb=20
```

## 使用方法

### 开始追踪

```bash
cd /home/<USER>/KUtrace/postproc
./kutrace_control
```

在提示符后输入：
- `go` - 开始追踪
- `stop` - 停止追踪并生成原始追踪文件

### 生成分析报告

```bash
./postproc3.sh kutrace.raw
```

这将生成HTML格式的性能分析报告。

## ARM N2特定特性

### 性能计数器

ARM N2支持以下性能事件：
- 指令执行计数 (INST_RETIRED)
- 周期计数 (CYCLES)
- L3缓存miss (L3_CACHE_MISS)
- L3缓存refill (L3_CACHE_REFILL)
- 分支预测miss (BRANCH_MISS)
- DTLB miss (DTLB_MISS)
- ITLB miss (ITLB_MISS)

### 时间计数器

ARM N2使用CNTVCT_EL0寄存器进行高精度时间计数，频率通过CNTFRQ_EL0动态检测。

### CPU检测

系统会自动检测ARM N2处理器并应用相应的优化配置。

## 故障排除

### 模块加载失败

如果模块加载失败，检查：
1. 内核是否已正确编译并安装
2. KUTRACE选项是否已在内核中启用
3. 查看dmesg输出中的错误信息

### 性能计数器问题

如果性能计数器不工作：
1. 确保以root权限运行
2. 检查ARM PMU是否已启用
3. 验证CPU part number是否为0xD49

### 时间计数器问题

如果时间计数器不准确：
1. 检查CNTFRQ_EL0寄存器值
2. 确保HZ_PERIODIC已启用
3. 验证NO_HZ选项已禁用

## 技术细节

### ARM N2检测

```c
static bool detect_arm_n2(void) {
    u64 midr;
    asm volatile("mrs %0, midr_el1" : "=r" (midr));
    u32 part_num = (midr >> 4) & 0xFFF;
    return part_num == 0xD49; // ARM Neoverse N2
}
```

### 时间计数器频率检测

```c
static u64 get_arm_timer_freq(void) {
    u64 freq;
    asm volatile("mrs %0, cntfrq_el0" : "=r" (freq));
    return freq;
}
```

### L3缓存监控

```c
// ARM N2 L3 cache miss event (0x2B)
u64 evtcount = 0x2B;
asm volatile("msr pmevtyper3_el0, %x0" : : "r" (evtcount));
asm volatile("msr pmcntenset_el0, %x0" : : "r" (r|1<<3));
```

## 性能优化建议

1. 使用适当的追踪缓冲区大小（建议20-50MB）
2. 在低负载时进行追踪以获得更准确的结果
3. 避免在追踪期间进行大量I/O操作
4. 考虑使用CPU亲和性来减少上下文切换

## 支持

如果遇到问题，请检查：
1. 内核日志 (`dmesg`)
2. 系统日志 (`journalctl`)
3. KUTrace模块输出 (`dmesg | grep kutrace`)

## 版本信息

- KUTrace版本：支持ARM N2
- 内核版本：5.10.211
- ARM N2支持：CPU part 0xD49
- 最后更新：2024年 