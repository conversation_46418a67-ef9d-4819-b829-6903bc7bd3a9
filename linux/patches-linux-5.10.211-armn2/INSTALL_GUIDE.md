# KUTrace ARM Neoverse N2 完整安装指南

## 概述

本指南将帮助您在ARM Neoverse N2服务器上完整安装和配置KUTrace性能分析工具。

## 系统要求

- ARM Neoverse N2处理器（CPU part 0xD49）
- Linux内核 5.10.211
- 支持ARM64的编译工具链
- 至少20MB可用内存用于追踪缓冲区

## 安装步骤

### 步骤1：验证系统架构

```bash
# 检查CPU架构
uname -m

# 检查CPU型号
cat /proc/cpuinfo | grep "CPU part"

# 应该显示：CPU part : 0xd49
```

### 步骤2：应用内核补丁

```bash
cd /home/<USER>/KUtrace/linux/patches-linux-5.10.211-armn2
./apply_armn2_patches.sh
```

这个脚本会：
- 备份原始内核文件
- 应用KUTrace内核补丁
- 创建必要的目录结构

### 步骤3：配置内核

```bash
cd /home/<USER>/linux-5.10.211
./configure_armn2_kernel.sh
```

在menuconfig中配置以下选项：

**必需启用的选项：**
- `CONFIG_KUTRACE=y` (Kernel hacking -> KUTrace support)
- `CONFIG_HZ_PERIODIC=y` (Processor type and features -> Timer frequency -> Periodic timer ticks)
- `CONFIG_HZ=100` (或更高)

**必需禁用的选项：**
- `CONFIG_NO_HZ=n`
- `CONFIG_NO_HZ_IDLE=n`
- `CONFIG_NO_HZ_FULL=n`

### 步骤4：编译内核

```bash
cd /home/<USER>/linux-5.10.211

# 编译内核（使用所有可用CPU核心）
make -j$(nproc)

# 安装模块
make modules_install

# 安装内核
make install
```

### 步骤5：重启系统

```bash
reboot
```

### 步骤6：编译KUTrace模块

```bash
cd /home/<USER>/KUtrace/linux/module
make clean
make
```

### 步骤7：加载KUTrace模块

```bash
# 加载模块（20MB追踪缓冲区）
sudo insmod kutrace_mod.ko tracemb=20

# 检查模块是否加载成功
dmesg | grep kutrace
```

应该看到类似输出：
```
kutrace_trace hello =====================
IsArmN2_64 detected, timer freq: 100000000 Hz
kutrace_trace All done init successfully!
```

## 使用方法

### 开始性能追踪

```bash
cd /home/<USER>/KUtrace/postproc

# 启动KUTrace控制程序
./kutrace_control
```

在提示符后输入：
- `go` - 开始追踪
- `stop` - 停止追踪并生成原始追踪文件

### 生成性能分析报告

```bash
# 处理原始追踪文件
./postproc3.sh kutrace.raw

# 生成HTML报告
./show_cpu.html
```

## ARM N2特定功能

### 性能计数器

ARM N2支持以下性能事件：
- **指令执行计数** (INST_RETIRED) - 计数器2
- **周期计数** (CYCLES) - 内置计数器
- **L3缓存miss** (L3_CACHE_MISS) - 计数器3
- **L3缓存refill** (L3_CACHE_REFILL) - 计数器4
- **分支预测miss** (BRANCH_MISS) - 计数器5
- **DTLB miss** (DTLB_MISS) - 计数器6
- **ITLB miss** (ITLB_MISS) - 计数器7

### 时间计数器

ARM N2使用CNTVCT_EL0寄存器进行高精度时间计数：
- 频率通过CNTFRQ_EL0动态检测
- 典型频率：100MHz
- 精度：10ns

### 自动CPU检测

系统会自动检测ARM N2处理器并应用优化配置：
- CPU part number: 0xD49
- 自动配置PMU事件
- 动态时间频率检测

## 故障排除

### 模块加载失败

**问题：** `insmod: ERROR: could not insert module kutrace_mod.ko: Invalid parameters`

**解决方案：**
1. 检查内核是否已正确编译并安装
2. 验证KUTRACE选项已在内核中启用
3. 查看详细错误信息：`dmesg | tail -20`

### 性能计数器不工作

**问题：** 性能计数器返回0或错误值

**解决方案：**
1. 确保以root权限运行
2. 检查ARM PMU是否已启用
3. 验证CPU part number：`cat /proc/cpuinfo | grep "CPU part"`

### 时间计数器不准确

**问题：** 时间戳不准确或跳跃

**解决方案：**
1. 检查CNTFRQ_EL0寄存器值
2. 确保HZ_PERIODIC已启用
3. 验证NO_HZ选项已禁用

### 编译错误

**问题：** 模块编译失败

**解决方案：**
1. 确保内核已正确配置：`make defconfig && make prepare`
2. 检查内核头文件是否存在
3. 验证编译工具链版本

## 性能优化建议

### 追踪缓冲区大小

- **小系统** (< 32核心): 10-20MB
- **中等系统** (32-128核心): 20-50MB
- **大系统** (> 128核心): 50-100MB

### 追踪时机

- 在系统负载较低时进行追踪
- 避免在追踪期间进行大量I/O操作
- 考虑使用CPU亲和性减少上下文切换

### 分析重点

- **IPC分析**: 关注指令每周期比率
- **缓存分析**: 监控L3缓存miss率
- **内存分析**: 观察内存访问模式
- **调度分析**: 检查进程切换频率

## 高级配置

### 自定义性能事件

可以修改模块源码来监控其他ARM N2性能事件：

```c
// 在kutrace_mod.c中添加新事件
#define ARM_N2_EVENT_CUSTOM 0xXX
```

### 网络追踪

启用网络包追踪：

```bash
# 加载模块时指定网络过滤参数
sudo insmod kutrace_mod.ko tracemb=20 pktmask=0xFFFFFF pktmatch=0x123456
```

### 进程过滤

只追踪特定进程：

```bash
# 在kutrace_control中设置PID过滤
./kutrace_control
> filter 1234  # 只追踪PID 1234
> go
```

## 技术支持

如果遇到问题，请检查：

1. **内核日志**: `dmesg | grep kutrace`
2. **系统日志**: `journalctl -f`
3. **模块信息**: `modinfo kutrace_mod.ko`
4. **系统信息**: `cat /proc/cpuinfo`

## 版本信息

- **KUTrace版本**: 支持ARM N2
- **内核版本**: 5.10.211
- **ARM N2支持**: CPU part 0xD49
- **最后更新**: 2024年8月

## 许可证

KUTrace遵循GPL v2许可证。详见LICENSE文件。 