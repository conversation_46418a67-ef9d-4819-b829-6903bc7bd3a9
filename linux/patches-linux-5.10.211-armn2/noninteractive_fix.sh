#!/bin/bash
# Non-interactive fix for KUTrace ARM N2 module
# This script avoids interactive prompts and focuses on module compilation

KERNEL_DIR="/home/<USER>/linux-5.10.211"
MODULE_DIR="/home/<USER>/KUtrace/linux/module"

echo "Non-interactive fix for KUTrace ARM N2 module..."
echo "Kernel directory: $KERNEL_DIR"
echo "Module directory: $MODULE_DIR"

cd "$KERNEL_DIR"

# Check if .config exists, if not create default
if [ ! -f ".config" ]; then
    echo "Creating default kernel configuration..."
    make defconfig
fi

# Enable required options without triggering interactive prompts
echo "Updating kernel configuration..."

# Use oldconfig to apply changes without prompts
echo "CONFIG_MODVERSIONS=y" >> .config
echo "CONFIG_KUTRACE=y" >> .config
echo "CONFIG_HZ_PERIODIC=y" >> .config
echo "CONFIG_HZ=100" >> .config

# Remove NO_HZ options
sed -i '/CONFIG_NO_HZ=y/d' .config
sed -i '/CONFIG_NO_HZ_IDLE=y/d' .config
sed -i '/CONFIG_NO_HZ_FULL=y/d' .config

# Remove duplicate entries
sort -u .config > .config.tmp && mv .config.tmp .config

echo ""
echo "Updated configuration:"
echo "======================"
grep -E "CONFIG_(KUTRACE|MODVERSIONS|HZ_PERIODIC|HZ)=" .config

echo ""
echo "Preparing kernel for module compilation..."

# Prepare kernel for external modules (non-interactive)
make modules_prepare

echo ""
echo "Compiling KUTrace module..."

# Compile the module
cd "$MODULE_DIR"
make clean
make

echo ""
echo "Checking module version magic..."
if [ -f "kutrace_mod.ko" ]; then
    echo "Module compiled successfully!"
    echo "Module info:"
    modinfo kutrace_mod.ko | grep -E "(version|depends|vermagic)" || echo "modinfo not available"
    
    echo ""
    echo "Module file details:"
    ls -la kutrace_mod.ko
    
    echo ""
    echo "Next steps:"
    echo "1. Reboot the system: reboot"
    echo "2. After reboot, load the module:"
    echo "   sudo insmod /home/<USER>/KUtrace/linux/module/kutrace_mod.ko tracemb=20"
    echo "3. Start KUTrace:"
    echo "   cd /home/<USER>/KUtrace && ./kutrace_control"
else
    echo "Module compilation failed!"
    echo "Checking for compilation errors..."
    ls -la *.o *.ko 2>/dev/null || echo "No object files found"
    exit 1
fi 