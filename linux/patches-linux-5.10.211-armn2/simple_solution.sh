#!/bin/bash
# Simple solution for KUTrace ARM N2 - Skip full kernel compilation
# This script focuses on getting the module working without full kernel rebuild

echo "Simple solution for KUTrace ARM N2 module issue..."
echo "=================================================="

# Step 1: Check current kernel configuration
echo "Step 1: Checking current kernel configuration..."
cd /home/<USER>/linux-5.10.211

if [ ! -f ".config" ]; then
    echo "No .config found. Creating default configuration..."
    make defconfig
fi

# Step 2: Enable required options
echo "Step 2: Enabling required kernel options..."
echo "CONFIG_MODVERSIONS=y" >> .config
echo "CONFIG_KUTRACE=y" >> .config
echo "CONFIG_HZ_PERIODIC=y" >> .config
echo "CONFIG_HZ=100" >> .config

# Remove duplicate entries
sort -u .config > .config.tmp && mv .config.tmp .config

echo "Updated configuration:"
grep -E "CONFIG_(KUTRACE|MODVERSIONS|HZ_PERIODIC|HZ)=" .config

# Step 3: Prepare kernel for external modules
echo ""
echo "Step 3: Preparing kernel for external modules..."
make modules_prepare

# Step 4: Compile KUTrace module
echo ""
echo "Step 4: Compiling KUTrace module..."
cd /home/<USER>/KUtrace/linux/module
make clean
make

# Step 5: Check module
echo ""
echo "Step 5: Checking compiled module..."
if [ -f "kutrace_mod.ko" ]; then
    echo "✅ Module compiled successfully!"
    echo "Module details:"
    ls -la kutrace_mod.ko
    
    echo ""
    echo "Module version info:"
    modinfo kutrace_mod.ko 2>/dev/null | grep -E "(version|vermagic)" || echo "modinfo not available"
    
    echo ""
    echo "Step 6: Testing module loading..."
    echo "Note: This may fail due to version mismatch, but will show the error"
    
    # Try to load module and capture error
    ERROR_OUTPUT=$(insmod kutrace_mod.ko tracemb=20 2>&1)
    if [ $? -eq 0 ]; then
        echo "✅ Module loaded successfully!"
        echo "KUTrace is now ready to use!"
        echo ""
        echo "Next steps:"
        echo "1. Start KUTrace: cd /home/<USER>/KUtrace && ./kutrace_control"
        echo "2. In kutrace_control, type 'go' to start tracing"
        echo "3. Type 'stop' to stop tracing and generate report"
    else
        echo "❌ Module loading failed:"
        echo "$ERROR_OUTPUT"
        echo ""
        echo "Trying alternative loading method..."
        
        # Try with force option
        ERROR_OUTPUT2=$(insmod kutrace_mod.ko tracemb=20 --force 2>&1)
        if [ $? -eq 0 ]; then
            echo "✅ Module loaded successfully with --force!"
            echo "KUTrace is now ready to use!"
        else
            echo "❌ Force loading also failed:"
            echo "$ERROR_OUTPUT2"
            echo ""
            echo "Alternative solutions:"
            echo "1. Reboot the system and try again"
            echo "2. Use 'modprobe kutrace_mod tracemb=20' instead"
            echo "3. Check kernel logs: dmesg | tail -10"
        fi
    fi
else
    echo "❌ Module compilation failed!"
    echo "Checking for compilation errors..."
    ls -la *.o *.ko 2>/dev/null || echo "No object files found"
fi

echo ""
echo "Solution summary:"
echo "================="
echo "If module loaded successfully, KUTrace is ready for ARM N2!"
echo "If not, try rebooting the system and loading again."
echo ""
echo "For troubleshooting, check:"
echo "- Kernel logs: dmesg | grep kutrace"
echo "- Module info: modinfo kutrace_mod.ko"
echo "- System info: uname -a" 