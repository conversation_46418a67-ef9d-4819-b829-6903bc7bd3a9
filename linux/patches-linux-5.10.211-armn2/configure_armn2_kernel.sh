#!/bin/bash
# Configure ARM N2 kernel for KUTrace support
# This script helps configure the kernel with the necessary options

KERNEL_DIR="/home/<USER>/linux-5.10.211"

echo "Configuring ARM N2 kernel for KUTrace support..."
echo "Kernel directory: $KERNEL_DIR"

# Check if kernel directory exists
if [ ! -d "$KERNEL_DIR" ]; then
    echo "Error: Kernel directory $KERNEL_DIR does not exist"
    exit 1
fi

cd "$KERNEL_DIR"

# Check if .config exists
if [ ! -f ".config" ]; then
    echo "No .config found. Creating default ARM64 configuration..."
    make defconfig
fi

echo ""
echo "Current kernel configuration:"
echo "=============================="

# Check current KUTRACE configuration
if grep -q "CONFIG_KUTRACE=y" .config; then
    echo "KUTRACE: ENABLED"
else
    echo "KUTRACE: DISABLED (needs to be enabled)"
fi

# Check HZ configuration
if grep -q "CONFIG_HZ_PERIODIC=y" .config; then
    echo "HZ_PERIODIC: ENABLED"
else
    echo "HZ_PERIODIC: DISABLED (needs to be enabled)"
fi

# Check NO_HZ configuration
if grep -q "CONFIG_NO_HZ=y" .config; then
    echo "NO_HZ: ENABLED (needs to be disabled)"
else
    echo "NO_HZ: DISABLED"
fi

if grep -q "CONFIG_NO_HZ_IDLE=y" .config; then
    echo "NO_HZ_IDLE: ENABLED (needs to be disabled)"
else
    echo "NO_HZ_IDLE: DISABLED"
fi

if grep -q "CONFIG_NO_HZ_FULL=y" .config; then
    echo "NO_HZ_FULL: ENABLED (needs to be disabled)"
else
    echo "NO_HZ_FULL: DISABLED"
fi

echo ""
echo "Required configuration changes:"
echo "==============================="
echo "1. Enable KUTRACE option"
echo "2. Enable HZ_PERIODIC"
echo "3. Disable all NO_HZ options"
echo "4. Set HZ to 100 or higher"
echo ""

echo "Opening kernel configuration menu..."
echo "Please make the following changes:"
echo ""
echo "1. Navigate to 'Kernel hacking' -> 'KUTrace support' and enable it"
echo "2. Navigate to 'Processor type and features' -> 'Timer frequency' and set to 100 Hz"
echo "3. Navigate to 'Processor type and features' -> 'Timer frequency' -> 'Periodic timer ticks' and enable it"
echo "4. Navigate to 'Processor type and features' -> 'Timer frequency' and disable all 'Tickless System' options"
echo ""

read -p "Press Enter to open menuconfig..."

make menuconfig

echo ""
echo "Configuration complete!"
echo ""
echo "To apply the configuration and compile:"
echo "1. make -j$(nproc)"
echo "2. make modules_install"
echo "3. make install"
echo "4. reboot"
echo ""
echo "After reboot, load KUTrace module:"
echo "cd /home/<USER>/KUtrace/linux/module && make"
echo "sudo insmod kutrace_mod.ko tracemb=20" 