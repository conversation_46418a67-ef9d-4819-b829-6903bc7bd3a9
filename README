KUtrace is an extremely low-overhead Linux kernel tracing facility for observing all 
the execution time on all cores of a multi-core processor, nothing missing, while running 
completely unmodified user programs written in any computer language. It has been used in 
live datacenters (x86 processors) and in real-time autonomous driving (ARM processors) 
to understand long-standing performance mysteries. The design goal of KUtrace is to 
reveal the root cause(s) of unexpected delayed responses in real-time transactions or 
database processing while having such low overhead that it does not distort the system 
under test.

This March 2022 update COMPLETELY REPLACES the previous 2019 repository.

The directory contains files associated with the 2022 book 
Understanding Software Dynamics by <PERSON>, ISBN 978-0137589739.

The book-figures directory contains the underlying HTML files for over 100 of
the diagrams in the book, so you can wander around them to see more context or
to discover other performance issues. They work best in the Chrome browser.

The kutrace_user_guide.pdf file gives more detail and is more recent than the 
text in Chapter 19 of the book. Read it to fully use the HTML files.

The hello_world_trace.html file is an example trace of the hello_world_trace.c 
program found in book-user-code.

The book-user-code directory contains all the programs used in the book and the
script to compile them. They compile and run on both x86 and Rpi4 machines.

The linux_patches_installation_guide.pdf gives brief directions for building
the patches.

The loadable-module directory contains the source and Makefile for the companion
kernel module that implements the bulk of KUtrace. It builds for AMD and Intel 
x86 machines and for the Raspberry Pi4-B.

The patches-* files contain original source and patched source pairs of files
for adding KUtrace to three different versions of the Linux operating system.
These files are structured in the matching directories of the original Linux 
source download. The patches-linux-5.10.46-rpi4 folder contains additional 
documentation and useful scripts for building a patched kernel for any of
these three versions or for incorporating the patches into other versions. Only
64-bit versions of Linux are supported in this code; 32-bit ports of KUtrace 
are possible but difficult.

All the code is open sourced, most of it under the BSD three-clause license. The code specific to freebsd uses the BSD2 license, and the code specific to risc-v is licensed under GPL-2.0. The Linux loadable module(s) are required by Linux to be licensed under GPL-2.0. 



